# NEXUS SaaS Starter - SDK Generation Implementation PRP

**PRP Name**: SDK Generation & Multi-Language Client Libraries  
**Version**: 1.0  
**Date**: January 2025  
**Type**: Enterprise Feature Implementation PRP  
**Framework**: Next.js 15.4+ / React 19 / TypeScript 5.8+  

---

## Purpose

Implement a comprehensive SDK generation system that creates type-safe, multi-language client libraries for the NEXUS SaaS platform, enabling developers to integrate seamlessly across TypeScript, Python, Go, PHP, Java, and C# with full multi-tenant support and enterprise-grade developer experience.

## Core Principles

1. **Type Safety First**: Generate fully-typed client libraries with compile-time validation
2. **Multi-Tenant Aware**: SDKs automatically handle workspace context and tenant isolation
3. **Developer Experience**: Auto-completion, comprehensive docs, examples, and testing utilities
4. **Production Ready**: Enterprise-grade error handling, retry logic, and monitoring
5. **Language Agnostic**: Consistent API patterns across all supported programming languages
6. **Real-Time Support**: WebSocket client generation for real-time features

---

## Goal

Build a production-ready SDK generation system that automatically creates and maintains client libraries for multiple programming languages, enabling developers to integrate with the NEXUS SaaS platform with minimal setup and maximum type safety.

## Why

- **Developer Adoption**: Reduce integration time from days to hours with ready-to-use SDKs
- **Type Safety**: Eliminate runtime errors with compile-time validation across all languages
- **Multi-Tenant Security**: Ensure proper workspace isolation in all client interactions
- **Consistency**: Provide uniform API patterns across different programming ecosystems
- **Maintenance**: Automatically update SDKs when API changes occur
- **Enterprise Ready**: Support enterprise requirements for security, monitoring, and compliance

## What

A comprehensive SDK generation system featuring:
- Multi-language client library generation (TypeScript, Python, Go, PHP, Java, C#)
- Type-safe API clients with full IntelliSense support
- Multi-tenant workspace context handling
- Authentication integration (API keys, Bearer tokens, session cookies)
- Real-time WebSocket client support
- Comprehensive error handling and retry mechanisms
- Auto-generated documentation and examples
- CI/CD integration for automatic updates

### Success Criteria

- [ ] TypeScript SDK with 100% type coverage and React hooks
- [ ] Python SDK with type hints and async/await support
- [ ] Go SDK with proper error handling and context support
- [ ] PHP SDK with PSR-4 autoloading and modern PHP features
- [ ] Java SDK with Maven/Gradle support and proper exception handling
- [ ] C# SDK with NuGet packaging and async/await patterns
- [ ] WebSocket clients for real-time features across all languages
- [ ] Comprehensive test suites for all generated SDKs (>95% coverage)
- [ ] Auto-generated documentation with interactive examples
- [ ] CI/CD pipeline for automatic SDK updates and publishing

---

## All Needed Context

### Documentation & References

```yaml
# Context7-Verified Patterns (CRITICAL)
openapi_generator_patterns:
  - url: /openapi-tools/openapi-generator
    why: "Multi-language SDK generation from OpenAPI specifications"
    critical: "Gradle build tasks, CLI commands, language-specific configurations"
    patterns: ["buildGoClient", "buildKotlinClient", "Java SDK generation", "C# client libraries"]

graphql_code_generator_patterns:
  - url: /dotansimha/graphql-code-generator
    why: "Type-safe client generation with React hooks and TypeScript support"
    critical: "SDK generation patterns, TypeScript integration, React hooks generation"
    patterns: ["getSdk function", "typescript-react-apollo", "client-preset", "typed operations"]

# Additional Context7 References
better_auth_integration:
  - url: /better-auth/better-auth
    why: "Authentication patterns for SDK integration"
    critical: "Multi-tenant auth, API key management, session handling"

next_js_patterns:
  - url: /vercel/next.js
    why: "Next.js 15.4+ API route patterns and middleware"
    critical: "Route handlers, middleware, server components"

# Current Codebase References
api_management_prp:
  - file: PRPs/features/02-core/api-management-documentation-implementation.md
    why: "Existing API documentation and basic SDK patterns"
    critical: "OpenAPI specification generation, client SDK foundation"

authentication_patterns:
  - file: PRPs/features/01-foundation/authentication-api-documentation-implementation.md
    why: "Authentication API patterns and multi-tenant support"
    critical: "Better-Auth integration, workspace context, API key management"

integration_monitoring:
  - file: PRPs/features/02-core/integration-monitoring-implementation.md
    why: "Monitoring and observability patterns for SDK usage"
    critical: "Health checks, metrics collection, error tracking"
```

### Current Codebase Patterns

```typescript
// From API Management PRP - Basic SDK generation foundation
export class SDKGenerator {
  async generateTypeScriptClient(openApiSpec: any): Promise<string> {
    // Basic TypeScript client generation
    const clientCode = `
export interface ApiClientConfig {
  baseUrl: string;
  apiKey?: string;
  timeout?: number;
}

export class NexusSaasApiClient {
  private config: ApiClientConfig;
  
  constructor(config: ApiClientConfig) {
    this.config = { timeout: 10000, ...config };
  }
  
  private async request<T>(method: string, path: string, data?: any): Promise<T> {
    // Basic request implementation
  }
}`;
    return clientCode;
  }
}

// From Authentication PRP - Multi-tenant patterns
interface WorkspaceContext {
  workspaceId: string;
  userId: string;
  permissions: string[];
}

// From Integration Monitoring PRP - Health check patterns
interface HealthCheck {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  lastCheck: Date;
  responseTime: number;
}
```

### Known Gotchas & Context7 Insights

```yaml
# OpenAPI Generator Gotchas (Context7 Verified)
openapi_generator_gotchas:
  - "Use reproducible builds with --enable-post-process-file for consistent output"
  - "Configure proper error handling with custom exception classes"
  - "Set up proper authentication methods (Bearer, Cookie, API Key) in templates"
  - "Use markdown tables for property documentation with Name|Type|Description|Notes format"
  - "Generate selective components (models, APIs, tests, docs) to avoid bloat"

# GraphQL Code Generator Gotchas (Context7 Verified)
graphql_codegen_gotchas:
  - "Use client-preset for optimal TypeScript integration with Next.js"
  - "Configure proper schema introspection for remote GraphQL endpoints"
  - "Set up proper plugin chains: typescript -> typescript-operations -> typescript-react-apollo"
  - "Use getSdk pattern for fully-typed GraphQL client generation"
  - "Configure proper document parsing for .graphql files"

# Multi-Tenant SDK Gotchas
multi_tenant_gotchas:
  - "Always include workspace context in SDK initialization"
  - "Implement proper tenant isolation in all API calls"
  - "Handle workspace switching in long-lived SDK instances"
  - "Validate workspace permissions before API calls"
  - "Implement proper error handling for tenant-related errors"

# Language-Specific Gotchas
language_specific_gotchas:
  typescript:
    - "Use strict TypeScript configuration for maximum type safety"
    - "Generate React hooks for seamless frontend integration"
    - "Support both CommonJS and ESM module formats"
  python:
    - "Use type hints and dataclasses for modern Python patterns"
    - "Support both sync and async client patterns"
    - "Implement proper exception hierarchy"
  go:
    - "Use context.Context for proper cancellation and timeouts"
    - "Implement proper error wrapping with errors.Is/As"
    - "Support both struct and interface patterns"
  php:
    - "Use PSR-4 autoloading and modern PHP 8+ features"
    - "Implement proper exception handling with custom exception classes"
    - "Support both synchronous and asynchronous patterns with ReactPHP"
  java:
    - "Use proper Maven/Gradle dependency management"
    - "Implement builder patterns for complex configurations"
    - "Support both blocking and non-blocking HTTP clients"
  csharp:
    - "Use async/await patterns throughout"
    - "Implement proper IDisposable patterns for HTTP clients"
    - "Support both .NET Framework and .NET Core/5+"
```

---

## Implementation Blueprint

### Database Schema Extensions

```sql
-- SDK Generation and Management
CREATE TABLE sdk_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  language VARCHAR(50) NOT NULL,
  version VARCHAR(50) NOT NULL,
  api_version VARCHAR(50) NOT NULL,
  generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  download_url TEXT,
  documentation_url TEXT,
  changelog TEXT,
  is_stable BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE sdk_downloads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sdk_version_id UUID REFERENCES sdk_versions(id) ON DELETE CASCADE,
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  download_count INTEGER DEFAULT 1,
  last_downloaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_agent TEXT,
  ip_address INET,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE sdk_usage_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  sdk_language VARCHAR(50) NOT NULL,
  sdk_version VARCHAR(50) NOT NULL,
  api_calls_count INTEGER DEFAULT 0,
  error_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  features_used JSONB DEFAULT '{}',
  performance_metrics JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE sdk_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  sdk_language VARCHAR(50) NOT NULL,
  sdk_version VARCHAR(50) NOT NULL,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  feedback_text TEXT,
  issue_type VARCHAR(100),
  is_resolved BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_sdk_versions_language_version ON sdk_versions(language, version);
CREATE INDEX idx_sdk_downloads_workspace_language ON sdk_downloads(workspace_id, sdk_version_id);
CREATE INDEX idx_sdk_usage_metrics_workspace_language ON sdk_usage_metrics(workspace_id, sdk_language);
CREATE INDEX idx_sdk_feedback_workspace_language ON sdk_feedback(workspace_id, sdk_language);
```

---

## 🔧 Technology Stack

### Core SDK Generation
- **OpenAPI Generator**: Multi-language client generation
- **GraphQL Code Generator**: Type-safe GraphQL clients
- **TypeScript**: Primary SDK language with full type safety
- **Zod**: Runtime validation and type inference

### Build & Distribution
- **Rollup**: Bundle optimization and tree-shaking
- **Vite**: Development and build tooling
- **npm/yarn/pnpm**: Package management and distribution
- **GitHub Actions**: Automated SDK generation and publishing

### Testing & Quality
- **Vitest**: Unit and integration testing
- **Playwright**: End-to-end SDK testing
- **ESLint**: Code quality and consistency
- **Prettier**: Code formatting

### Documentation
- **TypeDoc**: API documentation generation
- **Storybook**: Interactive SDK examples
- **Docusaurus**: Developer portal integration

## 🏗️ Implementation Details

### 1. Core SDK Generator (`lib/sdk-generator/`)

#### Multi-Language Generator (`lib/sdk-generator/core/generator.ts`)
```typescript
import { OpenAPIV3 } from 'openapi-types';
import { CodegenConfig } from '@graphql-codegen/cli';
import { z } from 'zod';

export interface SDKGeneratorConfig {
  openApiSpec: OpenAPIV3.Document;
  graphqlSchema?: string;
  outputDir: string;
  languages: SDKLanguage[];
  tenantContext: boolean;
  authIntegration: boolean;
  realtimeSupport: boolean;
}

export interface SDKLanguage {
  name: 'typescript' | 'javascript' | 'python' | 'go' | 'java' | 'csharp' | 'php' | 'ruby';
  version: string;
  template: string;
  packageName: string;
  outputPath: string;
}

export class SDKGenerator {
  private config: SDKGeneratorConfig;
  private openApiGenerator: OpenAPIGenerator;
  private graphqlGenerator: GraphQLCodeGenerator;

  constructor(config: SDKGeneratorConfig) {
    this.config = config;
    this.openApiGenerator = new OpenAPIGenerator(config);
    this.graphqlGenerator = new GraphQLCodeGenerator(config);
  }

  async generateAll(): Promise<GenerationResult[]> {
    const results: GenerationResult[] = [];
    
    for (const language of this.config.languages) {
      try {
        const result = await this.generateForLanguage(language);
        results.push(result);
      } catch (error) {
        results.push({
          language: language.name,
          success: false,
          error: error.message,
          artifacts: []
        });
      }
    }

    return results;
  }

  private async generateForLanguage(language: SDKLanguage): Promise<GenerationResult> {
    // Generate REST API client
    const restClient = await this.openApiGenerator.generate({
      language: language.name,
      template: language.template,
      outputPath: language.outputPath,
      packageName: language.packageName,
      additionalProperties: {
        tenantContext: this.config.tenantContext,
        authIntegration: this.config.authIntegration,
        supportsAsync: true,
        modelPropertyNaming: 'camelCase'
      }
    });

    // Generate GraphQL client if schema provided
    let graphqlClient = null;
    if (this.config.graphqlSchema) {
      graphqlClient = await this.graphqlGenerator.generate({
        language: language.name,
        schema: this.config.graphqlSchema,
        outputPath: `${language.outputPath}/graphql`,
        config: this.getGraphQLConfig(language)
      });
    }

    // Generate WebSocket client for real-time features
    const websocketClient = this.config.realtimeSupport 
      ? await this.generateWebSocketClient(language)
      : null;

    return {
      language: language.name,
      success: true,
      artifacts: [
        restClient,
        graphqlClient,
        websocketClient
      ].filter(Boolean),
      metadata: {
        packageName: language.packageName,
        version: await this.getSDKVersion(),
        generatedAt: new Date().toISOString(),
        features: this.getEnabledFeatures()
      }
    };
  }

  private getGraphQLConfig(language: SDKLanguage): CodegenConfig {
    const baseConfig = {
      schema: this.config.graphqlSchema,
      generates: {}
    };

    switch (language.name) {
      case 'typescript':
        return {
          ...baseConfig,
          generates: {
            [`${language.outputPath}/graphql/types.ts`]: {
              plugins: ['typescript', 'typescript-operations'],
              config: {
                scalars: {
                  DateTime: 'Date',
                  JSON: 'Record<string, any>'
                },
                enumsAsTypes: true,
                futureProofEnums: true
              }
            },
            [`${language.outputPath}/graphql/sdk.ts`]: {
              plugins: ['typescript-graphql-request'],
              config: {
                rawRequest: false,
                inlineFragmentTypes: 'combine'
              }
            }
          }
        };
      
      case 'javascript':
        return {
          ...baseConfig,
          generates: {
            [`${language.outputPath}/graphql/index.js`]: {
              plugins: ['graphql-codegen-javascript'],
              config: {
                gqlImport: 'graphql-request#gql'
              }
            }
          }
        };
      
      default:
        throw new Error(`GraphQL generation not supported for ${language.name}`);
    }
  }

  private async generateWebSocketClient(language: SDKLanguage): Promise<Artifact> {
    // Generate WebSocket client based on language
    const template = await this.loadWebSocketTemplate(language.name);
    const client = await this.renderTemplate(template, {
      packageName: language.packageName,
      tenantContext: this.config.tenantContext,
      authIntegration: this.config.authIntegration
    });

    return {
      type: 'websocket-client',
      path: `${language.outputPath}/websocket`,
      content: client,
      language: language.name
    };
  }
}
```

#### OpenAPI Generator Integration (`lib/sdk-generator/openapi/generator.ts`)
```typescript
import { exec } from 'child_process';
import { promisify } from 'util';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

export class OpenAPIGenerator {
  private config: SDKGeneratorConfig;
  private templateDir: string;

  constructor(config: SDKGeneratorConfig) {
    this.config = config;
    this.templateDir = path.join(__dirname, '../templates');
  }

  async generate(options: OpenAPIGenerationOptions): Promise<Artifact> {
    // Prepare custom templates for multi-tenant support
    await this.prepareCustomTemplates(options.language);
    
    // Generate temporary OpenAPI spec with tenant context
    const specPath = await this.prepareOpenAPISpec(options);
    
    // Execute OpenAPI Generator
    const command = this.buildGeneratorCommand(options, specPath);
    const { stdout, stderr } = await execAsync(command);
    
    if (stderr && !stderr.includes('WARN')) {
      throw new Error(`OpenAPI Generator failed: ${stderr}`);
    }

    // Post-process generated code
    await this.postProcessGenerated(options);

    return {
      type: 'rest-client',
      path: options.outputPath,
      language: options.language,
      metadata: {
        generator: 'openapi-generator',
        version: await this.getGeneratorVersion(),
        command: command
      }
    };
  }

  private buildGeneratorCommand(options: OpenAPIGenerationOptions, specPath: string): string {
    const baseCommand = [
      'npx @openapitools/openapi-generator-cli generate',
      `-g ${this.getGeneratorName(options.language)}`,
      `-i ${specPath}`,
      `-o ${options.outputPath}`,
      `--package-name ${options.packageName}`,
      '--skip-validate-spec',
      '--enable-post-process-file'
    ];

    // Add language-specific options
    const additionalProps = this.getAdditionalProperties(options);
    if (additionalProps.length > 0) {
      baseCommand.push(`--additional-properties=${additionalProps.join(',')}`);
    }

    // Add custom templates
    const templatePath = path.join(this.templateDir, options.language);
    baseCommand.push(`--template-dir ${templatePath}`);

    return baseCommand.join(' ');
  }

  private getGeneratorName(language: string): string {
    const generators = {
      typescript: 'typescript-fetch',
      javascript: 'javascript',
      python: 'python',
      go: 'go',
      java: 'java',
      csharp: 'csharp',
      php: 'php',
      ruby: 'ruby'
    };

    return generators[language] || 'typescript-fetch';
  }

  private getAdditionalProperties(options: OpenAPIGenerationOptions): string[] {
    const props = [];
    
    if (options.additionalProperties?.tenantContext) {
      props.push('tenantContext=true');
    }
    
    if (options.additionalProperties?.authIntegration) {
      props.push('authIntegration=true');
    }
    
    if (options.additionalProperties?.supportsAsync) {
      props.push('supportsAsync=true');
    }

    // Language-specific properties
    switch (options.language) {
      case 'typescript':
        props.push('typescriptThreePlus=true');
        props.push('supportsES6=true');
        props.push('npmName=' + options.packageName);
        break;
      case 'python':
        props.push('packageName=' + options.packageName);
        props.push('projectName=' + options.packageName);
        break;
      case 'java':
        props.push('groupId=com.nexus');
        props.push('artifactId=' + options.packageName);
        break;
    }

    return props;
  }

  private async prepareOpenAPISpec(options: OpenAPIGenerationOptions): Promise<string> {
    // Enhance OpenAPI spec with tenant context and auth
    const enhancedSpec = {
      ...this.config.openApiSpec,
      components: {
        ...this.config.openApiSpec.components,
        securitySchemes: {
          BearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          },
          TenantHeader: {
            type: 'apiKey',
            in: 'header',
            name: 'X-Tenant-ID'
          }
        }
      },
      security: [
        { BearerAuth: [] },
        { TenantHeader: [] }
      ]
    };

    const specPath = path.join(options.outputPath, 'openapi-enhanced.json');
    await mkdir(path.dirname(specPath), { recursive: true });
    await writeFile(specPath, JSON.stringify(enhancedSpec, null, 2));
    
    return specPath;
  }

  private async prepareCustomTemplates(language: string): Promise<void> {
    const templateDir = path.join(this.templateDir, language);
    await mkdir(templateDir, { recursive: true });

    // Copy and customize templates based on language
    switch (language) {
      case 'typescript':
        await this.prepareTypeScriptTemplates(templateDir);
        break;
      case 'python':
        await this.preparePythonTemplates(templateDir);
        break;
      // Add other languages as needed
    }
  }

  private async prepareTypeScriptTemplates(templateDir: string): Promise<void> {
    // Custom TypeScript template with tenant context
    const apiTemplate = `
import { Configuration, {{classname}}Api } from './generated';
import { TenantContext } from './tenant-context';
import { AuthProvider } from './auth-provider';

export class {{classname}}Client extends {{classname}}Api {
  private tenantContext: TenantContext;
  private authProvider: AuthProvider;

  constructor(
    tenantContext: TenantContext,
    authProvider: AuthProvider,
    configuration?: Configuration
  ) {
    const config = new Configuration({
      ...configuration,
      accessToken: () => authProvider.getAccessToken(),
      headers: {
        ...configuration?.headers,
        'X-Tenant-ID': tenantContext.getTenantId()
      }
    });
    
    super(config);
    this.tenantContext = tenantContext;
    this.authProvider = authProvider;
  }

  // Override methods to add tenant context automatically
  {{#operations}}
  {{#operation}}
  async {{operationId}}({{#allParams}}{{paramName}}{{#hasMore}}, {{/hasMore}}{{/allParams}}): Promise<{{returnType}}> {
    return super.{{operationId}}({{#allParams}}{{paramName}}{{#hasMore}}, {{/hasMore}}{{/allParams}});
  }
  {{/operation}}
  {{/operations}}
}
`;

### 2. Language-Specific Generators

#### TypeScript SDK Generator (`lib/sdk-generator/languages/typescript.ts`)
```typescript
import { GraphQLCodeGenerator } from '../graphql/generator';
import { OpenAPIGenerator } from '../openapi/generator';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

export class TypeScriptSDKGenerator {
  private openApiGenerator: OpenAPIGenerator;
  private graphqlGenerator: GraphQLCodeGenerator;

  constructor(config: SDKGeneratorConfig) {
    this.openApiGenerator = new OpenAPIGenerator(config);
    this.graphqlGenerator = new GraphQLCodeGenerator(config);
  }

  async generate(options: TypeScriptGenerationOptions): Promise<TypeScriptSDKArtifact> {
    const outputDir = options.outputPath;
    
    // Generate base client structure
    await this.generateBaseClient(outputDir, options);
    
    // Generate REST API client
    const restClient = await this.openApiGenerator.generate({
      ...options,
      language: 'typescript',
      template: 'typescript-fetch'
    });

    // Generate GraphQL client with React hooks
    const graphqlClient = await this.generateGraphQLClient(outputDir, options);
    
    // Generate WebSocket client
    const websocketClient = await this.generateWebSocketClient(outputDir, options);
    
    // Generate package.json and build configuration
    await this.generatePackageConfig(outputDir, options);
    
    // Generate TypeScript configuration
    await this.generateTSConfig(outputDir);
    
    // Generate documentation
    await this.generateDocumentation(outputDir, options);

    return {
      type: 'typescript-sdk',
      path: outputDir,
      artifacts: [restClient, graphqlClient, websocketClient],
      packageInfo: {
        name: options.packageName,
        version: options.version,
        main: 'dist/index.js',
        types: 'dist/index.d.ts'
      }
    };
  }

  private async generateBaseClient(outputDir: string, options: TypeScriptGenerationOptions): Promise<void> {
    const clientCode = `
import { Configuration } from './generated/configuration';
import { AuthProvider } from './auth/auth-provider';
import { TenantContext } from './tenant/tenant-context';
import { WebSocketClient } from './websocket/client';
import { GraphQLClient } from './graphql/client';
import { RetryConfig, defaultRetryConfig } from './utils/retry';
import { Logger, createLogger } from './utils/logger';

export interface NexusClientConfig {
  baseUrl: string;
  tenantId: string;
  authProvider: AuthProvider;
  retryConfig?: RetryConfig;
  logger?: Logger;
  enableWebSocket?: boolean;
  enableGraphQL?: boolean;
}

export class NexusClient {
  private config: NexusClientConfig;
  private tenantContext: TenantContext;
  private logger: Logger;
  private wsClient?: WebSocketClient;
  private gqlClient?: GraphQLClient;

  // Generated API clients will be injected here
  public readonly api: {
    // Auto-generated from OpenAPI spec
  };

  constructor(config: NexusClientConfig) {
    this.config = {
      retryConfig: defaultRetryConfig,
      logger: createLogger('NexusClient'),
      ...config
    };
    
    this.tenantContext = new TenantContext(config.tenantId);
    this.logger = this.config.logger!;
    
    // Initialize API clients
    this.initializeApiClients();
    
    // Initialize optional clients
    if (config.enableWebSocket) {
      this.initializeWebSocketClient();
    }
    
    if (config.enableGraphQL) {
      this.initializeGraphQLClient();
    }
  }

  private initializeApiClients(): void {
    const configuration = new Configuration({
      basePath: this.config.baseUrl,
      accessToken: () => this.config.authProvider.getAccessToken(),
      headers: {
        'X-Tenant-ID': this.tenantContext.getTenantId(),
        'User-Agent': 'nexus-sdk-typescript/${options.version}'
      }
    });

    // Auto-generated API clients will be initialized here
    // this.api = new GeneratedApiClients(configuration);
  }

  private initializeWebSocketClient(): void {
    this.wsClient = new WebSocketClient({
      url: this.config.baseUrl.replace('http', 'ws') + '/ws',
      tenantId: this.tenantContext.getTenantId(),
      authProvider: this.config.authProvider,
      logger: this.logger
    });
  }

  private initializeGraphQLClient(): void {
    this.gqlClient = new GraphQLClient({
      endpoint: this.config.baseUrl + '/graphql',
      tenantId: this.tenantContext.getTenantId(),
      authProvider: this.config.authProvider,
      logger: this.logger
    });
  }

  // Tenant management
  public switchTenant(tenantId: string): void {
    this.tenantContext.setTenantId(tenantId);
    // Reinitialize clients with new tenant context
    this.initializeApiClients();
  }

  public getTenantId(): string {
    return this.tenantContext.getTenantId();
  }

  // WebSocket methods
  public get websocket(): WebSocketClient | undefined {
    return this.wsClient;
  }

  // GraphQL methods
  public get graphql(): GraphQLClient | undefined {
    return this.gqlClient;
  }

  // Cleanup
  public async disconnect(): Promise<void> {
    if (this.wsClient) {
      await this.wsClient.disconnect();
    }
    
    if (this.gqlClient) {
      await this.gqlClient.disconnect();
    }
  }
}

// Export all types and utilities
export * from './generated';
export * from './auth/auth-provider';
export * from './tenant/tenant-context';
export * from './websocket/client';
export * from './graphql/client';
export * from './utils/retry';
export * from './utils/logger';
export * from './types';
`;

    await mkdir(outputDir, { recursive: true });
    await writeFile(path.join(outputDir, 'index.ts'), clientCode);
  }

  private async generateGraphQLClient(outputDir: string, options: TypeScriptGenerationOptions): Promise<Artifact> {
    if (!options.graphqlSchema) {
      return null;
    }

    // Generate GraphQL types and hooks
    const graphqlConfig = {
      schema: options.graphqlSchema,
      generates: {
        [`${outputDir}/graphql/types.ts`]: {
          plugins: ['typescript', 'typescript-operations'],
          config: {
            scalars: {
              DateTime: 'Date',
              JSON: 'Record<string, any>',
              UUID: 'string'
            },
            enumsAsTypes: true,
            futureProofEnums: true,
            nonOptionalTypename: true
          }
        },
        [`${outputDir}/graphql/hooks.ts`]: {
          plugins: ['typescript-react-apollo'],
          config: {
            withHooks: true,
            withComponent: false,
            withHOC: false,
            apolloReactHooksImportFrom: '@apollo/client'
          }
        },
        [`${outputDir}/graphql/sdk.ts`]: {
          plugins: ['typescript-graphql-request'],
          config: {
            rawRequest: false,
            inlineFragmentTypes: 'combine'
          }
        }
      }
    };

    await this.graphqlGenerator.generate({
      language: 'typescript',
      schema: options.graphqlSchema,
      outputPath: `${outputDir}/graphql`,
      config: graphqlConfig
    });

    // Generate GraphQL client wrapper
    const clientCode = `
import { GraphQLClient as BaseGraphQLClient } from 'graphql-request';
import { getSdk, Sdk } from './sdk';
import { AuthProvider } from '../auth/auth-provider';
import { Logger } from '../utils/logger';

export interface GraphQLClientConfig {
  endpoint: string;
  tenantId: string;
  authProvider: AuthProvider;
  logger: Logger;
}

export class GraphQLClient {
  private client: BaseGraphQLClient;
  private sdk: Sdk;
  private config: GraphQLClientConfig;

  constructor(config: GraphQLClientConfig) {
    this.config = config;
    this.client = new BaseGraphQLClient(config.endpoint, {
      headers: {
        'X-Tenant-ID': config.tenantId,
        'Authorization': \`Bearer \${config.authProvider.getAccessToken()}\`
      }
    });
    this.sdk = getSdk(this.client);
  }

  // Expose SDK methods
  public get queries() {
    return this.sdk;
  }

  // Raw GraphQL execution
  public async request<T = any>(query: string, variables?: any): Promise<T> {
    return this.client.request<T>(query, variables);
  }

  public async disconnect(): Promise<void> {
    // Cleanup if needed
  }
}
`;

    await writeFile(path.join(outputDir, 'graphql', 'client.ts'), clientCode);

    return {
      type: 'graphql-client',
      path: `${outputDir}/graphql`,
      language: 'typescript'
    };
  }

  private async generateWebSocketClient(outputDir: string, options: TypeScriptGenerationOptions): Promise<Artifact> {
    const wsClientCode = `
import { AuthProvider } from '../auth/auth-provider';
import { Logger } from '../utils/logger';

export interface WebSocketClientConfig {
  url: string;
  tenantId: string;
  authProvider: AuthProvider;
  logger: Logger;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

export interface WebSocketMessage {
  type: string;
  payload: any;
  tenantId: string;
  timestamp: number;
}

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private config: WebSocketClientConfig;
  private reconnectAttempts = 0;
  private eventListeners = new Map<string, Set<Function>>();
  private isConnected = false;

  constructor(config: WebSocketClientConfig) {
    this.config = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 5,
      ...config
    };
  }

  public async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const token = this.config.authProvider.getAccessToken();
        const url = \`\${this.config.url}?token=\${token}&tenantId=\${this.config.tenantId}\`;
        
        this.ws = new WebSocket(url);
        
        this.ws.onopen = () => {
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.config.logger.info('WebSocket connected');
          resolve();
        };
        
        this.ws.onmessage = (event) => {
          this.handleMessage(event);
        };
        
        this.ws.onclose = () => {
          this.isConnected = false;
          this.config.logger.info('WebSocket disconnected');
          this.handleReconnect();
        };
        
        this.ws.onerror = (error) => {
          this.config.logger.error('WebSocket error:', error);
          reject(error);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  public async disconnect(): Promise<void> {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.isConnected = false;
    }
  }

  public send(type: string, payload: any): void {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket not connected');
    }

    const message: WebSocketMessage = {
      type,
      payload,
      tenantId: this.config.tenantId,
      timestamp: Date.now()
    };

    this.ws.send(JSON.stringify(message));
  }

  public on(eventType: string, callback: Function): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    this.eventListeners.get(eventType)!.add(callback);
  }

  public off(eventType: string, callback: Function): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      // Verify tenant context
      if (message.tenantId !== this.config.tenantId) {
        this.config.logger.warn('Received message for different tenant');
        return;
      }

      const listeners = this.eventListeners.get(message.type);
      if (listeners) {
        listeners.forEach(callback => callback(message.payload));
      }
    } catch (error) {
      this.config.logger.error('Failed to parse WebSocket message:', error);
    }
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.config.maxReconnectAttempts!) {
      this.reconnectAttempts++;
      setTimeout(() => {
        this.config.logger.info(\`Attempting to reconnect (\${this.reconnectAttempts}/\${this.config.maxReconnectAttempts})\`);
        this.connect().catch(error => {
          this.config.logger.error('Reconnection failed:', error);
        });
      }, this.config.reconnectInterval);
    }
  }
}

// React hook for WebSocket
export function useWebSocket(client: WebSocketClient, eventType: string) {
  const [data, setData] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const handleMessage = (payload: any) => {
      setData(payload);
    };

    const handleConnection = () => {
      setIsConnected(true);
    };

    const handleDisconnection = () => {
      setIsConnected(false);
    };

    client.on(eventType, handleMessage);
    client.on('connected', handleConnection);
    client.on('disconnected', handleDisconnection);

    return () => {
      client.off(eventType, handleMessage);
      client.off('connected', handleConnection);
      client.off('disconnected', handleDisconnection);
    };
  }, [client, eventType]);

  return { data, isConnected };
}
`;

    await mkdir(path.join(outputDir, 'websocket'), { recursive: true });
    await writeFile(path.join(outputDir, 'websocket', 'client.ts'), wsClientCode);

    return {
      type: 'websocket-client',
      path: `${outputDir}/websocket`,
      language: 'typescript'
    };
  }

  private async generatePackageConfig(outputDir: string, options: TypeScriptGenerationOptions): Promise<void> {
    const packageJson = {
      name: options.packageName,
      version: options.version,
      description: `TypeScript SDK for ${options.serviceName || 'NEXUS SaaS'}`,
      main: 'dist/index.js',
      types: 'dist/index.d.ts',
      module: 'dist/index.esm.js',
      files: ['dist'],
      scripts: {
        build: 'rollup -c',
        'build:watch': 'rollup -c -w',
        test: 'vitest',
        'test:coverage': 'vitest --coverage',
        lint: 'eslint src --ext .ts,.tsx',
        'lint:fix': 'eslint src --ext .ts,.tsx --fix',
        'type-check': 'tsc --noEmit',
        docs: 'typedoc src/index.ts'
      },
      dependencies: {
        'graphql-request': '^6.1.0',
        '@apollo/client': '^3.8.0',
        'graphql': '^16.8.0'
      },
      devDependencies: {
        '@types/node': '^20.0.0',
        '@typescript-eslint/eslint-plugin': '^6.0.0',
        '@typescript-eslint/parser': '^6.0.0',
        'eslint': '^8.0.0',
        'rollup': '^4.0.0',
        '@rollup/plugin-typescript': '^11.0.0',
        '@rollup/plugin-node-resolve': '^15.0.0',
        '@rollup/plugin-commonjs': '^25.0.0',
        'typescript': '^5.0.0',
        'vitest': '^1.0.0',
        'typedoc': '^0.25.0'
      },
      peerDependencies: {
        'react': '>=16.8.0',
        'react-dom': '>=16.8.0'
      },
      keywords: [
        'nexus',
        'saas',
        'sdk',
        'typescript',
        'api-client',
        'graphql',
        'websocket'
      ],
      author: 'NEXUS Team',
      license: 'MIT',
      repository: {
        type: 'git',
        url: options.repositoryUrl || ''
      },
      bugs: {
        url: options.bugsUrl || ''
      },
      homepage: options.homepageUrl || ''
    };

    await writeFile(path.join(outputDir, 'package.json'), JSON.stringify(packageJson, null, 2));
  }

  private async generateTSConfig(outputDir: string): Promise<void> {
    const tsConfig = {
      compilerOptions: {
        target: 'ES2020',
        lib: ['ES2020', 'DOM'],
        module: 'ESNext',
        moduleResolution: 'node',
        declaration: true,
        declarationMap: true,
        sourceMap: true,
        outDir: 'dist',
        strict: true,
        esModuleInterop: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true,
        resolveJsonModule: true,
        allowSyntheticDefaultImports: true
      },
      include: ['src/**/*'],
      exclude: ['node_modules', 'dist', '**/*.test.ts', '**/*.spec.ts']
    };

    await writeFile(path.join(outputDir, 'tsconfig.json'), JSON.stringify(tsConfig, null, 2));
  }
}
```

#### Python SDK Generator (`lib/sdk-generator/languages/python.ts`)
```typescript
export class PythonSDKGenerator {
  private openApiGenerator: OpenAPIGenerator;

  constructor(config: SDKGeneratorConfig) {
    this.openApiGenerator = new OpenAPIGenerator(config);
  }

  async generate(options: PythonGenerationOptions): Promise<PythonSDKArtifact> {
    const outputDir = options.outputPath;
    
    // Generate base client using OpenAPI Generator
    const restClient = await this.openApiGenerator.generate({
      ...options,
      language: 'python',
      template: 'python'
    });

    // Generate enhanced client with multi-tenant support
    await this.generateEnhancedClient(outputDir, options);
    
    // Generate setup.py and requirements
    await this.generatePackageConfig(outputDir, options);
    
    // Generate documentation
    await this.generateDocumentation(outputDir, options);

    return {
      type: 'python-sdk',
      path: outputDir,
      artifacts: [restClient],
      packageInfo: {
        name: options.packageName,
        version: options.version
      }
    };
  }

  private async generateEnhancedClient(outputDir: string, options: PythonGenerationOptions): Promise<void> {
    const clientCode = `
"""
NEXUS SaaS Python SDK
Multi-tenant aware client library
"""

import asyncio
import logging
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass
import httpx
import websockets
import json
from datetime import datetime

@dataclass
class NexusClientConfig:
    base_url: str
    tenant_id: str
    auth_provider: 'AuthProvider'
    timeout: float = 30.0
    retry_attempts: int = 3
    enable_websocket: bool = False
    logger: Optional[logging.Logger] = None

class AuthProvider:
    """Abstract base class for authentication providers"""
    
    def get_access_token(self) -> str:
        raise NotImplementedError
    
    def refresh_token(self) -> str:
        raise NotImplementedError

class TenantContext:
    """Manages tenant context for multi-tenant operations"""
    
    def __init__(self, tenant_id: str):
        self._tenant_id = tenant_id
    
    @property
    def tenant_id(self) -> str:
        return self._tenant_id
    
    def set_tenant_id(self, tenant_id: str) -> None:
        self._tenant_id = tenant_id

class WebSocketClient:
    """WebSocket client for real-time features"""
    
    def __init__(self, config: NexusClientConfig):
        self.config = config
        self.websocket = None
        self.event_handlers = {}
        self.is_connected = False
        
    async def connect(self) -> None:
        """Connect to WebSocket endpoint"""
        token = self.config.auth_provider.get_access_token()
        url = f"{self.config.base_url.replace('http', 'ws')}/ws?token={token}&tenantId={self.config.tenant_id}"
        
        self.websocket = await websockets.connect(url)
        self.is_connected = True
        
        # Start message handler
        asyncio.create_task(self._handle_messages())
    
    async def disconnect(self) -> None:
        """Disconnect from WebSocket"""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
    
    async def send(self, message_type: str, payload: Dict[str, Any]) -> None:
        """Send message through WebSocket"""
        if not self.is_connected or not self.websocket:
            raise RuntimeError("WebSocket not connected")
        
        message = {
            "type": message_type,
            "payload": payload,
            "tenantId": self.config.tenant_id,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.websocket.send(json.dumps(message))
    
    def on(self, event_type: str, handler: Callable) -> None:
        """Register event handler"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
    
    async def _handle_messages(self) -> None:
        """Handle incoming WebSocket messages"""
        async for message in self.websocket:
            try:
                data = json.loads(message)
                
                # Verify tenant context
                if data.get('tenantId') != self.config.tenant_id:
                    continue
                
                message_type = data.get('type')
                if message_type in self.event_handlers:
                    for handler in self.event_handlers[message_type]:
                        await handler(data.get('payload'))
                        
            except Exception as e:
                if self.config.logger:
                    self.config.logger.error(f"Error handling WebSocket message: {e}")

class NexusClient:
    """Main NEXUS SaaS client"""
    
    def __init__(self, config: NexusClientConfig):
        self.config = config
        self.tenant_context = TenantContext(config.tenant_id)
        self.logger = config.logger or logging.getLogger(__name__)
        
        # Initialize HTTP client
        self.http_client = httpx.AsyncClient(
            base_url=config.base_url,
            timeout=config.timeout,
            headers={
                'X-Tenant-ID': config.tenant_id,
                'User-Agent': f'nexus-sdk-python/{options.version}'
            }
        )
        
        # Initialize WebSocket client if enabled
        self.websocket_client = None
        if config.enable_websocket:
            self.websocket_client = WebSocketClient(config)
    
    async def __aenter__(self):
        """Async context manager entry"""
        if self.websocket_client:
            await self.websocket_client.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def close(self) -> None:
        """Close all connections"""
        await self.http_client.aclose()
        if self.websocket_client:
            await self.websocket_client.disconnect()
    
    def switch_tenant(self, tenant_id: str) -> None:
        """Switch to different tenant context"""
        self.tenant_context.set_tenant_id(tenant_id)
        self.http_client.headers['X-Tenant-ID'] = tenant_id
    
    @property
    def tenant_id(self) -> str:
        """Get current tenant ID"""
        return self.tenant_context.tenant_id
    
    @property
    def websocket(self) -> Optional[WebSocketClient]:
        """Get WebSocket client"""
        return self.websocket_client
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> httpx.Response:
        """Make authenticated HTTP request"""
        headers = kwargs.get('headers', {})
        headers['Authorization'] = f'Bearer {self.config.auth_provider.get_access_token()}'
        kwargs['headers'] = headers
        
        for attempt in range(self.config.retry_attempts):
            try:
                response = await self.http_client.request(method, endpoint, **kwargs)
                response.raise_for_status()
                return response
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 401:
                    # Try to refresh token
                    self.config.auth_provider.refresh_token()
                    headers['Authorization'] = f'Bearer {self.config.auth_provider.get_access_token()}'
                    continue
                elif attempt == self.config.retry_attempts - 1:
                    raise
            except Exception as e:
                if attempt == self.config.retry_attempts - 1:
                    raise
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
        
        raise RuntimeError("Max retry attempts exceeded")

# Export main classes
__all__ = ['NexusClient', 'NexusClientConfig', 'AuthProvider', 'TenantContext', 'WebSocketClient']
`;

### 3. API Routes & Management

#### SDK Generation API (`app/api/sdk/generate/route.ts`)
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { SDKGenerator } from '@/lib/sdk-generator';
import { generateOpenAPISpec } from '@/lib/api-docs/openapi';
import { z } from 'zod';

const generateSDKSchema = z.object({
  languages: z.array(z.enum(['typescript', 'javascript', 'python', 'go', 'java', 'csharp', 'php', 'ruby'])),
  version: z.string().optional(),
  includeGraphQL: z.boolean().default(false),
  includeWebSocket: z.boolean().default(false),
  packageName: z.string().optional(),
  customizations: z.record(z.any()).optional()
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const workspace = await prisma.workspace.findFirst({
      where: {
        members: {
          some: { userId: session.user.id }
        }
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    const body = await request.json();
    const { languages, version, includeGraphQL, includeWebSocket, packageName, customizations } = 
      generateSDKSchema.parse(body);

    // Generate OpenAPI specification
    const openApiSpec = await generateOpenAPISpec(workspace.id);
    
    // Get GraphQL schema if requested
    let graphqlSchema = null;
    if (includeGraphQL) {
      graphqlSchema = await getGraphQLSchema(workspace.id);
    }

    // Initialize SDK generator
    const generator = new SDKGenerator({
      openApiSpec,
      graphqlSchema,
      outputDir: `/tmp/sdk-generation/${workspace.id}`,
      languages: languages.map(lang => ({
        name: lang,
        version: version || '1.0.0',
        template: getDefaultTemplate(lang),
        packageName: packageName || `nexus-${workspace.slug}-sdk-${lang}`,
        outputPath: `/tmp/sdk-generation/${workspace.id}/${lang}`
      })),
      tenantContext: true,
      authIntegration: true,
      realtimeSupport: includeWebSocket
    });

    // Generate SDKs
    const results = await generator.generateAll();

    // Store generation results
    const sdkVersions = await Promise.all(
      results.map(async (result) => {
        if (result.success) {
          return prisma.sdkVersion.create({
            data: {
              workspaceId: workspace.id,
              language: result.language,
              version: version || '1.0.0',
              generatedAt: new Date(),
              downloadUrl: `/api/sdk/download/${result.language}/${version || '1.0.0'}`,
              features: {
                graphql: includeGraphQL,
                websocket: includeWebSocket,
                ...customizations
              },
              metadata: result.metadata
            }
          });
        }
        return null;
      })
    );

    return NextResponse.json({
      success: true,
      results: results.filter(r => r.success),
      errors: results.filter(r => !r.success),
      sdkVersions: sdkVersions.filter(Boolean)
    });

  } catch (error) {
    console.error('SDK generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate SDKs' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const workspace = await prisma.workspace.findFirst({
      where: {
        members: {
          some: { userId: session.user.id }
        }
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    const sdkVersions = await prisma.sdkVersion.findMany({
      where: { workspaceId: workspace.id },
      orderBy: { generatedAt: 'desc' },
      take: 50
    });

    return NextResponse.json({ sdkVersions });

  } catch (error) {
    console.error('Error fetching SDK versions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch SDK versions' },
      { status: 500 }
    );
  }
}

function getDefaultTemplate(language: string): string {
  const templates = {
    typescript: 'typescript-fetch',
    javascript: 'javascript',
    python: 'python',
    go: 'go',
    java: 'java',
    csharp: 'csharp',
    php: 'php',
    ruby: 'ruby'
  };
  return templates[language] || 'typescript-fetch';
}

async function getGraphQLSchema(workspaceId: string): Promise<string | null> {
  // Implementation to get GraphQL schema for workspace
  // This would integrate with your GraphQL setup
  return null;
}
```

#### SDK Download API (`app/api/sdk/download/[language]/[version]/route.ts`)
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { createReadStream, existsSync } from 'fs';
import { join } from 'path';
import archiver from 'archiver';

interface RouteParams {
  params: {
    language: string;
    version: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const workspace = await prisma.workspace.findFirst({
      where: {
        members: {
          some: { userId: session.user.id }
        }
      }
    });

    if (!workspace) {
      return NextResponse.json({ error: 'Workspace not found' }, { status: 404 });
    }

    const { language, version } = params;

    // Find SDK version
    const sdkVersion = await prisma.sdkVersion.findFirst({
      where: {
        workspaceId: workspace.id,
        language,
        version
      }
    });

    if (!sdkVersion) {
      return NextResponse.json({ error: 'SDK version not found' }, { status: 404 });
    }

    // Check if SDK files exist
    const sdkPath = join(process.cwd(), 'tmp', 'sdk-generation', workspace.id, language);
    if (!existsSync(sdkPath)) {
      return NextResponse.json({ error: 'SDK files not found' }, { status: 404 });
    }

    // Track download
    await prisma.sdkDownload.create({
      data: {
        workspaceId: workspace.id,
        sdkVersionId: sdkVersion.id,
        userId: session.user.id,
        downloadedAt: new Date(),
        userAgent: request.headers.get('user-agent') || '',
        ipAddress: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown'
      }
    });

    // Create ZIP archive
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    // Set response headers
    const filename = `${workspace.slug}-${language}-sdk-v${version}.zip`;
    const headers = new Headers({
      'Content-Type': 'application/zip',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Cache-Control': 'no-cache'
    });

    // Create readable stream for the response
    const stream = new ReadableStream({
      start(controller) {
        archive.on('data', (chunk) => {
          controller.enqueue(new Uint8Array(chunk));
        });

        archive.on('end', () => {
          controller.close();
        });

        archive.on('error', (err) => {
          controller.error(err);
        });

        // Add SDK files to archive
        archive.directory(sdkPath, false);
        archive.finalize();
      }
    });

    return new NextResponse(stream, { headers });

  } catch (error) {
    console.error('SDK download error:', error);
    return NextResponse.json(
      { error: 'Failed to download SDK' },
      { status: 500 }
    );
  }
}
```

### 4. Testing Framework

#### SDK Testing Suite (`lib/sdk-generator/testing/test-runner.ts`)
```typescript
import { spawn } from 'child_process';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

export interface TestSuite {
  language: string;
  tests: TestCase[];
  setup?: string[];
  teardown?: string[];
}

export interface TestCase {
  name: string;
  description: string;
  code: string;
  expectedOutput?: any;
  shouldFail?: boolean;
}

export class SDKTestRunner {
  private outputDir: string;

  constructor(outputDir: string) {
    this.outputDir = outputDir;
  }

  async runTestSuite(suite: TestSuite): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    // Setup test environment
    if (suite.setup) {
      await this.runSetupCommands(suite.setup, suite.language);
    }

    // Run each test case
    for (const testCase of suite.tests) {
      try {
        const result = await this.runTestCase(testCase, suite.language);
        results.push(result);
      } catch (error) {
        results.push({
          name: testCase.name,
          success: false,
          error: error.message,
          duration: 0
        });
      }
    }

    // Cleanup
    if (suite.teardown) {
      await this.runTeardownCommands(suite.teardown, suite.language);
    }

    return results;
  }

  private async runTestCase(testCase: TestCase, language: string): Promise<TestResult> {
    const startTime = Date.now();
    
    // Write test file
    const testFile = await this.writeTestFile(testCase, language);
    
    // Execute test
    const result = await this.executeTest(testFile, language);
    
    const duration = Date.now() - startTime;

    return {
      name: testCase.name,
      success: testCase.shouldFail ? !result.success : result.success,
      output: result.output,
      error: result.error,
      duration
    };
  }

  private async writeTestFile(testCase: TestCase, language: string): Promise<string> {
    const testDir = path.join(this.outputDir, 'tests', language);
    await mkdir(testDir, { recursive: true });

    const extension = this.getFileExtension(language);
    const filename = `${testCase.name.replace(/\s+/g, '_')}.${extension}`;
    const filepath = path.join(testDir, filename);

    await writeFile(filepath, testCase.code);
    return filepath;
  }

  private async executeTest(testFile: string, language: string): Promise<ExecutionResult> {
    const command = this.getTestCommand(language, testFile);
    
    return new Promise((resolve) => {
      const process = spawn(command.cmd, command.args, {
        cwd: path.dirname(testFile),
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        resolve({
          success: code === 0,
          output: stdout,
          error: stderr
        });
      });
    });
  }

  private getTestCommand(language: string, testFile: string): { cmd: string; args: string[] } {
    switch (language) {
      case 'typescript':
        return { cmd: 'npx', args: ['tsx', testFile] };
      case 'javascript':
        return { cmd: 'node', args: [testFile] };
      case 'python':
        return { cmd: 'python', args: [testFile] };
      case 'go':
        return { cmd: 'go', args: ['run', testFile] };
      default:
        throw new Error(`Unsupported test language: ${language}`);
    }
  }

  private getFileExtension(language: string): string {
    const extensions = {
      typescript: 'ts',
      javascript: 'js',
      python: 'py',
      go: 'go',
      java: 'java',
      csharp: 'cs',
      php: 'php',
      ruby: 'rb'
    };
    return extensions[language] || 'txt';
  }
}

// Test suite definitions
export const createTypeScriptTestSuite = (packageName: string): TestSuite => ({
  language: 'typescript',
  setup: [
    'npm install',
    'npm run build'
  ],
  tests: [
    {
      name: 'client_initialization',
      description: 'Test client initialization with tenant context',
      code: `
import { NexusClient, NexusClientConfig } from '${packageName}';

class MockAuthProvider {
  getAccessToken(): string {
    return 'mock-token';
  }
}

const config: NexusClientConfig = {
  baseUrl: 'https://api.example.com',
  tenantId: 'test-tenant',
  authProvider: new MockAuthProvider()
};

const client = new NexusClient(config);
console.log('Client initialized successfully');
console.log('Tenant ID:', client.getTenantId());
`
    },
    {
      name: 'tenant_switching',
      description: 'Test tenant context switching',
      code: `
import { NexusClient, NexusClientConfig } from '${packageName}';

class MockAuthProvider {
  getAccessToken(): string {
    return 'mock-token';
  }
}

const config: NexusClientConfig = {
  baseUrl: 'https://api.example.com',
  tenantId: 'tenant-1',
  authProvider: new MockAuthProvider()
};

const client = new NexusClient(config);
console.log('Initial tenant:', client.getTenantId());

client.switchTenant('tenant-2');
console.log('Switched tenant:', client.getTenantId());
`
    }
  ]
});
```

### 5. CI/CD Integration

#### GitHub Actions Workflow (`.github/workflows/sdk-generation.yml`)
```yaml
name: SDK Generation and Publishing

on:
  push:
    branches: [main]
    paths:
      - 'app/api/**'
      - 'lib/api-docs/**'
      - 'prisma/schema.prisma'
  workflow_dispatch:
    inputs:
      languages:
        description: 'Languages to generate (comma-separated)'
        required: false
        default: 'typescript,python,go'
      version:
        description: 'SDK version'
        required: false
        default: 'auto'

jobs:
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      api-changed: ${{ steps.changes.outputs.api }}
      schema-changed: ${{ steps.changes.outputs.schema }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            api:
              - 'app/api/**'
              - 'lib/api-docs/**'
            schema:
              - 'prisma/schema.prisma'

  generate-sdks:
    needs: detect-changes
    if: needs.detect-changes.outputs.api-changed == 'true' || needs.detect-changes.outputs.schema-changed == 'true' || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        language: [typescript, python, go, java, csharp]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Python
        if: matrix.language == 'python'
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Setup Go
        if: matrix.language == 'go'
        uses: actions/setup-go@v4
        with:
          go-version: '1.21'

      - name: Setup Java
        if: matrix.language == 'java'
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Setup .NET
        if: matrix.language == 'csharp'
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '8.0'

      - name: Install dependencies
        run: npm ci

      - name: Install OpenAPI Generator
        run: npm install -g @openapitools/openapi-generator-cli

      - name: Generate OpenAPI spec
        run: npm run generate:openapi-spec

      - name: Generate SDK
        run: |
          npm run sdk:generate -- \
            --language ${{ matrix.language }} \
            --version ${{ github.event.inputs.version || 'auto' }} \
            --output ./generated-sdks/${{ matrix.language }}

      - name: Test SDK
        run: npm run sdk:test -- --language ${{ matrix.language }}

      - name: Build SDK package
        run: npm run sdk:build -- --language ${{ matrix.language }}

      - name: Upload SDK artifacts
        uses: actions/upload-artifact@v3
        with:
          name: sdk-${{ matrix.language }}
          path: ./generated-sdks/${{ matrix.language }}/dist/

      - name: Publish to package registry
        if: github.ref == 'refs/heads/main'
        run: npm run sdk:publish -- --language ${{ matrix.language }}
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          PYPI_TOKEN: ${{ secrets.PYPI_TOKEN }}
          NUGET_TOKEN: ${{ secrets.NUGET_TOKEN }}

  update-documentation:
    needs: generate-sdks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Download SDK artifacts
        uses: actions/download-artifact@v3
        with:
          path: ./sdk-artifacts

      - name: Generate SDK documentation
        run: npm run sdk:docs:generate

      - name: Update developer portal
        run: npm run docs:update-sdk-docs

      - name: Commit documentation updates
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add docs/
          git diff --staged --quiet || git commit -m "Update SDK documentation [skip ci]"
          git push

  notify-completion:
    needs: [generate-sdks, update-documentation]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#dev-notifications'
          text: |
            SDK Generation completed for commit ${{ github.sha }}
            Languages: ${{ join(matrix.language, ', ') }}
            Status: ${{ job.status }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

---

## 🧪 Testing Strategy

### Unit Tests
- **Generator Logic**: Test SDK generation for each language
- **Template Rendering**: Verify custom template processing
- **Multi-tenant Context**: Ensure tenant isolation
- **Authentication Integration**: Test auth provider patterns

### Integration Tests
- **End-to-End Generation**: Full SDK generation pipeline
- **API Compatibility**: Generated SDKs work with actual APIs
- **WebSocket Functionality**: Real-time features work correctly
- **Cross-Language Consistency**: Similar behavior across languages

### Performance Tests
- **Generation Speed**: Benchmark SDK generation times
- **Bundle Size**: Monitor generated SDK sizes
- **Runtime Performance**: Test SDK execution speed
- **Memory Usage**: Monitor resource consumption

---

## 📊 Monitoring & Analytics

### SDK Usage Metrics
- Download counts by language and version
- API call patterns from generated SDKs
- Error rates and common issues
- Performance metrics and bottlenecks

### Developer Experience Metrics
- Time to first successful API call
- Documentation engagement
- Support ticket categories
- Feature adoption rates

---

## 🔄 Maintenance & Updates

### Automated Updates
- **API Changes**: Auto-regenerate SDKs when APIs change
- **Security Patches**: Update dependencies and regenerate
- **Feature Additions**: Extend SDKs with new capabilities
- **Bug Fixes**: Address issues across all languages

### Version Management
- **Semantic Versioning**: Clear version strategy
- **Backward Compatibility**: Maintain compatibility promises
- **Deprecation Notices**: Graceful feature retirement
- **Migration Guides**: Help developers upgrade

---

## 🎯 Success Metrics

### Technical Metrics
- **Generation Success Rate**: >99% successful generations
- **Test Coverage**: >90% code coverage across all SDKs
- **Performance**: <30s generation time per language
- **Bundle Size**: <500KB for TypeScript SDK

### Business Metrics
- **Adoption Rate**: >80% of API users use generated SDKs
- **Developer Satisfaction**: >4.5/5 rating
- **Time to Integration**: <15 minutes for first API call
- **Support Reduction**: 50% fewer API integration tickets

---

    await mkdir(path.join(outputDir, options.packageName.replace('-', '_')), { recursive: true });
    await writeFile(path.join(outputDir, options.packageName.replace('-', '_'), '__init__.py'), clientCode);
  }
}
```

#### Go SDK Generator (`lib/sdk-generator/languages/go.ts`)
```typescript
import { BaseSDKGenerator, SDKGenerationOptions, SDKGenerationResult } from '../core/base-generator';
import { OpenAPIGenerator } from '../openapi/generator';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

export class GoSDKGenerator extends BaseSDKGenerator {
  async generate(options: SDKGenerationOptions): Promise<SDKGenerationResult> {
    try {
      const outputDir = options.outputPath;
      await mkdir(outputDir, { recursive: true });

      // Generate base client using OpenAPI Generator
      const openApiGenerator = new OpenAPIGenerator();
      await openApiGenerator.generate({
        spec: options.openApiSpec,
        language: 'go',
        outputPath: path.join(outputDir, 'generated'),
        packageName: options.packageName,
        additionalProperties: {
          packageName: options.packageName,
          packageVersion: options.version,
          moduleName: `github.com/your-org/${options.packageName}`
        }
      });

      // Generate enhanced client with multi-tenant support
      await this.generateEnhancedClient(outputDir, options);
      
      // Generate Go module files
      await this.generateModuleFiles(outputDir, options);
      
      // Generate documentation
      await this.generateDocumentation(outputDir, options);

      return {
        success: true,
        language: 'go',
        outputPath: outputDir,
        files: await this.getGeneratedFiles(outputDir),
        metadata: {
          packageName: options.packageName,
          version: options.version,
          features: ['rest-api', 'multi-tenant', 'auth', 'websocket']
        }
      };
    } catch (error) {
      return {
        success: false,
        language: 'go',
        error: error.message
      };
    }
  }

  private async generateEnhancedClient(outputDir: string, options: SDKGenerationOptions): Promise<void> {
    const clientCode = `package nexus

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gorilla/websocket"
)

// ClientConfig holds the configuration for the Nexus client
type ClientConfig struct {
	BaseURL      string
	TenantID     string
	AuthProvider AuthProvider
	HTTPClient   *http.Client
	RetryConfig  *RetryConfig
}

// AuthProvider interface for authentication
type AuthProvider interface {
	GetAccessToken(ctx context.Context) (string, error)
}

// TenantContext holds tenant-specific information
type TenantContext struct {
	ID       string
	Name     string
	Settings map[string]interface{}
}

// RetryConfig holds retry configuration
type RetryConfig struct {
	MaxRetries int
	BackoffMs  int
}

// Client is the main Nexus API client
type Client struct {
	config       *ClientConfig
	httpClient   *http.Client
	tenantCtx    *TenantContext
	authProvider AuthProvider
}

// NewClient creates a new Nexus client
func NewClient(config *ClientConfig) *Client {
	if config.HTTPClient == nil {
		config.HTTPClient = &http.Client{
			Timeout: 30 * time.Second,
		}
	}

	if config.RetryConfig == nil {
		config.RetryConfig = &RetryConfig{
			MaxRetries: 3,
			BackoffMs:  1000,
		}
	}

	return &Client{
		config:       config,
		httpClient:   config.HTTPClient,
		authProvider: config.AuthProvider,
		tenantCtx: &TenantContext{
			ID: config.TenantID,
		},
	}
}

// GetTenantID returns the current tenant ID
func (c *Client) GetTenantID() string {
	return c.tenantCtx.ID
}

// SwitchTenant switches to a different tenant context
func (c *Client) SwitchTenant(tenantID string) {
	c.tenantCtx.ID = tenantID
}

// makeRequest makes an authenticated HTTP request
func (c *Client) makeRequest(ctx context.Context, method, endpoint string, body interface{}) (*http.Response, error) {
	// Get access token
	token, err := c.authProvider.GetAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// Build URL
	u, err := url.Parse(c.config.BaseURL + endpoint)
	if err != nil {
		return nil, fmt.Errorf("invalid URL: %w", err)
	}

	// Prepare request body
	var reqBody []byte
	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, method, u.String(), strings.NewReader(string(reqBody)))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Tenant-ID", c.tenantCtx.ID)

	// Execute request with retry
	return c.executeWithRetry(req)
}

// executeWithRetry executes an HTTP request with retry logic
func (c *Client) executeWithRetry(req *http.Request) (*http.Response, error) {
	var lastErr error
	
	for attempt := 0; attempt <= c.config.RetryConfig.MaxRetries; attempt++ {
		if attempt > 0 {
			time.Sleep(time.Duration(c.config.RetryConfig.BackoffMs*attempt) * time.Millisecond)
		}

		resp, err := c.httpClient.Do(req)
		if err != nil {
			lastErr = err
			continue
		}

		// Check if we should retry based on status code
		if resp.StatusCode >= 500 || resp.StatusCode == 429 {
			resp.Body.Close()
			lastErr = fmt.Errorf("server error: %d", resp.StatusCode)
			continue
		}

		return resp, nil
	}

	return nil, fmt.Errorf("request failed after %d attempts: %w", c.config.RetryConfig.MaxRetries, lastErr)
}

// WebSocketClient handles WebSocket connections
type WebSocketClient struct {
	client   *Client
	conn     *websocket.Conn
	handlers map[string]func([]byte)
}

// NewWebSocketClient creates a new WebSocket client
func (c *Client) NewWebSocketClient() *WebSocketClient {
	return &WebSocketClient{
		client:   c,
		handlers: make(map[string]func([]byte)),
	}
}

// Connect establishes a WebSocket connection
func (ws *WebSocketClient) Connect(ctx context.Context, endpoint string) error {
	// Get access token
	token, err := ws.client.authProvider.GetAccessToken(ctx)
	if err != nil {
		return fmt.Errorf("failed to get access token: %w", err)
	}

	// Build WebSocket URL
	wsURL := strings.Replace(ws.client.config.BaseURL, "http", "ws", 1) + endpoint

	// Set headers
	headers := http.Header{}
	headers.Set("Authorization", "Bearer "+token)
	headers.Set("X-Tenant-ID", ws.client.tenantCtx.ID)

	// Connect
	conn, _, err := websocket.DefaultDialer.DialContext(ctx, wsURL, headers)
	if err != nil {
		return fmt.Errorf("failed to connect to WebSocket: %w", err)
	}

	ws.conn = conn
	go ws.readMessages()

	return nil
}

// Subscribe subscribes to a WebSocket event
func (ws *WebSocketClient) Subscribe(event string, handler func([]byte)) {
	ws.handlers[event] = handler
}

// Send sends a message through the WebSocket
func (ws *WebSocketClient) Send(message interface{}) error {
	if ws.conn == nil {
		return fmt.Errorf("WebSocket not connected")
	}

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	return ws.conn.WriteMessage(websocket.TextMessage, data)
}

// Close closes the WebSocket connection
func (ws *WebSocketClient) Close() error {
	if ws.conn != nil {
		return ws.conn.Close()
	}
	return nil
}

// readMessages reads messages from the WebSocket
func (ws *WebSocketClient) readMessages() {
	defer ws.conn.Close()

	for {
		_, message, err := ws.conn.ReadMessage()
		if err != nil {
			break
		}

		// Parse message to determine event type
		var msg map[string]interface{}
		if err := json.Unmarshal(message, &msg); err != nil {
			continue
		}

		if event, ok := msg["event"].(string); ok {
			if handler, exists := ws.handlers[event]; exists {
				handler(message)
			}
		}
	}
}

// Error types
type APIError struct {
	Code    int    \`json:"code"\`
	Message string \`json:"message"\`
	Details string \`json:"details,omitempty"\`
}

func (e *APIError) Error() string {
	return fmt.Sprintf("API error %d: %s", e.Code, e.Message)
}
`;

    await writeFile(path.join(outputDir, 'client.go'), clientCode);
  }

  private async generateModuleFiles(outputDir: string, options: SDKGenerationOptions): Promise<void> {
    // Generate go.mod
    const goMod = `module github.com/your-org/${options.packageName}

go 1.21

require (
	github.com/gorilla/websocket v1.5.0
)
`;

    // Generate go.sum (would be generated by go mod tidy)
    const goSum = `github.com/gorilla/websocket v1.5.0 h1:PPwGk2jz7EePpoHN/+ClbZu8SPxiqlu12wZP/3sWmnc=
github.com/gorilla/websocket v1.5.0/go.mod h1:YR8l580nyteQvAITg2hZ9XVh4b55+EU/adAjf1fMHhE=
`;

    await writeFile(path.join(outputDir, 'go.mod'), goMod);
    await writeFile(path.join(outputDir, 'go.sum'), goSum);
  }

  private async generateDocumentation(outputDir: string, options: SDKGenerationOptions): Promise<void> {
    const readme = `# ${options.packageName}

Go SDK for the Nexus SaaS Platform

## Installation

\`\`\`bash
go get github.com/your-org/${options.packageName}
\`\`\`

## Quick Start

\`\`\`go
package main

import (
    "context"
    "fmt"
    "log"
    
    nexus "github.com/your-org/${options.packageName}"
)

// Implement AuthProvider
type MyAuthProvider struct {
    token string
}

func (a *MyAuthProvider) GetAccessToken(ctx context.Context) (string, error) {
    return a.token, nil
}

func main() {
    // Configure client
    config := &nexus.ClientConfig{
        BaseURL:      "https://api.your-domain.com",
        TenantID:     "your-tenant-id",
        AuthProvider: &MyAuthProvider{token: "your-access-token"},
    }

    // Create client
    client := nexus.NewClient(config)

    // Use the client
    fmt.Printf("Current tenant: %s\\n", client.GetTenantID())
}
\`\`\`

## WebSocket Usage

\`\`\`go
// Create WebSocket client
wsClient := client.NewWebSocketClient()

// Connect
err := wsClient.Connect(context.Background(), "/ws")
if err != nil {
    log.Fatal(err)
}
defer wsClient.Close()

// Subscribe to events
wsClient.Subscribe("notification", func(data []byte) {
    fmt.Printf("Received notification: %s\\n", string(data))
})

// Send message
wsClient.Send(map[string]interface{}{
    "event": "subscribe",
    "channel": "notifications",
})
\`\`\`

## Multi-Tenant Support

\`\`\`go
// Switch tenant context
client.SwitchTenant("different-tenant-id")

// All subsequent API calls will use the new tenant context
fmt.Printf("Switched to tenant: %s\\n", client.GetTenantID())
\`\`\`

## Error Handling

\`\`\`go
resp, err := client.makeRequest(ctx, "GET", "/api/users", nil)
if err != nil {
    if apiErr, ok := err.(*nexus.APIError); ok {
        fmt.Printf("API Error: %d - %s\\n", apiErr.Code, apiErr.Message)
    } else {
        fmt.Printf("Request Error: %v\\n", err)
    }
    return
}
defer resp.Body.Close()
\`\`\`

## Configuration

### Retry Configuration

\`\`\`go
config := &nexus.ClientConfig{
    BaseURL:  "https://api.your-domain.com",
    TenantID: "your-tenant-id",
    AuthProvider: authProvider,
    RetryConfig: &nexus.RetryConfig{
        MaxRetries: 5,
        BackoffMs:  2000,
    },
}
\`\`\`

### Custom HTTP Client

\`\`\`go
import "net/http"

httpClient := &http.Client{
    Timeout: 60 * time.Second,
}

config := &nexus.ClientConfig{
    BaseURL:    "https://api.your-domain.com",
    TenantID:   "your-tenant-id",
    AuthProvider: authProvider,
    HTTPClient: httpClient,
}
\`\`\`

## API Reference

See the [API documentation](https://docs.your-domain.com/api) for detailed information about available endpoints and data structures.
`;

    await writeFile(path.join(outputDir, 'README.md'), readme);
  }
}
  sdk_version VARCHAR(50) NOT NULL,
  method_name VARCHAR(255) NOT NULL,
  call_count INTEGER DEFAULT 1,
  error_count INTEGER DEFAULT 0,
  avg_response_time DECIMAL(10,3),
  last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(workspace_id, sdk_language, sdk_version, method_name, date)
);

CREATE TABLE api_key_sdk_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
  sdk_language VARCHAR(50) NOT NULL,
  sdk_version VARCHAR(50) NOT NULL,
  request_count INTEGER DEFAULT 1,
  last_request_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_sdk_versions_language_version ON sdk_versions(language, version);
CREATE INDEX idx_sdk_downloads_workspace_id ON sdk_downloads(workspace_id);
CREATE INDEX idx_sdk_usage_metrics_workspace_date ON sdk_usage_metrics(workspace_id, date);
CREATE INDEX idx_api_key_sdk_usage_api_key_id ON api_key_sdk_usage(api_key_id);
```

### Prisma Schema Extensions

```prisma
model SdkVersion {
  id               String   @id @default(cuid())
  language         String
  version          String
  apiVersion       String   @map("api_version")
  generatedAt      DateTime @default(now()) @map("generated_at")
  downloadUrl      String?  @map("download_url")
  documentationUrl String?  @map("documentation_url")
  changelog        String?
  isStable         Boolean  @default(false) @map("is_stable")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  downloads SdkDownload[]

  @@map("sdk_versions")
}

model SdkDownload {
  id               String    @id @default(cuid())
  sdkVersionId     String    @map("sdk_version_id")
  workspaceId      String    @map("workspace_id")
  userId           String?   @map("user_id")
  downloadCount    Int       @default(1) @map("download_count")
  lastDownloadedAt DateTime  @default(now()) @map("last_downloaded_at")
  userAgent        String?   @map("user_agent")
  ipAddress        String?   @map("ip_address")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  sdkVersion SdkVersion @relation(fields: [sdkVersionId], references: [id], onDelete: Cascade)
  workspace  Workspace  @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  user       User?      @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("sdk_downloads")
}

model SdkUsageMetric {
  id              String   @id @default(cuid())
  workspaceId     String   @map("workspace_id")
  sdkLanguage     String   @map("sdk_language")
  sdkVersion      String   @map("sdk_version")
  methodName      String   @map("method_name")
  callCount       Int      @default(1) @map("call_count")
  errorCount      Int      @default(0) @map("error_count")
  avgResponseTime Decimal? @map("avg_response_time") @db.Decimal(10, 3)
  lastUsedAt      DateTime @default(now()) @map("last_used_at")
  date            DateTime @default(now()) @db.Date
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  workspace Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)

  @@unique([workspaceId, sdkLanguage, sdkVersion, methodName, date])
  @@map("sdk_usage_metrics")
}

model ApiKeySdkUsage {
  id            String   @id @default(cuid())
  apiKeyId      String   @map("api_key_id")
  sdkLanguage   String   @map("sdk_language")
  sdkVersion    String   @map("sdk_version")
  requestCount  Int      @default(1) @map("request_count")
  lastRequestAt DateTime @default(now()) @map("last_request_at")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  apiKey ApiKey @relation(fields: [apiKeyId], references: [id], onDelete: Cascade)

  @@map("api_key_sdk_usage")
}

// Add to existing models
model Workspace {
  // ... existing fields
  sdkDownloads    SdkDownload[]
  sdkUsageMetrics SdkUsageMetric[]
}

model User {
  // ... existing fields
  sdkDownloads SdkDownload[]
}

model ApiKey {
  // ... existing fields
  sdkUsage ApiKeySdkUsage[]
}
```

---

## List of Tasks to be Completed

### 1. Core SDK Generation Engine (Priority: Critical)

#### Multi-Language SDK Generator (`lib/sdk-generator/index.ts`)
```typescript
// Based on Context7 OpenAPI Generator patterns
import { OpenAPIV3 } from 'openapi-types';
import { execSync } from 'child_process';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';

export interface SDKGenerationConfig {
  languages: SDKLanguage[];
  apiSpec: OpenAPIV3.Document;
  outputDir: string;
  version: string;
  packageName: string;
  namespace: string;
  templates?: Record<string, string>;
}

export interface SDKLanguage {
  name: 'typescript' | 'python' | 'go' | 'php' | 'java' | 'csharp';
  version: string;
  config: Record<string, any>;
  templates?: string[];
}

export class MultiLanguageSDKGenerator {
  private config: SDKGenerationConfig;
  
  constructor(config: SDKGenerationConfig) {
    this.config = config;
  }
  
  async generateAllSDKs(): Promise<SDKGenerationResult[]> {
    const results: SDKGenerationResult[] = [];
    
    for (const language of this.config.languages) {
      try {
        const result = await this.generateSDK(language);
        results.push(result);
      } catch (error) {
        results.push({
          language: language.name,
          success: false,
          error: error.message,
          generatedAt: new Date()
        });
      }
    }
    
    return results;
  }
  
  private async generateSDK(language: SDKLanguage): Promise<SDKGenerationResult> {
    // Based on Context7 OpenAPI Generator patterns
    const generator = this.getLanguageGenerator(language);
    const outputPath = join(this.config.outputDir, language.name);
    
    // Create output directory
    mkdirSync(outputPath, { recursive: true });
    
    // Generate OpenAPI spec file
    const specPath = join(outputPath, 'openapi.json');
    writeFileSync(specPath, JSON.stringify(this.config.apiSpec, null, 2));
    
    // Generate SDK using OpenAPI Generator
    const command = this.buildGeneratorCommand(language, specPath, outputPath);
    execSync(command, { stdio: 'inherit' });
    
    // Post-process generated code
    await this.postProcessSDK(language, outputPath);
    
    // Generate additional files (README, examples, tests)
    await this.generateAdditionalFiles(language, outputPath);
    
    return {
      language: language.name,
      success: true,
      outputPath,
      generatedAt: new Date(),
      version: this.config.version
    };
  }
  
  private buildGeneratorCommand(language: SDKLanguage, specPath: string, outputPath: string): string {
    // Based on Context7 OpenAPI Generator CLI patterns
    const baseCommand = 'openapi-generator-cli generate';
    const generator = this.getLanguageGenerator(language);
    
    return `${baseCommand} -i ${specPath} -g ${generator} -o ${outputPath} ${this.getLanguageOptions(language)}`;
  }
  
  private getLanguageGenerator(language: SDKLanguage): string {
    const generators = {
      typescript: 'typescript-fetch',
      python: 'python',
      go: 'go',
      php: 'php',
      java: 'java',
      csharp: 'csharp'
    };
    
    return generators[language.name];
  }
  
  private getLanguageOptions(language: SDKLanguage): string {
    // Language-specific configuration options
    const options = {
      typescript: [
        '--additional-properties=npmName=@nexus-saas/typescript-sdk',
        '--additional-properties=npmVersion=' + this.config.version,
        '--additional-properties=supportsES6=true',
        '--additional-properties=typescriptThreePlus=true'
      ],
      python: [
        '--additional-properties=packageName=nexus_saas_sdk',
        '--additional-properties=packageVersion=' + this.config.version,
        '--additional-properties=packageUrl=https://github.com/nexus-saas/python-sdk'
      ],
      go: [
        '--additional-properties=packageName=nexussaas',
        '--additional-properties=packageVersion=' + this.config.version,
        '--additional-properties=packageUrl=github.com/nexus-saas/go-sdk'
      ],
      php: [
        '--additional-properties=packageName=NexusSaaS\\\\SDK',
        '--additional-properties=composerVendorName=nexus-saas',
        '--additional-properties=composerProjectName=php-sdk'
      ],
      java: [
        '--additional-properties=groupId=com.nexussaas',
        '--additional-properties=artifactId=nexus-saas-sdk',
        '--additional-properties=artifactVersion=' + this.config.version
      ],
      csharp: [
        '--additional-properties=packageName=NexusSaaS.SDK',
        '--additional-properties=packageVersion=' + this.config.version,
        '--additional-properties=packageCompany=NexusSaaS'
      ]
    };
    
    return options[language.name]?.join(' ') || '';
  }
  
  private async postProcessSDK(language: SDKLanguage, outputPath: string): Promise<void> {
    // Post-processing based on language-specific requirements
    switch (language.name) {
      case 'typescript':
        await this.postProcessTypeScript(outputPath);
        break;
      case 'python':
        await this.postProcessPython(outputPath);
        break;
      case 'go':
        await this.postProcessGo(outputPath);
        break;
      // Add other languages...
    }
  }
  
  private async postProcessTypeScript(outputPath: string): Promise<void> {
    // Add React hooks generation using GraphQL Code Generator patterns
    // Add multi-tenant workspace context
    // Add Better-Auth integration
    // Add WebSocket client support
  }
  
  private async generateAdditionalFiles(language: SDKLanguage, outputPath: string): Promise<void> {
    // Generate README with examples
    // Generate test files
    // Generate CI/CD configuration
    // Generate package configuration files
  }
}

export interface SDKGenerationResult {
  language: string;
  success: boolean;
  outputPath?: string;
  error?: string;
  generatedAt: Date;
  version?: string;
}
```

#### TypeScript SDK Generator (`lib/sdk-generator/typescript.ts`)
```typescript
// Based on Context7 GraphQL Code Generator patterns
import { CodegenConfig } from '@graphql-codegen/cli';
import { generate } from '@graphql-codegen/cli';

export class TypeScriptSDKGenerator {
  async generateTypeScriptSDK(config: SDKGenerationConfig): Promise<void> {
    // Generate base TypeScript client
    await this.generateBaseClient(config);
    
    // Generate React hooks using GraphQL Code Generator patterns
    await this.generateReactHooks(config);
    
    // Generate WebSocket client
    await this.generateWebSocketClient(config);
    
    // Generate multi-tenant context helpers
    await this.generateMultiTenantHelpers(config);
  }
  
  private async generateReactHooks(config: SDKGenerationConfig): Promise<void> {
    // Based on Context7 GraphQL Code Generator React Apollo patterns
    const codegenConfig: CodegenConfig = {
      schema: config.graphqlSchema,
      documents: './src/**/*.graphql',
      generates: {
        './src/generated/hooks.ts': {
          plugins: [
            'typescript',
            'typescript-operations',
            'typescript-react-apollo'
          ],
          config: {
            withHooks: true,
            withComponent: false,
            withHOC: false
          }
        }
      }
    };
    
    await generate(codegenConfig);
  }
  
  private generateBaseClient(config: SDKGenerationConfig): string {
    return `
// Generated TypeScript SDK for NEXUS SaaS API
// DO NOT EDIT - This file is auto-generated

import { WorkspaceContext } from './types';

export interface NexusSaasClientConfig {
  baseUrl: string;
  apiKey?: string;
  bearerToken?: string;
  workspaceId?: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

export class NexusSaasClient {
  private config: Required<NexusSaasClientConfig>;
  private workspaceContext?: WorkspaceContext;
  
  constructor(config: NexusSaasClientConfig) {
    this.config = {
      timeout: 10000,
      retryAttempts: 3,
      retryDelay: 1000,
      ...config
    };
  }
  
  // Multi-tenant workspace context management
  setWorkspaceContext(context: WorkspaceContext): void {
    this.workspaceContext = context;
  }
  
  getWorkspaceContext(): WorkspaceContext | undefined {
    return this.workspaceContext;
  }
  
  // Type-safe request method with retry logic
  private async request<T>(
    method: string,
    path: string,
    data?: any,
    options?: RequestOptions
  ): Promise<T> {
    const url = \`\${this.config.baseUrl}\${path}\`;
    const headers = this.buildHeaders(options);
    
    for (let attempt = 0; attempt <= this.config.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, {
          method,
          headers,
          body: data ? JSON.stringify(data) : undefined,
          signal: AbortSignal.timeout(this.config.timeout),
        });
        
        if (!response.ok) {
          throw new NexusSaasApiError(response.status, await response.text());
        }
        
        return await response.json();
      } catch (error) {
        if (attempt === this.config.retryAttempts || !this.isRetryableError(error)) {
          throw error;
        }
        
        await this.delay(this.config.retryDelay * Math.pow(2, attempt));
      }
    }
    
    throw new Error('Max retry attempts exceeded');
  }
  
  private buildHeaders(options?: RequestOptions): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'NexusSaaS-TypeScript-SDK/\${this.config.version}'
    };
    
    // Authentication
    if (this.config.apiKey) {
      headers['X-API-Key'] = this.config.apiKey;
    }
    
    if (this.config.bearerToken) {
      headers['Authorization'] = \`Bearer \${this.config.bearerToken}\`;
    }
    
    // Multi-tenant workspace context
    if (this.workspaceContext?.workspaceId) {
      headers['X-Workspace-ID'] = this.workspaceContext.workspaceId;
    }
    
    return { ...headers, ...options?.headers };
  }
  
  private isRetryableError(error: any): boolean {
    return error instanceof TypeError || // Network errors
           (error instanceof NexusSaasApiError && error.status >= 500);
  }
  
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  // Auto-generated API methods will be inserted here
  \${this.generateApiMethods(config)}
}

export class NexusSaasApiError extends Error {
  constructor(
    public status: number,
    public message: string,
    public response?: any
  ) {
    super(\`API Error \${status}: \${message}\`);
    this.name = 'NexusSaasApiError';
  }
}

// WebSocket client for real-time features
export class NexusSaasWebSocketClient {
  private ws?: WebSocket;
  private config: NexusSaasClientConfig;
  private eventHandlers: Map<string, Function[]> = new Map();
  
  constructor(config: NexusSaasClientConfig) {
    this.config = config;
  }
  
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      const wsUrl = this.config.baseUrl.replace('http', 'ws') + '/ws';
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        this.authenticate();
        resolve();
      };
      
      this.ws.onerror = (error) => reject(error);
      this.ws.onmessage = (event) => this.handleMessage(event);
    });
  }
  
  private authenticate(): void {
    if (this.ws && this.config.apiKey) {
      this.ws.send(JSON.stringify({
        type: 'auth',
        apiKey: this.config.apiKey,
        workspaceId: this.config.workspaceId
      }));
    }
  }
  
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }
  
  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      const handlers = this.eventHandlers.get(data.type) || [];
      handlers.forEach(handler => handler(data));
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }
}
`;
  }
}
```

### 2. Language-Specific Generators (Priority: High)

#### Python SDK Generator (`lib/sdk-generator/python.ts`)
```typescript
export class PythonSDKGenerator {
  generatePythonSDK(config: SDKGenerationConfig): string {
    return `
# Generated Python SDK for NEXUS SaaS API
# DO NOT EDIT - This file is auto-generated

import asyncio
import aiohttp
import json
from typing import Optional, Dict, Any, Union
from dataclasses import dataclass
from enum import Enum

@dataclass
class NexusSaasClientConfig:
    base_url: str
    api_key: Optional[str] = None
    bearer_token: Optional[str] = None
    workspace_id: Optional[str] = None
    timeout: int = 10
    retry_attempts: int = 3
    retry_delay: float = 1.0

@dataclass
class WorkspaceContext:
    workspace_id: str
    user_id: str
    permissions: list[str]

class NexusSaasApiError(Exception):
    def __init__(self, status: int, message: str, response: Optional[Any] = None):
        self.status = status
        self.message = message
        self.response = response
        super().__init__(f"API Error {status}: {message}")

class NexusSaasClient:
    def __init__(self, config: NexusSaasClientConfig):
        self.config = config
        self.workspace_context: Optional[WorkspaceContext] = None
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def set_workspace_context(self, context: WorkspaceContext) -> None:
        self.workspace_context = context
    
    def get_workspace_context(self) -> Optional[WorkspaceContext]:
        return self.workspace_context
    
    async def request(
        self,
        method: str,
        path: str,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Any:
        url = f"{self.config.base_url}{path}"
        request_headers = self._build_headers(headers)
        
        for attempt in range(self.config.retry_attempts + 1):
            try:
                async with self.session.request(
                    method,
                    url,
                    json=data,
                    headers=request_headers
                ) as response:
                    if not response.ok:
                        error_text = await response.text()
                        raise NexusSaasApiError(response.status, error_text)
                    
                    return await response.json()
            
            except (aiohttp.ClientError, asyncio.TimeoutError) as error:
                if attempt == self.config.retry_attempts or not self._is_retryable_error(error):
                    raise
                
                await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
        
        raise Exception("Max retry attempts exceeded")
    
    def _build_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        headers = {
            "Content-Type": "application/json",
            "User-Agent": f"NexusSaaS-Python-SDK/{self.config.version}"
        }
        
        if self.config.api_key:
            headers["X-API-Key"] = self.config.api_key
        
        if self.config.bearer_token:
            headers["Authorization"] = f"Bearer {self.config.bearer_token}"
        
        if self.workspace_context and self.workspace_context.workspace_id:
            headers["X-Workspace-ID"] = self.workspace_context.workspace_id
        
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    def _is_retryable_error(self, error: Exception) -> bool:
        return isinstance(error, (aiohttp.ClientError, asyncio.TimeoutError))
    
    # Auto-generated API methods will be inserted here
    ${this.generatePythonApiMethods(config)}

# Synchronous client wrapper
class NexusSaasSyncClient:
    def __init__(self, config: NexusSaasClientConfig):
        self.async_client = NexusSaasClient(config)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
    
    def _run_async(self, coro):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
    
    # Synchronous wrapper methods
    def set_workspace_context(self, context: WorkspaceContext) -> None:
        self.async_client.set_workspace_context(context)
    
    # Auto-generated sync API methods will be inserted here
`;
  }
}
```

#### Go SDK Generator (`lib/sdk-generator/go.ts`)
```typescript
export class GoSDKGenerator {
  generateGoSDK(config: SDKGenerationConfig): string {
    return `
// Generated Go SDK for NEXUS SaaS API
// DO NOT EDIT - This file is auto-generated

package nexussaas

import (
    "bytes"
    "context"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "time"
)

// ClientConfig holds the configuration for the NEXUS SaaS client
type ClientConfig struct {
    BaseURL       string
    APIKey        string
    BearerToken   string
    WorkspaceID   string
    Timeout       time.Duration
    RetryAttempts int
    RetryDelay    time.Duration
    HTTPClient    *http.Client
}

// WorkspaceContext represents the multi-tenant workspace context
type WorkspaceContext struct {
    WorkspaceID string   \`json:"workspace_id"\`
    UserID      string   \`json:"user_id"\`
    Permissions []string \`json:"permissions"\`
}

// APIError represents an error returned by the API
type APIError struct {
    Status   int    \`json:"status"\`
    Message  string \`json:"message"\`
    Response []byte \`json:"response,omitempty"\`
}

func (e *APIError) Error() string {
    return fmt.Sprintf("API Error %d: %s", e.Status, e.Message)
}

// Client is the main client for interacting with the NEXUS SaaS API
type Client struct {
    config           ClientConfig
    workspaceContext *WorkspaceContext
    httpClient       *http.Client
}

// NewClient creates a new NEXUS SaaS client
func NewClient(config ClientConfig) *Client {
    if config.Timeout == 0 {
        config.Timeout = 10 * time.Second
    }
    if config.RetryAttempts == 0 {
        config.RetryAttempts = 3
    }
    if config.RetryDelay == 0 {
        config.RetryDelay = time.Second
    }
    
    httpClient := config.HTTPClient
    if httpClient == nil {
        httpClient = &http.Client{
            Timeout: config.Timeout,
        }
    }
    
    return &Client{
        config:     config,
        httpClient: httpClient,
    }
}

// SetWorkspaceContext sets the workspace context for multi-tenant operations
func (c *Client) SetWorkspaceContext(ctx WorkspaceContext) {
    c.workspaceContext = &ctx
}

// GetWorkspaceContext returns the current workspace context
func (c *Client) GetWorkspaceContext() *WorkspaceContext {
    return c.workspaceContext
}

// Request makes a request to the API with retry logic and proper error handling
func (c *Client) Request(ctx context.Context, method, path string, body interface{}, result interface{}) error {
    url := c.config.BaseURL + path
    
    var reqBody io.Reader
    if body != nil {
        jsonBody, err := json.Marshal(body)
        if err != nil {
            return fmt.Errorf("failed to marshal request body: %w", err)
        }
        reqBody = bytes.NewReader(jsonBody)
    }
    
    for attempt := 0; attempt <= c.config.RetryAttempts; attempt++ {
        req, err := http.NewRequestWithContext(ctx, method, url, reqBody)
        if err != nil {
            return fmt.Errorf("failed to create request: %w", err)
        }
        
        c.setHeaders(req)
        
        resp, err := c.httpClient.Do(req)
        if err != nil {
            if attempt == c.config.RetryAttempts || !c.isRetryableError(err) {
                return fmt.Errorf("request failed: %w", err)
            }
            
            select {
            case <-ctx.Done():
                return ctx.Err()
            case <-time.After(c.config.RetryDelay * time.Duration(1<<attempt)):
                continue
            }
        }
        defer resp.Body.Close()
        
        respBody, err := io.ReadAll(resp.Body)
        if err != nil {
            return fmt.Errorf("failed to read response body: %w", err)
        }
        
        if resp.StatusCode >= 400 {
            return &APIError{
                Status:   resp.StatusCode,
                Message:  string(respBody),
                Response: respBody,
            }
        }
        
        if result != nil {
            if err := json.Unmarshal(respBody, result); err != nil {
                return fmt.Errorf("failed to unmarshal response: %w", err)
            }
        }
        
        return nil
    }
    
    return fmt.Errorf("max retry attempts exceeded")
}

func (c *Client) setHeaders(req *http.Request) {
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("User-Agent", fmt.Sprintf("NexusSaaS-Go-SDK/%s", Version))
    
    if c.config.APIKey != "" {
        req.Header.Set("X-API-Key", c.config.APIKey)
    }
    
    if c.config.BearerToken != "" {
        req.Header.Set("Authorization", "Bearer "+c.config.BearerToken)
    }
    
    if c.workspaceContext != nil && c.workspaceContext.WorkspaceID != "" {
        req.Header.Set("X-Workspace-ID", c.workspaceContext.WorkspaceID)
    }
}

func (c *Client) isRetryableError(err error) bool {
    // Network errors and timeouts are retryable
    return true // Simplified for example
}

// Auto-generated API methods will be inserted here
${this.generateGoApiMethods(config)}

// Version of the SDK
const Version = "${config.version}"
`;
  }
}
```

### 3. WebSocket Client Generation (Priority: High)

#### WebSocket Client Generator (`lib/sdk-generator/websocket.ts`)
```typescript
export class WebSocketClientGenerator {
  generateTypeScriptWebSocketClient(): string {
    return `
// WebSocket client for real-time NEXUS SaaS features
export interface WebSocketClientConfig {
  baseUrl: string;
  apiKey?: string;
  workspaceId?: string;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
  workspaceId?: string;
}

export class NexusSaasWebSocketClient extends EventTarget {
  private ws?: WebSocket;
  private config: Required<WebSocketClientConfig>;
  private reconnectCount = 0;
  private isConnecting = false;
  private isAuthenticated = false;
  
  constructor(config: WebSocketClientConfig) {
    super();
    this.config = {
      reconnectAttempts: 5,
      reconnectDelay: 1000,
      ...config
    };
  }
  
  async connect(): Promise<void> {
    if (this.isConnecting || this.isConnected()) {
      return;
    }
    
    this.isConnecting = true;
    
    return new Promise((resolve, reject) => {
      const wsUrl = this.config.baseUrl.replace(/^http/, 'ws') + '/ws';
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        this.isConnecting = false;
        this.reconnectCount = 0;
        this.authenticate();
        this.dispatchEvent(new CustomEvent('connected'));
        resolve();
      };
      
      this.ws.onclose = (event) => {
        this.isConnecting = false;
        this.isAuthenticated = false;
        this.handleDisconnection(event);
      };
      
      this.ws.onerror = (error) => {
        this.isConnecting = false;
        this.dispatchEvent(new CustomEvent('error', { detail: error }));
        reject(error);
      };
      
      this.ws.onmessage = (event) => {
        this.handleMessage(event);
      };
    });
  }
  
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = undefined;
    }
  }
  
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
  
  send(type: string, data: any): void {
    if (!this.isConnected() || !this.isAuthenticated) {
      throw new Error('WebSocket not connected or not authenticated');
    }
    
    const message: WebSocketMessage = {
      type,
      data,
      timestamp: Date.now(),
      workspaceId: this.config.workspaceId
    };
    
    this.ws!.send(JSON.stringify(message));
  }
  
  // Subscription methods for real-time features
  subscribeToWorkspaceEvents(): void {
    this.send('subscribe', { channel: 'workspace_events' });
  }
  
  subscribeToUserNotifications(): void {
    this.send('subscribe', { channel: 'user_notifications' });
  }
  
  subscribeToSystemAlerts(): void {
    this.send('subscribe', { channel: 'system_alerts' });
  }
  
  private authenticate(): void {
    if (!this.ws || !this.config.apiKey) return;
    
    this.send('auth', {
      apiKey: this.config.apiKey,
      workspaceId: this.config.workspaceId
    });
  }
  
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      if (message.type === 'auth_success') {
        this.isAuthenticated = true;
        this.dispatchEvent(new CustomEvent('authenticated'));
        return;
      }
      
      if (message.type === 'auth_error') {
        this.dispatchEvent(new CustomEvent('auth_error', { detail: message.data }));
        return;
      }
      
      // Dispatch typed events for different message types
      this.dispatchEvent(new CustomEvent(message.type, { detail: message }));
      this.dispatchEvent(new CustomEvent('message', { detail: message }));
      
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
      this.dispatchEvent(new CustomEvent('parse_error', { detail: error }));
    }
  }
  
  private handleDisconnection(event: CloseEvent): void {
    this.dispatchEvent(new CustomEvent('disconnected', { detail: event }));
    
    // Auto-reconnect logic
    if (event.code !== 1000 && this.reconnectCount < this.config.reconnectAttempts) {
      const delay = this.config.reconnectDelay * Math.pow(2, this.reconnectCount);
      this.reconnectCount++;
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }, delay);
    }
  }
}

// React hook for WebSocket integration
export function useNexusSaasWebSocket(config: WebSocketClientConfig) {
  const [client] = useState(() => new NexusSaasWebSocketClient(config));
  const [isConnected, setIsConnected] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  useEffect(() => {
    const handleConnected = () => setIsConnected(true);
    const handleDisconnected = () => {
      setIsConnected(false);
      setIsAuthenticated(false);
    };
    const handleAuthenticated = () => setIsAuthenticated(true);
    
    client.addEventListener('connected', handleConnected);
    client.addEventListener('disconnected', handleDisconnected);
    client.addEventListener('authenticated', handleAuthenticated);
    
    client.connect();
    
    return () => {
      client.removeEventListener('connected', handleConnected);
      client.removeEventListener('disconnected', handleDisconnected);
      client.removeEventListener('authenticated', handleAuthenticated);
      client.disconnect();
    };
  }, [client]);
  
  return {
    client,
    isConnected,
    isAuthenticated,
    send: client.send.bind(client),
    subscribe: {
      workspaceEvents: () => client.subscribeToWorkspaceEvents(),
      userNotifications: () => client.subscribeToUserNotifications(),
      systemAlerts: () => client.subscribeToSystemAlerts()
    }
  };
}
`;
  }
}
```

### 4. SDK Management API Routes (Priority: High)

#### SDK Generation API (`app/api/sdk/generate/route.ts`)
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { MultiLanguageSDKGenerator } from '@/lib/sdk-generator';
import { getOpenAPISpec } from '@/lib/api-docs';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { languages, version, includeExamples } = await request.json();

    // Validate request
    if (!languages || !Array.isArray(languages) || languages.length === 0) {
      return NextResponse.json(
        { error: 'Languages array is required' },
        { status: 400 }
      );
    }

    // Get current OpenAPI specification
    const apiSpec = await getOpenAPISpec();

    // Generate SDKs for requested languages
    const generator = new MultiLanguageSDKGenerator({
      languages: languages.map(lang => ({
        name: lang,
        version: version || '1.0.0',
        config: {}
      })),
      apiSpec,
      outputDir: './generated-sdks',
      version: version || '1.0.0',
      packageName: 'nexus-saas-sdk',
      namespace: 'NexusSaaS'
    });

    const results = await generator.generateAllSDKs();

    // Store generation results in database
    for (const result of results) {
      if (result.success) {
        await prisma.sdkVersion.create({
          data: {
            language: result.language,
            version: result.version!,
            apiVersion: apiSpec.info.version,
            downloadUrl: `/api/sdk/download/${result.language}/${result.version}`,
            documentationUrl: `/docs/sdk/${result.language}`,
            isStable: true
          }
        });
      }
    }

    return NextResponse.json({
      success: true,
      results,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('SDK generation failed:', error);
    return NextResponse.json(
      { error: 'SDK generation failed' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get available SDK versions
    const sdkVersions = await prisma.sdkVersion.findMany({
      orderBy: [
        { language: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    // Group by language
    const sdksByLanguage = sdkVersions.reduce((acc, sdk) => {
      if (!acc[sdk.language]) {
        acc[sdk.language] = [];
      }
      acc[sdk.language].push(sdk);
      return acc;
    }, {} as Record<string, typeof sdkVersions>);

    return NextResponse.json({
      sdks: sdksByLanguage,
      supportedLanguages: [
        'typescript',
        'python',
        'go',
        'php',
        'java',
        'csharp'
      ]
    });

  } catch (error) {
    console.error('Failed to fetch SDK versions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch SDK versions' },
      { status: 500 }
    );
  }
}
```

#### SDK Download API (`app/api/sdk/download/[language]/[version]/route.ts`)
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { createReadStream, existsSync } from 'fs';
import { join } from 'path';
import archiver from 'archiver';

interface RouteParams {
  params: {
    language: string;
    version: string;
  };
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { language, version } = params;

    // Find SDK version
    const sdkVersion = await prisma.sdkVersion.findFirst({
      where: {
        language,
        version
      }
    });

    if (!sdkVersion) {
      return NextResponse.json(
        { error: 'SDK version not found' },
        { status: 404 }
      );
    }

    // Check if SDK files exist
    const sdkPath = join(process.cwd(), 'generated-sdks', language, version);
    if (!existsSync(sdkPath)) {
      return NextResponse.json(
        { error: 'SDK files not found' },
        { status: 404 }
      );
    }

    // Track download
    const workspace = await prisma.workspace.findFirst({
      where: {
        members: {
          some: {
            userId: session.user.id
          }
        }
      }
    });

    if (workspace) {
      await prisma.sdkDownload.upsert({
        where: {
          sdkVersionId_workspaceId: {
            sdkVersionId: sdkVersion.id,
            workspaceId: workspace.id
          }
        },
        update: {
          downloadCount: {
            increment: 1
          },
          lastDownloadedAt: new Date(),
          userAgent: request.headers.get('user-agent') || undefined,
          ipAddress: request.ip || undefined
        },
        create: {
          sdkVersionId: sdkVersion.id,
          workspaceId: workspace.id,
          userId: session.user.id,
          downloadCount: 1,
          userAgent: request.headers.get('user-agent') || undefined,
          ipAddress: request.ip || undefined
        }
      });
    }

    // Create ZIP archive
    const archive = archiver('zip', {
      zlib: { level: 9 }
    });

    // Set response headers
    const filename = `nexus-saas-${language}-sdk-${version}.zip`;
    const headers = new Headers({
      'Content-Type': 'application/zip',
      'Content-Disposition': `attachment; filename="${filename}"`,
      'Cache-Control': 'public, max-age=3600'
    });

    // Create readable stream
    const stream = new ReadableStream({
      start(controller) {
        archive.on('data', (chunk) => {
          controller.enqueue(new Uint8Array(chunk));
        });

        archive.on('end', () => {
          controller.close();
        });

        archive.on('error', (err) => {
          controller.error(err);
        });

        // Add SDK files to archive
        archive.directory(sdkPath, false);
        archive.finalize();
      }
    });

    return new Response(stream, { headers });

  } catch (error) {
    console.error('SDK download failed:', error);
    return NextResponse.json(
      { error: 'SDK download failed' },
      { status: 500 }
    );
  }
}
```

### 5. SDK Documentation Generator (Priority: Medium)

#### Documentation Generator (`lib/sdk-generator/docs.ts`)
```typescript
export class SDKDocumentationGenerator {
  async generateDocumentation(language: string, sdkPath: string): Promise<void> {
    switch (language) {
      case 'typescript':
        await this.generateTypeScriptDocs(sdkPath);
        break;
      case 'python':
        await this.generatePythonDocs(sdkPath);
        break;
      case 'go':
        await this.generateGoDocs(sdkPath);
        break;
      // Add other languages...
    }
  }

  private async generateTypeScriptDocs(sdkPath: string): Promise<void> {
    const readmeContent = `
# NEXUS SaaS TypeScript SDK

A type-safe TypeScript/JavaScript client for the NEXUS SaaS API.

## Installation

\`\`\`bash
npm install @nexus-saas/typescript-sdk
# or
yarn add @nexus-saas/typescript-sdk
# or
pnpm add @nexus-saas/typescript-sdk
\`\`\`

## Quick Start

\`\`\`typescript
import { NexusSaasClient } from '@nexus-saas/typescript-sdk';

// Initialize the client
const client = new NexusSaasClient({
  baseUrl: 'https://api.nexus-saas.com',
  apiKey: 'your-api-key-here',
  workspaceId: 'your-workspace-id'
});

// Set workspace context for multi-tenant operations
client.setWorkspaceContext({
  workspaceId: 'workspace-123',
  userId: 'user-456',
  permissions: ['read', 'write']
});

// Make API calls
try {
  const users = await client.users.list();
  console.log('Users:', users);
} catch (error) {
  if (error instanceof NexusSaasApiError) {
    console.error('API Error:', error.status, error.message);
  }
}
\`\`\`

## React Integration

\`\`\`typescript
import { useNexusSaasClient } from '@nexus-saas/typescript-sdk/react';

function UsersList() {
  const { data: users, loading, error } = useNexusSaasClient(
    client => client.users.list()
  );

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <ul>
      {users?.map(user => (
        <li key={user.id}>{user.name}</li>
      ))}
    </ul>
  );
}
\`\`\`

## WebSocket Real-time Features

\`\`\`typescript
import { useNexusSaasWebSocket } from '@nexus-saas/typescript-sdk/websocket';

function RealTimeNotifications() {
  const { client, isConnected, isAuthenticated } = useNexusSaasWebSocket({
    baseUrl: 'wss://api.nexus-saas.com',
    apiKey: 'your-api-key',
    workspaceId: 'your-workspace-id'
  });

  useEffect(() => {
    if (isAuthenticated) {
      client.subscribeToUserNotifications();
      
      client.addEventListener('notification', (event) => {
        console.log('New notification:', event.detail);
      });
    }
  }, [isAuthenticated]);

  return (
    <div>
      Status: {isConnected ? 'Connected' : 'Disconnected'}
    </div>
  );
}
\`\`\`

## Configuration Options

\`\`\`typescript
interface NexusSaasClientConfig {
  baseUrl: string;           // API base URL
  apiKey?: string;           // API key for authentication
  bearerToken?: string;      // Bearer token for user authentication
  workspaceId?: string;      // Default workspace ID
  timeout?: number;          // Request timeout in milliseconds (default: 10000)
  retryAttempts?: number;    // Number of retry attempts (default: 3)
  retryDelay?: number;       // Delay between retries in milliseconds (default: 1000)
}
\`\`\`

## Error Handling

\`\`\`typescript
import { NexusSaasApiError } from '@nexus-saas/typescript-sdk';

try {
  await client.users.create({ name: 'John Doe' });
} catch (error) {
  if (error instanceof NexusSaasApiError) {
    switch (error.status) {
      case 400:
        console.error('Bad request:', error.message);
        break;
      case 401:
        console.error('Unauthorized - check your API key');
        break;
      case 403:
        console.error('Forbidden - insufficient permissions');
        break;
      case 429:
        console.error('Rate limited - please slow down');
        break;
      default:
        console.error('API error:', error.status, error.message);
    }
  } else {
    console.error('Network or other error:', error);
  }
}
\`\`\`

## Multi-Tenant Support

The SDK automatically handles multi-tenant operations when you set a workspace context:

\`\`\`typescript
// Set workspace context
client.setWorkspaceContext({
  workspaceId: 'workspace-123',
  userId: 'user-456',
  permissions: ['read', 'write', 'admin']
});

// All subsequent API calls will include the workspace context
const workspaceUsers = await client.users.list(); // Only users in workspace-123
\`\`\`

## API Reference

### Users API

\`\`\`typescript
// List users
const users = await client.users.list({
  page: 1,
  limit: 10,
  search: 'john'
});

// Get user by ID
const user = await client.users.get('user-123');

// Create user
const newUser = await client.users.create({
  name: 'John Doe',
  email: '<EMAIL>'
});

// Update user
const updatedUser = await client.users.update('user-123', {
  name: 'John Smith'
});

// Delete user
await client.users.delete('user-123');
\`\`\`

### Workspaces API

\`\`\`typescript
// List workspaces
const workspaces = await client.workspaces.list();

// Get workspace by ID
const workspace = await client.workspaces.get('workspace-123');

// Create workspace
const newWorkspace = await client.workspaces.create({
  name: 'My Workspace',
  plan: 'pro'
});
\`\`\`

## TypeScript Support

The SDK is written in TypeScript and provides full type safety:

\`\`\