# Advanced RBAC System - Implementation PRP

**Generated by**: Nexus-Master Agent 🧙‍♂️  
**Date**: July 18, 2025  
**Status**: Implementation Ready  
**Sprint**: 9-10 (Core Features Phase)  
**Estimated Complexity**: High (Advanced access control system)

---

## 🎯 **FEATURE OVERVIEW**

Implement a comprehensive Role-Based Access Control (RBAC) system with fine-grained permissions, hierarchical roles, and dynamic permission management. This system builds upon Better Auth's powerful access control foundation to provide enterprise-grade security and flexible permission management.

### **Key Capabilities**
- **Fine-Grained Permissions**: Resource-based permissions with granular actions
- **Hierarchical Role System**: Multi-level roles with inheritance
- **Dynamic Permission Assignment**: Runtime permission management
- **Context-Aware Access Control**: Organization, team, and resource-specific permissions
- **Role Templates**: Pre-defined role configurations for common use cases
- **Permission Audit Trail**: Complete logging of permission changes

---

## 📋 **IMPLEMENTATION REQUIREMENTS**

### **Core RBAC Components**

#### **1. Permission System Architecture**
- **Resource-Based Permissions**: Define permissions per resource type
- **Action-Based Control**: Granular actions (create, read, update, delete, share, etc.)
- **Permission Inheritance**: Hierarchical permission structures
- **Dynamic Permission Evaluation**: Real-time permission checking

#### **2. Role Management System**
- **Role Hierarchy**: Parent-child role relationships
- **Role Templates**: Pre-configured role sets for common scenarios
- **Custom Role Creation**: User-defined roles with specific permissions
- **Role Assignment**: Multiple roles per user with context-aware activation

#### **3. Access Control Engine**
- **Permission Evaluation**: Fast, cached permission checking
- **Context-Aware Decisions**: Organization, team, and resource-specific access
- **Policy Engine**: Rule-based permission evaluation
- **Audit Logging**: Complete permission usage tracking

---

## 🏗️ **TECHNICAL ARCHITECTURE**

### **Permission Model Structure**

```typescript
// Permission Statement Definition
interface PermissionStatement {
  // Core Resources
  organization: ["create", "read", "update", "delete", "manage"]
  team: ["create", "read", "update", "delete", "join", "leave", "manage"]
  member: ["create", "read", "update", "delete", "invite", "remove", "manage"]
  project: ["create", "read", "update", "delete", "share", "collaborate", "manage"]
  
  // Content Management
  document: ["create", "read", "update", "delete", "share", "review", "publish"]
  file: ["create", "read", "update", "delete", "share", "download", "upload"]
  
  // System Resources
  user: ["create", "read", "update", "delete", "ban", "impersonate", "manage"]
  session: ["read", "create", "revoke", "delete", "manage"]
  
  // Billing & Subscription
  subscription: ["read", "create", "update", "cancel", "manage"]
  invoice: ["read", "create", "update", "delete", "manage"]
  
  // Analytics & Reporting
  analytics: ["read", "create", "export", "manage"]
  report: ["read", "create", "update", "delete", "schedule", "share", "manage"]
  
  // Integration & API
  integration: ["read", "create", "update", "delete", "manage"]
  webhook: ["read", "create", "update", "delete", "manage"]
  api: ["read", "create", "update", "delete", "manage"]
}
```

### **Role Hierarchy System**

```typescript
// Role Hierarchy Definition
interface RoleHierarchy {
  // System-Level Roles
  superAdmin: {
    permissions: "all",
    description: "Full system access",
    hierarchy: 0
  }
  
  // Organization-Level Roles
  owner: {
    permissions: {
      organization: ["create", "read", "update", "delete", "manage"],
      team: ["create", "read", "update", "delete", "manage"],
      member: ["create", "read", "update", "delete", "invite", "remove", "manage"],
      project: ["create", "read", "update", "delete", "share", "collaborate", "manage"],
      // ... all organization resources
    },
    description: "Organization owner with full control",
    hierarchy: 1
  }
  
  admin: {
    permissions: {
      organization: ["read", "update", "manage"],
      team: ["create", "read", "update", "delete", "manage"],
      member: ["create", "read", "update", "invite", "remove", "manage"],
      project: ["create", "read", "update", "delete", "share", "collaborate", "manage"],
      // ... most organization resources except critical ones
    },
    description: "Organization administrator",
    hierarchy: 2
  }
  
  // Team-Level Roles
  teamLead: {
    permissions: {
      team: ["read", "update", "manage"],
      member: ["read", "invite", "manage"],
      project: ["create", "read", "update", "delete", "share", "collaborate", "manage"],
      // ... team-specific resources
    },
    description: "Team leadership role",
    hierarchy: 3
  }
  
  teamMember: {
    permissions: {
      team: ["read"],
      member: ["read"],
      project: ["create", "read", "update", "share", "collaborate"],
      // ... member-level resources
    },
    description: "Standard team member",
    hierarchy: 4
  }
  
  // Project-Level Roles
  projectManager: {
    permissions: {
      project: ["read", "update", "share", "collaborate", "manage"],
      document: ["create", "read", "update", "delete", "share", "review", "publish"],
      file: ["create", "read", "update", "delete", "share", "download", "upload"],
    },
    description: "Project management role",
    hierarchy: 3
  }
  
  contributor: {
    permissions: {
      project: ["read", "collaborate"],
      document: ["read", "create", "update", "review"],
      file: ["read", "create", "update", "download", "upload"],
    },
    description: "Project contributor",
    hierarchy: 4
  }
  
  viewer: {
    permissions: {
      project: ["read"],
      document: ["read"],
      file: ["read", "download"],
    },
    description: "Read-only access",
    hierarchy: 5
  }
}
```

### **Permission Context System**

```typescript
// Permission Context Definition
interface PermissionContext {
  // Context Types
  global: {
    userId: string,
    roles: string[],
    permissions: PermissionStatement
  }
  
  organization: {
    organizationId: string,
    userId: string,
    roles: string[],
    permissions: PermissionStatement,
    membershipLevel: "owner" | "admin" | "member"
  }
  
  team: {
    teamId: string,
    organizationId: string,
    userId: string,
    roles: string[],
    permissions: PermissionStatement,
    membershipLevel: "lead" | "member"
  }
  
  project: {
    projectId: string,
    teamId?: string,
    organizationId: string,
    userId: string,
    roles: string[],
    permissions: PermissionStatement,
    accessLevel: "owner" | "manager" | "contributor" | "viewer"
  }
  
  resource: {
    resourceId: string,
    resourceType: keyof PermissionStatement,
    context: "global" | "organization" | "team" | "project",
    contextId: string,
    userId: string,
    roles: string[],
    permissions: PermissionStatement
  }
}
```

---

## 🛠️ **IMPLEMENTATION COMPONENTS**

### **1. Better Auth RBAC Configuration**

```typescript
// lib/auth/rbac-config.ts
import { createAccessControl } from "better-auth/plugins/access"
import { defaultStatements } from "better-auth/plugins/organization/access"

// Define comprehensive permission statements
export const rbacStatement = {
  // Merge default organization permissions
  ...defaultStatements,
  
  // Core Resources
  organization: ["create", "read", "update", "delete", "manage"],
  team: ["create", "read", "update", "delete", "join", "leave", "manage"],
  member: ["create", "read", "update", "delete", "invite", "remove", "manage"],
  project: ["create", "read", "update", "delete", "share", "collaborate", "manage"],
  
  // Content Management
  document: ["create", "read", "update", "delete", "share", "review", "publish"],
  file: ["create", "read", "update", "delete", "share", "download", "upload"],
  
  // System Resources
  user: ["create", "read", "update", "delete", "ban", "impersonate", "manage"],
  session: ["read", "create", "revoke", "delete", "manage"],
  
  // Billing & Subscription
  subscription: ["read", "create", "update", "cancel", "manage"],
  invoice: ["read", "create", "update", "delete", "manage"],
  
  // Analytics & Reporting
  analytics: ["read", "create", "export", "manage"],
  report: ["read", "create", "update", "delete", "schedule", "share", "manage"],
  
  // Integration & API
  integration: ["read", "create", "update", "delete", "manage"],
  webhook: ["read", "create", "update", "delete", "manage"],
  api: ["read", "create", "update", "delete", "manage"],
} as const

// Create access control instance
export const rbacController = createAccessControl(rbacStatement)

// Define hierarchical roles
export const roles = {
  // System-Level Roles
  superAdmin: rbacController.newRole({
    // Full access to all resources
    organization: ["create", "read", "update", "delete", "manage"],
    team: ["create", "read", "update", "delete", "join", "leave", "manage"],
    member: ["create", "read", "update", "delete", "invite", "remove", "manage"],
    project: ["create", "read", "update", "delete", "share", "collaborate", "manage"],
    document: ["create", "read", "update", "delete", "share", "review", "publish"],
    file: ["create", "read", "update", "delete", "share", "download", "upload"],
    user: ["create", "read", "update", "delete", "ban", "impersonate", "manage"],
    session: ["read", "create", "revoke", "delete", "manage"],
    subscription: ["read", "create", "update", "cancel", "manage"],
    invoice: ["read", "create", "update", "delete", "manage"],
    analytics: ["read", "create", "export", "manage"],
    report: ["read", "create", "update", "delete", "schedule", "share", "manage"],
    integration: ["read", "create", "update", "delete", "manage"],
    webhook: ["read", "create", "update", "delete", "manage"],
    api: ["read", "create", "update", "delete", "manage"],
  }),
  
  // Organization-Level Roles
  owner: rbacController.newRole({
    organization: ["create", "read", "update", "delete", "manage"],
    team: ["create", "read", "update", "delete", "manage"],
    member: ["create", "read", "update", "delete", "invite", "remove", "manage"],
    project: ["create", "read", "update", "delete", "share", "collaborate", "manage"],
    document: ["create", "read", "update", "delete", "share", "review", "publish"],
    file: ["create", "read", "update", "delete", "share", "download", "upload"],
    subscription: ["read", "create", "update", "cancel", "manage"],
    invoice: ["read", "create", "update", "delete", "manage"],
    analytics: ["read", "create", "export", "manage"],
    report: ["read", "create", "update", "delete", "schedule", "share", "manage"],
    integration: ["read", "create", "update", "delete", "manage"],
    webhook: ["read", "create", "update", "delete", "manage"],
    api: ["read", "create", "update", "delete", "manage"],
  }),
  
  admin: rbacController.newRole({
    organization: ["read", "update", "manage"],
    team: ["create", "read", "update", "delete", "manage"],
    member: ["create", "read", "update", "invite", "remove", "manage"],
    project: ["create", "read", "update", "delete", "share", "collaborate", "manage"],
    document: ["create", "read", "update", "delete", "share", "review", "publish"],
    file: ["create", "read", "update", "delete", "share", "download", "upload"],
    subscription: ["read", "update", "manage"],
    invoice: ["read", "create", "update", "manage"],
    analytics: ["read", "create", "export", "manage"],
    report: ["read", "create", "update", "delete", "schedule", "share", "manage"],
    integration: ["read", "create", "update", "delete", "manage"],
    webhook: ["read", "create", "update", "delete", "manage"],
    api: ["read", "create", "update", "manage"],
  }),
  
  // Team-Level Roles
  teamLead: rbacController.newRole({
    team: ["read", "update", "manage"],
    member: ["read", "invite", "manage"],
    project: ["create", "read", "update", "delete", "share", "collaborate", "manage"],
    document: ["create", "read", "update", "delete", "share", "review", "publish"],
    file: ["create", "read", "update", "delete", "share", "download", "upload"],
    analytics: ["read", "create", "export"],
    report: ["read", "create", "update", "delete", "schedule", "share"],
  }),
  
  teamMember: rbacController.newRole({
    team: ["read"],
    member: ["read"],
    project: ["create", "read", "update", "share", "collaborate"],
    document: ["create", "read", "update", "share", "review"],
    file: ["create", "read", "update", "share", "download", "upload"],
    analytics: ["read"],
    report: ["read", "create", "update"],
  }),
  
  // Project-Level Roles
  projectManager: rbacController.newRole({
    project: ["read", "update", "share", "collaborate", "manage"],
    document: ["create", "read", "update", "delete", "share", "review", "publish"],
    file: ["create", "read", "update", "delete", "share", "download", "upload"],
    analytics: ["read", "create", "export"],
    report: ["read", "create", "update", "delete", "schedule", "share"],
  }),
  
  contributor: rbacController.newRole({
    project: ["read", "collaborate"],
    document: ["read", "create", "update", "review"],
    file: ["read", "create", "update", "download", "upload"],
    analytics: ["read"],
    report: ["read", "create", "update"],
  }),
  
  viewer: rbacController.newRole({
    project: ["read"],
    document: ["read"],
    file: ["read", "download"],
    analytics: ["read"],
    report: ["read"],
  }),
}

// Role hierarchy levels (lower number = higher authority)
export const roleHierarchy = {
  superAdmin: 0,
  owner: 1,
  admin: 2,
  teamLead: 3,
  projectManager: 3,
  teamMember: 4,
  contributor: 4,
  viewer: 5,
} as const

// Role templates for common scenarios
export const roleTemplates = {
  // Enterprise Templates
  enterprise: {
    ceo: ["owner"],
    cto: ["admin"],
    teamLead: ["teamLead"],
    developer: ["teamMember", "contributor"],
    designer: ["teamMember", "contributor"],
    analyst: ["viewer", "analytics"],
  },
  
  // Agency Templates
  agency: {
    owner: ["owner"],
    accountManager: ["admin"],
    projectManager: ["projectManager"],
    creative: ["contributor"],
    intern: ["viewer"],
  },
  
  // SaaS Templates
  saas: {
    founder: ["owner"],
    manager: ["admin"],
    developer: ["teamMember", "contributor"],
    support: ["viewer", "member"],
    customer: ["viewer"],
  },
}
```

### **2. Permission Service Implementation**

```typescript
// lib/services/permission-service.ts
import { rbacController, roles, roleHierarchy } from "@/lib/auth/rbac-config"
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"

export interface PermissionContext {
  userId: string
  organizationId?: string
  teamId?: string
  projectId?: string
  resourceId?: string
  resourceType?: string
}

export interface PermissionCheck {
  resource: string
  actions: string[]
  context?: PermissionContext
}

export class PermissionService {
  /**
   * Check if user has specific permissions in given context
   */
  async hasPermission(
    userId: string,
    check: PermissionCheck,
    context?: PermissionContext
  ): Promise<boolean> {
    try {
      // Get user's roles in the specified context
      const userRoles = await this.getUserRoles(userId, context)
      
      // Check each role for required permissions
      for (const role of userRoles) {
        const rolePermissions = this.getRolePermissions(role)
        
        if (this.checkRolePermissions(rolePermissions, check)) {
          return true
        }
      }
      
      return false
    } catch (error) {
      console.error("Permission check failed:", error)
      return false
    }
  }
  
  /**
   * Get all roles for a user in a specific context
   */
  async getUserRoles(
    userId: string,
    context?: PermissionContext
  ): Promise<string[]> {
    const userRoles: string[] = []
    
    // Get global user roles
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { role: true }
    })
    
    if (user?.role) {
      userRoles.push(user.role)
    }
    
    // Get organization-specific roles
    if (context?.organizationId) {
      const orgMembership = await db.member.findFirst({
        where: {
          userId,
          organizationId: context.organizationId
        },
        select: { role: true }
      })
      
      if (orgMembership?.role) {
        userRoles.push(orgMembership.role)
      }
    }
    
    // Get team-specific roles
    if (context?.teamId) {
      const teamMembership = await db.teamMembers.findFirst({
        where: {
          member: {
            userId
          },
          teamId: context.teamId
        },
        select: { role: true }
      })
      
      if (teamMembership?.role) {
        userRoles.push(teamMembership.role)
      }
    }
    
    // Get project-specific roles
    if (context?.projectId) {
      const projectMembership = await db.projectMembers.findFirst({
        where: {
          userId,
          projectId: context.projectId
        },
        select: { role: true }
      })
      
      if (projectMembership?.role) {
        userRoles.push(projectMembership.role)
      }
    }
    
    return userRoles
  }
  
  /**
   * Get permissions for a specific role
   */
  getRolePermissions(roleName: string): Record<string, string[]> {
    const role = roles[roleName as keyof typeof roles]
    if (!role) {
      return {}
    }
    
    // Convert role statements to permission object
    return role.statements
  }
  
  /**
   * Check if role permissions satisfy the required check
   */
  checkRolePermissions(
    rolePermissions: Record<string, string[]>,
    check: PermissionCheck
  ): boolean {
    const resourcePermissions = rolePermissions[check.resource]
    if (!resourcePermissions) {
      return false
    }
    
    // Check if all required actions are present
    return check.actions.every(action => 
      resourcePermissions.includes(action) || 
      resourcePermissions.includes("manage") // "manage" grants all permissions
    )
  }
  
  /**
   * Get effective permissions for a user in a specific context
   */
  async getEffectivePermissions(
    userId: string,
    context?: PermissionContext
  ): Promise<Record<string, string[]>> {
    const userRoles = await this.getUserRoles(userId, context)
    const effectivePermissions: Record<string, Set<string>> = {}
    
    // Combine permissions from all roles
    for (const role of userRoles) {
      const rolePermissions = this.getRolePermissions(role)
      
      for (const [resource, actions] of Object.entries(rolePermissions)) {
        if (!effectivePermissions[resource]) {
          effectivePermissions[resource] = new Set()
        }
        
        actions.forEach(action => {
          effectivePermissions[resource].add(action)
        })
      }
    }
    
    // Convert Sets back to arrays
    const result: Record<string, string[]> = {}
    for (const [resource, actionsSet] of Object.entries(effectivePermissions)) {
      result[resource] = Array.from(actionsSet)
    }
    
    return result
  }
  
  /**
   * Check if user can perform action on specific resource
   */
  async canPerformAction(
    userId: string,
    resourceType: string,
    action: string,
    context?: PermissionContext
  ): Promise<boolean> {
    return this.hasPermission(userId, {
      resource: resourceType,
      actions: [action]
    }, context)
  }
  
  /**
   * Get highest role for user in context (based on hierarchy)
   */
  async getHighestRole(
    userId: string,
    context?: PermissionContext
  ): Promise<string | null> {
    const userRoles = await this.getUserRoles(userId, context)
    
    let highestRole = null
    let highestLevel = Infinity
    
    for (const role of userRoles) {
      const level = roleHierarchy[role as keyof typeof roleHierarchy]
      if (level !== undefined && level < highestLevel) {
        highestLevel = level
        highestRole = role
      }
    }
    
    return highestRole
  }
  
  /**
   * Assign role to user in specific context
   */
  async assignRole(
    userId: string,
    role: string,
    context: PermissionContext
  ): Promise<void> {
    // Validate role exists
    if (!roles[role as keyof typeof roles]) {
      throw new Error(`Invalid role: ${role}`)
    }
    
    // Organization-level role assignment
    if (context.organizationId && !context.teamId && !context.projectId) {
      await db.member.update({
        where: {
          userId_organizationId: {
            userId,
            organizationId: context.organizationId
          }
        },
        data: { role }
      })
    }
    
    // Team-level role assignment
    if (context.teamId) {
      await db.teamMembers.update({
        where: {
          teamId_memberId: {
            teamId: context.teamId,
            memberId: userId
          }
        },
        data: { role }
      })
    }
    
    // Project-level role assignment
    if (context.projectId) {
      await db.projectMembers.upsert({
        where: {
          userId_projectId: {
            userId,
            projectId: context.projectId
          }
        },
        create: {
          userId,
          projectId: context.projectId,
          role
        },
        update: { role }
      })
    }
    
    // Log role assignment
    await this.logPermissionChange(userId, "role_assigned", {
      role,
      context,
      timestamp: new Date()
    })
  }
  
  /**
   * Remove role from user in specific context
   */
  async removeRole(
    userId: string,
    role: string,
    context: PermissionContext
  ): Promise<void> {
    // Organization-level role removal
    if (context.organizationId && !context.teamId && !context.projectId) {
      await db.member.update({
        where: {
          userId_organizationId: {
            userId,
            organizationId: context.organizationId
          }
        },
        data: { role: "member" } // Default role
      })
    }
    
    // Team-level role removal
    if (context.teamId) {
      await db.teamMembers.update({
        where: {
          teamId_memberId: {
            teamId: context.teamId,
            memberId: userId
          }
        },
        data: { role: "teamMember" } // Default role
      })
    }
    
    // Project-level role removal
    if (context.projectId) {
      await db.projectMembers.delete({
        where: {
          userId_projectId: {
            userId,
            projectId: context.projectId
          }
        }
      })
    }
    
    // Log role removal
    await this.logPermissionChange(userId, "role_removed", {
      role,
      context,
      timestamp: new Date()
    })
  }
  
  /**
   * Log permission changes for audit trail
   */
  async logPermissionChange(
    userId: string,
    action: string,
    details: Record<string, any>
  ): Promise<void> {
    await db.permissionLog.create({
      data: {
        userId,
        action,
        details,
        timestamp: new Date()
      }
    })
  }
  
  /**
   * Get permission audit log for user
   */
  async getPermissionAuditLog(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<any[]> {
    return db.permissionLog.findMany({
      where: { userId },
      orderBy: { timestamp: "desc" },
      take: limit,
      skip: offset
    })
  }
}

export const permissionService = new PermissionService()
```

### **3. Permission Middleware**

```typescript
// lib/middleware/permission-middleware.ts
import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { permissionService } from "@/lib/services/permission-service"

export interface PermissionMiddlewareOptions {
  resource: string
  actions: string[]
  context?: {
    organizationId?: string | ((req: NextRequest) => string)
    teamId?: string | ((req: NextRequest) => string)
    projectId?: string | ((req: NextRequest) => string)
  }
}

/**
 * Middleware to check user permissions before allowing access to routes
 */
export function withPermissions(options: PermissionMiddlewareOptions) {
  return async (request: NextRequest) => {
    try {
      // Get user session
      const session = await auth.api.getSession({
        headers: request.headers
      })
      
      if (!session) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        )
      }
      
      // Build permission context
      const context = {
        userId: session.user.id,
        organizationId: typeof options.context?.organizationId === "function" 
          ? options.context.organizationId(request)
          : options.context?.organizationId,
        teamId: typeof options.context?.teamId === "function"
          ? options.context.teamId(request)
          : options.context?.teamId,
        projectId: typeof options.context?.projectId === "function"
          ? options.context.projectId(request)
          : options.context?.projectId,
      }
      
      // Check permissions
      const hasPermission = await permissionService.hasPermission(
        session.user.id,
        {
          resource: options.resource,
          actions: options.actions
        },
        context
      )
      
      if (!hasPermission) {
        return NextResponse.json(
          { 
            error: "Forbidden", 
            message: `Insufficient permissions for ${options.resource}:${options.actions.join(",")}` 
          },
          { status: 403 }
        )
      }
      
      // Add permission context to request headers for downstream use
      const requestHeaders = new Headers(request.headers)
      requestHeaders.set("x-user-id", session.user.id)
      requestHeaders.set("x-user-permissions", JSON.stringify(context))
      
      return NextResponse.next({
        request: {
          headers: requestHeaders
        }
      })
      
    } catch (error) {
      console.error("Permission middleware error:", error)
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      )
    }
  }
}

// Common permission middleware factories
export const requireOrganizationAdmin = (organizationId?: string) => 
  withPermissions({
    resource: "organization",
    actions: ["manage"],
    context: { organizationId }
  })

export const requireTeamLead = (teamId?: string) =>
  withPermissions({
    resource: "team",
    actions: ["manage"],
    context: { teamId }
  })

export const requireProjectAccess = (projectId?: string, actions: string[] = ["read"]) =>
  withPermissions({
    resource: "project",
    actions,
    context: { projectId }
  })
```

### **4. Permission Management Components**

```typescript
// components/rbac/permission-manager.tsx
"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Switch } from "@/components/ui/switch"
import { Shield, Users, Settings, Plus, Trash2 } from "lucide-react"

interface Permission {
  resource: string
  actions: string[]
}

interface Role {
  name: string
  description: string
  permissions: Permission[]
  hierarchy: number
}

interface UserRole {
  userId: string
  userName: string
  email: string
  roles: string[]
  context: {
    organizationId?: string
    teamId?: string
    projectId?: string
  }
}

export function PermissionManager() {
  const [selectedTab, setSelectedTab] = useState<"roles" | "users" | "audit">("roles")
  const [roles, setRoles] = useState<Role[]>([])
  const [userRoles, setUserRoles] = useState<UserRole[]>([])
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadRoles()
    loadUserRoles()
  }, [])

  const loadRoles = async () => {
    try {
      const response = await fetch("/api/rbac/roles")
      const data = await response.json()
      setRoles(data.roles || [])
    } catch (error) {
      console.error("Failed to load roles:", error)
    }
  }

  const loadUserRoles = async () => {
    try {
      const response = await fetch("/api/rbac/user-roles")
      const data = await response.json()
      setUserRoles(data.userRoles || [])
    } catch (error) {
      console.error("Failed to load user roles:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleRoleUpdate = async (role: Role) => {
    try {
      const response = await fetch(`/api/rbac/roles/${role.name}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(role)
      })
      
      if (response.ok) {
        await loadRoles()
        setSelectedRole(null)
      }
    } catch (error) {
      console.error("Failed to update role:", error)
    }
  }

  const handleUserRoleAssignment = async (
    userId: string,
    roleName: string,
    context: any
  ) => {
    try {
      const response = await fetch("/api/rbac/assign-role", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId,
          role: roleName,
          context
        })
      })
      
      if (response.ok) {
        await loadUserRoles()
      }
    } catch (error) {
      console.error("Failed to assign role:", error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Permission Management</h2>
          <p className="text-muted-foreground">
            Manage roles, permissions, and user access control
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant={selectedTab === "roles" ? "default" : "outline"}
            onClick={() => setSelectedTab("roles")}
          >
            <Shield className="h-4 w-4 mr-2" />
            Roles
          </Button>
          <Button
            variant={selectedTab === "users" ? "default" : "outline"}
            onClick={() => setSelectedTab("users")}
          >
            <Users className="h-4 w-4 mr-2" />
            Users
          </Button>
          <Button
            variant={selectedTab === "audit" ? "default" : "outline"}
            onClick={() => setSelectedTab("audit")}
          >
            <Settings className="h-4 w-4 mr-2" />
            Audit
          </Button>
        </div>
      </div>

      {selectedTab === "roles" && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Available Roles</CardTitle>
              <CardDescription>
                Manage role definitions and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px]">
                <div className="space-y-3">
                  {roles.map((role) => (
                    <div
                      key={role.name}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedRole?.name === role.name
                          ? "border-primary bg-primary/5"
                          : "border-border hover:border-primary/50"
                      }`}
                      onClick={() => setSelectedRole(role)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{role.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {role.description}
                          </p>
                        </div>
                        <Badge variant="secondary">
                          Level {role.hierarchy}
                        </Badge>
                      </div>
                      <div className="mt-2 flex flex-wrap gap-1">
                        {role.permissions.slice(0, 3).map((perm) => (
                          <Badge key={perm.resource} variant="outline" className="text-xs">
                            {perm.resource}
                          </Badge>
                        ))}
                        {role.permissions.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{role.permissions.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Role Editor</CardTitle>
              <CardDescription>
                {selectedRole ? `Edit ${selectedRole.name}` : "Select a role to edit"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedRole ? (
                <RoleEditor
                  role={selectedRole}
                  onUpdate={handleRoleUpdate}
                />
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Select a role from the list to edit its permissions
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {selectedTab === "users" && (
        <Card>
          <CardHeader>
            <CardTitle>User Role Management</CardTitle>
            <CardDescription>
              Assign and manage user roles across different contexts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <UserRoleManager
              userRoles={userRoles}
              roles={roles}
              onRoleAssignment={handleUserRoleAssignment}
            />
          </CardContent>
        </Card>
      )}

      {selectedTab === "audit" && (
        <Card>
          <CardHeader>
            <CardTitle>Permission Audit Log</CardTitle>
            <CardDescription>
              Track permission changes and access patterns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <PermissionAuditLog />
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Role Editor Component
function RoleEditor({ role, onUpdate }: { role: Role; onUpdate: (role: Role) => void }) {
  const [editedRole, setEditedRole] = useState<Role>(role)

  const handlePermissionToggle = (resource: string, action: string) => {
    const updatedPermissions = editedRole.permissions.map(perm => {
      if (perm.resource === resource) {
        const actions = perm.actions.includes(action)
          ? perm.actions.filter(a => a !== action)
          : [...perm.actions, action]
        return { ...perm, actions }
      }
      return perm
    })
    
    setEditedRole({ ...editedRole, permissions: updatedPermissions })
  }

  const availableResources = [
    "organization", "team", "member", "project", "document", "file",
    "user", "session", "subscription", "invoice", "analytics", "report"
  ]

  const availableActions = [
    "create", "read", "update", "delete", "manage", "share", "collaborate"
  ]

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="role-name">Role Name</Label>
        <Input
          id="role-name"
          value={editedRole.name}
          onChange={(e) => setEditedRole({ ...editedRole, name: e.target.value })}
        />
      </div>

      <div>
        <Label htmlFor="role-description">Description</Label>
        <Input
          id="role-description"
          value={editedRole.description}
          onChange={(e) => setEditedRole({ ...editedRole, description: e.target.value })}
        />
      </div>

      <div>
        <Label htmlFor="role-hierarchy">Hierarchy Level</Label>
        <Select
          value={editedRole.hierarchy.toString()}
          onValueChange={(value) => setEditedRole({ ...editedRole, hierarchy: parseInt(value) })}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {[0, 1, 2, 3, 4, 5].map(level => (
              <SelectItem key={level} value={level.toString()}>
                Level {level}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Separator />

      <div>
        <Label>Permissions</Label>
        <ScrollArea className="h-[300px] border rounded-md p-4">
          <div className="space-y-4">
            {availableResources.map(resource => {
              const resourcePermission = editedRole.permissions.find(p => p.resource === resource)
              return (
                <div key={resource} className="space-y-2">
                  <h4 className="font-medium capitalize">{resource}</h4>
                  <div className="grid grid-cols-3 gap-2">
                    {availableActions.map(action => (
                      <div key={action} className="flex items-center space-x-2">
                        <Switch
                          id={`${resource}-${action}`}
                          checked={resourcePermission?.actions.includes(action) || false}
                          onCheckedChange={() => handlePermissionToggle(resource, action)}
                        />
                        <Label htmlFor={`${resource}-${action}`} className="text-sm capitalize">
                          {action}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        </ScrollArea>
      </div>

      <div className="flex justify-end space-x-2">
        <Button variant="outline" onClick={() => setEditedRole(role)}>
          Reset
        </Button>
        <Button onClick={() => onUpdate(editedRole)}>
          Save Changes
        </Button>
      </div>
    </div>
  )
}

// User Role Manager Component
function UserRoleManager({
  userRoles,
  roles,
  onRoleAssignment
}: {
  userRoles: UserRole[]
  roles: Role[]
  onRoleAssignment: (userId: string, roleName: string, context: any) => void
}) {
  const [selectedUser, setSelectedUser] = useState<UserRole | null>(null)
  const [newRole, setNewRole] = useState("")
  const [context, setContext] = useState({
    organizationId: "",
    teamId: "",
    projectId: ""
  })

  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2">
        <div>
          <Label>Users</Label>
          <ScrollArea className="h-[400px] border rounded-md p-4">
            <div className="space-y-2">
              {userRoles.map(userRole => (
                <div
                  key={userRole.userId}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedUser?.userId === userRole.userId
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50"
                  }`}
                  onClick={() => setSelectedUser(userRole)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{userRole.userName}</h4>
                      <p className="text-sm text-muted-foreground">{userRole.email}</p>
                    </div>
                    <Badge variant="secondary">
                      {userRole.roles.length} roles
                    </Badge>
                  </div>
                  <div className="mt-2 flex flex-wrap gap-1">
                    {userRole.roles.map(role => (
                      <Badge key={role} variant="outline" className="text-xs">
                        {role}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>

        <div>
          <Label>Role Assignment</Label>
          {selectedUser ? (
            <div className="space-y-4 p-4 border rounded-md">
              <div>
                <h4 className="font-medium">{selectedUser.userName}</h4>
                <p className="text-sm text-muted-foreground">{selectedUser.email}</p>
              </div>

              <div>
                <Label htmlFor="new-role">Assign Role</Label>
                <Select value={newRole} onValueChange={setNewRole}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map(role => (
                      <SelectItem key={role.name} value={role.name}>
                        {role.name} - {role.description}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="org-id">Organization ID</Label>
                  <Input
                    id="org-id"
                    value={context.organizationId}
                    onChange={(e) => setContext({ ...context, organizationId: e.target.value })}
                    placeholder="Optional"
                  />
                </div>
                <div>
                  <Label htmlFor="team-id">Team ID</Label>
                  <Input
                    id="team-id"
                    value={context.teamId}
                    onChange={(e) => setContext({ ...context, teamId: e.target.value })}
                    placeholder="Optional"
                  />
                </div>
              </div>

              <Button
                onClick={() => onRoleAssignment(selectedUser.userId, newRole, context)}
                disabled={!newRole}
                className="w-full"
              >
                Assign Role
              </Button>

              <div className="space-y-2">
                <Label>Current Roles</Label>
                <div className="space-y-2">
                  {selectedUser.roles.map(role => (
                    <div key={role} className="flex items-center justify-between p-2 border rounded">
                      <span className="text-sm">{role}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          // Handle role removal
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground border rounded-md">
              Select a user to manage their roles
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Permission Audit Log Component
function PermissionAuditLog() {
  const [auditLogs, setAuditLogs] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAuditLogs()
  }, [])

  const loadAuditLogs = async () => {
    try {
      const response = await fetch("/api/rbac/audit-logs")
      const data = await response.json()
      setAuditLogs(data.logs || [])
    } catch (error) {
      console.error("Failed to load audit logs:", error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <ScrollArea className="h-[500px]">
      <div className="space-y-2">
        {auditLogs.map((log) => (
          <div key={log.id} className="p-3 border rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">{log.action}</h4>
                <p className="text-sm text-muted-foreground">
                  User: {log.userId} | {new Date(log.timestamp).toLocaleString()}
                </p>
              </div>
              <Badge variant="outline">{log.action}</Badge>
            </div>
            <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-x-auto">
              {JSON.stringify(log.details, null, 2)}
            </pre>
          </div>
        ))}
      </div>
    </ScrollArea>
  )
}
```

### **5. API Routes for RBAC Management**

```typescript
// app/api/rbac/roles/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { roles } from '@/lib/auth/rbac-config'
import { permissionService } from '@/lib/services/permission-service'

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers
    })

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to manage roles
    const hasPermission = await permissionService.hasPermission(
      session.user.id,
      { resource: 'user', actions: ['manage'] }
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Convert roles to API format
    const rolesData = Object.entries(roles).map(([name, role]) => ({
      name,
      description: `${name} role`,
      permissions: Object.entries(role.statements).map(([resource, actions]) => ({
        resource,
        actions: Array.isArray(actions) ? actions : []
      })),
      hierarchy: roleHierarchy[name as keyof typeof roleHierarchy] || 999
    }))

    return NextResponse.json({ roles: rolesData })
  } catch (error) {
    console.error('Failed to get roles:', error)
    return NextResponse.json(
      { error: 'Failed to get roles' },
      { status: 500 }
    )
  }
}

// app/api/rbac/assign-role/route.ts
export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers
    })

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { userId, role, context } = await request.json()

    // Check if user has permission to assign roles
    const hasPermission = await permissionService.hasPermission(
      session.user.id,
      { resource: 'user', actions: ['manage'] }
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Assign role
    await permissionService.assignRole(userId, role, context)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Failed to assign role:', error)
    return NextResponse.json(
      { error: 'Failed to assign role' },
      { status: 500 }
    )
  }
}
```

---

## 📊 **SUCCESS METRICS**

### **Technical Metrics**
- **Permission Check Performance**: < 50ms average response time
- **Role Assignment Speed**: < 100ms for role updates
- **Cache Hit Rate**: > 95% for permission checks
- **Database Query Efficiency**: < 3 queries per permission check

### **Security Metrics**
- **Permission Accuracy**: 100% correct access control decisions
- **Audit Trail Completeness**: 100% of permission changes logged
- **Unauthorized Access**: 0 instances of permission bypass
- **Role Hierarchy Integrity**: 100% proper role inheritance

### **User Experience Metrics**
- **Role Setup Time**: < 5 minutes for new role configuration
- **Permission Understanding**: > 90% user comprehension of their permissions
- **False Positive Rate**: < 1% incorrect permission denials
- **User Satisfaction**: > 4.5/5 rating for permission clarity

---

## 🧪 **TESTING STRATEGY**

### **Unit Tests**
- Permission service methods
- Role hierarchy evaluation
- Context-aware permission checks
- Permission inheritance logic

### **Integration Tests**
- Better Auth RBAC plugin integration
- Database permission operations
- Multi-context permission evaluation
- Audit logging functionality

### **Security Tests**
- Permission escalation prevention
- Context isolation verification
- Role hierarchy bypass attempts
- Unauthorized access prevention

### **Performance Tests**
- Permission check latency
- Bulk role assignment performance
- Cache effectiveness
- Database query optimization

---

## 🚀 **DEPLOYMENT STRATEGY**

### **Phase 1: Core Implementation**
1. Deploy Better Auth RBAC configuration
2. Implement permission service and middleware
3. Create basic role management interface
4. Deploy permission audit logging

### **Phase 2: Advanced Features**
1. Roll out hierarchical permission system
2. Implement context-aware permissions
3. Deploy role templates and presets
4. Add advanced audit and reporting

### **Phase 3: Optimization**
1. Implement permission caching
2. Add performance monitoring
3. Deploy permission analytics
4. Optimize database queries

---

## 📋 **COMPLETION CHECKLIST**

### **Backend Implementation**
- [ ] Better Auth RBAC configuration
- [ ] Permission service implementation
- [ ] Permission middleware setup
- [ ] Role hierarchy system
- [ ] Context-aware permissions
- [ ] Audit logging system
- [ ] API routes for RBAC management

### **Frontend Implementation**
- [ ] Permission management dashboard
- [ ] Role editor interface
- [ ] User role assignment UI
- [ ] Permission audit log viewer
- [ ] Role template system
- [ ] Permission-aware UI components

### **Testing & Security**
- [ ] Comprehensive unit tests
- [ ] Integration test suite
- [ ] Security penetration testing
- [ ] Performance benchmarking
- [ ] Permission accuracy validation

### **Documentation & Training**
- [ ] RBAC system documentation
- [ ] Permission guide for users
- [ ] Role management tutorials
- [ ] Security best practices
- [ ] Troubleshooting guide

---

**Implementation Priority**: **HIGH** (Critical security feature)  
**Estimated Timeline**: 7-10 days  
**Team Size**: 2-3 developers + 1 security specialist  
**Dependencies**: Better Auth organization plugin, existing user management system

---

*This PRP provides a comprehensive implementation plan for an advanced RBAC system that leverages Better Auth's powerful access control capabilities while adding enterprise-grade features like hierarchical roles, context-aware permissions, and detailed audit logging.*

**Built with ❤️ by Nexus-Master Agent**  
*Where 125 Senior Developers Meet AI Excellence*
