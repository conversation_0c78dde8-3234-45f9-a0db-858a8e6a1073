# Multi-Tenant Database Architecture Implementation PRP

**PRP Name**: Multi-Tenant Database Architecture Implementation  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Foundation Implementation PRP  
**Framework**: Next.js 15.4+ / React 19 / TypeScript 5.8+ / Prisma / PostgreSQL  
**Priority**: Critical Path - Must Complete First  

---

## Purpose

Implement a robust multi-tenant database architecture using Prisma with PostgreSQL that ensures complete tenant isolation, optimal performance, and compliance with enterprise security requirements including SOC 2, GDPR, and HIPAA.

## Context and Research

### Current Technology Stack
- **Database**: PostgreSQL with Prisma ORM
- **Framework**: Next.js 15.4+ with App Router
- **TypeScript**: 5.8+ for type safety
- **Authentication**: better-auth (to be integrated)
- **Deployment**: Vercel/AWS with connection pooling

### Multi-Tenant Architecture Patterns Research

**Row-Level Security (RLS) Pattern - RECOMMENDED**
- URL: https://www.postgresql.org/docs/current/ddl-rowsecurity.html
- URL: https://prisma.io/docs/guides/database/multi-tenancy
- Benefits: Single database, automatic isolation, scalable
- Implementation: tenant_id column + RLS policies

**Database-per-Tenant Pattern**
- URL: https://docs.aws.amazon.com/multi-tenant-saas-guidance/latest/ug/database-per-tenant.html
- Benefits: Complete isolation, easier compliance
- Drawbacks: Complex migrations, higher costs

**Schema-per-Tenant Pattern**
- URL: https://www.citusdata.com/blog/2017/03/09/multi-tenant-sharding-tutorial/
- Benefits: Namespace isolation, shared resources
- Drawbacks: Schema management complexity

### Security and Compliance Research

**SOC 2 Requirements**
- URL: https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/aicpasoc2report.html
- Data isolation requirements
- Audit logging needs
- Access control specifications

**GDPR Compliance**
- URL: https://gdpr.eu/data-protection/
- Data portability requirements
- Right to erasure implementation
- Data processing transparency

**HIPAA Compliance**
- URL: https://www.hhs.gov/hipaa/for-professionals/security/index.html
- Encryption requirements
- Audit trail specifications
- Access control mandates

### Performance Optimization Research

**Database Connection Pooling**
- URL: https://www.prisma.io/docs/guides/performance-and-optimization/connection-management
- Connection limits and optimization
- PgBouncer integration patterns

**Query Optimization**
- URL: https://www.postgresql.org/docs/current/performance-tips.html
- Index strategies for multi-tenant queries
- Partitioning for large datasets

## Implementation Blueprint

### Data Models and Structure

```typescript
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  domain      String?  @unique
  status      TenantStatus @default(ACTIVE)
  plan        SubscriptionPlan @default(STARTER)
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relationships
  users       User[]
  workspaces  Workspace[]
  
  @@map("tenants")
}

model User {
  id        String   @id @default(cuid())
  tenantId  String   // Multi-tenant isolation
  email     String   
  name      String?
  avatar    String?
  role      UserRole @default(MEMBER)
  status    UserStatus @default(ACTIVE)
  metadata  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  @@unique([tenantId, email])
  @@map("users")
}

model Workspace {
  id        String   @id @default(cuid())
  tenantId  String   // Multi-tenant isolation
  name      String
  slug      String
  settings  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  @@unique([tenantId, slug])
  @@map("workspaces")
}

enum TenantStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

enum SubscriptionPlan {
  STARTER
  PROFESSIONAL
  ENTERPRISE
}

enum UserRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  INVITED
}
```

### Database Security Implementation

```sql
-- Row-Level Security Policies
-- Enable RLS on all tenant-isolated tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY tenant_isolation_users ON users
  FOR ALL TO authenticated
  USING (tenant_id = current_setting('app.current_tenant_id')::text);

CREATE POLICY tenant_isolation_workspaces ON workspaces
  FOR ALL TO authenticated
  USING (tenant_id = current_setting('app.current_tenant_id')::text);

-- Create indexes for performance
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_workspaces_tenant_id ON workspaces(tenant_id);
CREATE INDEX idx_tenants_slug ON tenants(slug);
CREATE INDEX idx_tenants_domain ON tenants(domain);
```

### Prisma Client Configuration

```typescript
// lib/database/prisma.ts
import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: ['query', 'error', 'warn'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// Tenant-aware Prisma client
export function createTenantPrisma(tenantId: string) {
  return prisma.$extends({
    query: {
      $allModels: {
        async $allOperations({ args, query }) {
          // Automatically inject tenant_id for all operations
          if (args.where && typeof args.where === 'object') {
            args.where.tenantId = tenantId
          }
          if (args.data && typeof args.data === 'object') {
            args.data.tenantId = tenantId
          }
          return query(args)
        },
      },
    },
  })
}
```

### Tenant Context Middleware

```typescript
// lib/middleware/tenant-context.ts
import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/database/prisma'

export async function withTenantContext(request: NextRequest) {
  try {
    // Extract tenant from subdomain or header
    const host = request.headers.get('host')
    const subdomain = host?.split('.')[0]
    
    let tenant = null
    
    if (subdomain && subdomain !== 'www') {
      tenant = await prisma.tenant.findUnique({
        where: { slug: subdomain },
        select: { id: true, name: true, slug: true, status: true }
      })
    }
    
    if (!tenant) {
      // Try to get tenant from custom domain
      tenant = await prisma.tenant.findUnique({
        where: { domain: host },
        select: { id: true, name: true, slug: true, status: true }
      })
    }
    
    if (!tenant) {
      return NextResponse.json(
        { error: 'Tenant not found' },
        { status: 404 }
      )
    }
    
    if (tenant.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: 'Tenant is not active' },
        { status: 403 }
      )
    }
    
    // Add tenant context to request
    const response = NextResponse.next()
    response.headers.set('x-tenant-id', tenant.id)
    response.headers.set('x-tenant-slug', tenant.slug)
    
    return response
  } catch (error) {
    console.error('Tenant context middleware error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
```

## Task Breakdown

### Phase 1: Database Schema Implementation (2-3 hours)

1. **Create Prisma Schema**
   - File: `prisma/schema.prisma`
   - Implement base tenant, user, and workspace models
   - Add proper indexes and constraints
   - Include metadata fields for extensibility

2. **Create Database Migration**
   - Command: `npx prisma migrate dev --name init-multi-tenant`
   - Verify schema generation
   - Test migration rollback capability

3. **Implement Row-Level Security**
   - Create SQL policies for tenant isolation
   - Test policy enforcement
   - Verify performance impact

### Phase 2: Prisma Client Configuration (1-2 hours)

4. **Setup Tenant-Aware Prisma Client**
   - File: `lib/database/prisma.ts`
   - Implement automatic tenant_id injection
   - Add connection pooling configuration
   - Include error handling and logging

5. **Create Database Types**
   - File: `lib/types/database.ts`
   - Generate TypeScript types from Prisma schema
   - Add utility types for tenant operations
   - Include validation schemas

### Phase 3: Tenant Context System (2-3 hours)

6. **Implement Tenant Context Middleware**
   - File: `lib/middleware/tenant-context.ts`
   - Add subdomain detection logic
   - Implement custom domain support
   - Include tenant status validation

7. **Create Tenant Resolution Hook**
   - File: `hooks/use-tenant.ts`
   - Implement client-side tenant context
   - Add tenant switching capabilities
   - Include error handling

### Phase 4: Security and Compliance (1-2 hours)

8. **Implement Audit Logging**
   - File: `lib/audit/audit-logger.ts`
   - Track all database operations
   - Include user and tenant context
   - Add compliance metadata

9. **Add Data Encryption**
   - Implement field-level encryption for sensitive data
   - Add key rotation capabilities
   - Include backup encryption

### Phase 5: Performance Optimization (1-2 hours)

10. **Database Connection Pooling**
    - Configure PgBouncer or similar
    - Optimize connection limits
    - Add monitoring and alerts

11. **Query Optimization**
    - Analyze and optimize slow queries
    - Add composite indexes
    - Implement query caching

## Integration Points

### Database Changes and Migrations
- **Initial Migration**: Create base multi-tenant schema
- **Seed Data**: Create sample tenants and users
- **Backup Strategy**: Implement tenant-aware backups

### API Endpoint Modifications
- **Tenant Context**: All API routes must include tenant validation
- **Error Handling**: Standardized error responses for tenant issues
- **Rate Limiting**: Per-tenant rate limiting implementation

### Frontend Component Updates
- **Tenant Provider**: React context for tenant information
- **Tenant Selector**: UI for tenant switching (if applicable)
- **Loading States**: Tenant-specific loading states

### Authentication Integration
- **Tenant-Scoped Sessions**: Sessions must include tenant context
- **Multi-Tenant SSO**: Support for tenant-specific SSO providers
- **Role-Based Access**: Tenant-aware permission system

## Validation Gates

### Level 1: Syntax & Schema Validation
```bash
# TypeScript compilation
npx tsc --noEmit

# Prisma schema validation
npx prisma validate

# Database connection test
npx prisma db push --accept-data-loss
```

### Level 2: Database Tests
```bash
# Run database tests
npm test -- --testPathPattern=database

# Test specific queries
npm run test:db -- --testNamePattern="tenant isolation"

# Performance testing
npm run test:performance -- --testNamePattern="multi-tenant"
```

### Level 3: Integration Tests
```bash
# Start test database
npm run test:db:start

# Run integration tests
npm run test:integration

# Test tenant isolation
curl -H "Host: tenant1.localhost:3000" http://localhost:3000/api/users
curl -H "Host: tenant2.localhost:3000" http://localhost:3000/api/users
```

### Level 4: Security Tests
```bash
# Test RLS policies
npm run test:security:rls

# Test tenant isolation
npm run test:security:isolation

# Run security audit
npm audit
npx prisma audit
```

### Level 5: Performance Tests
```bash
# Database performance test
npm run test:performance:db

# Load testing
npm run test:load -- --tenants=100 --users=1000

# Connection pool testing
npm run test:connections
```

## Error Handling and Edge Cases

### Tenant Resolution Errors
- **Invalid Subdomain**: Graceful fallback to default tenant
- **Tenant Not Found**: Clear error message and redirect
- **Tenant Suspended**: Maintenance page with contact info

### Database Connection Issues
- **Connection Pool Exhaustion**: Queue requests with timeout
- **Database Unavailable**: Retry logic with exponential backoff
- **Migration Failures**: Rollback mechanism with notifications

### Performance Edge Cases
- **Large Tenant Queries**: Implement pagination and caching
- **Concurrent Tenant Operations**: Optimize locking and transactions
- **Database Migrations**: Zero-downtime migration strategy

## Multi-Tenant Architecture Considerations

### Data Isolation
- **Complete Separation**: Each tenant's data is completely isolated
- **Shared Resources**: Optimize for shared infrastructure costs
- **Compliance Ready**: Meet SOC 2, GDPR, and HIPAA requirements

### Performance Optimization
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Tenant-aware query optimization
- **Caching Strategy**: Multi-tenant caching with proper isolation

### Security Requirements
- **Row-Level Security**: Automatic tenant isolation at database level
- **Audit Logging**: Complete audit trail for compliance
- **Encryption**: Field-level encryption for sensitive data

## Success Criteria

### Functional Requirements
- ✅ Complete tenant isolation with zero data leakage
- ✅ Sub-200ms query response times for typical operations
- ✅ Support for 1000+ concurrent tenants
- ✅ Zero-downtime migrations and updates

### Security Requirements
- ✅ SOC 2 Type II compliance ready
- ✅ GDPR data protection compliance
- ✅ HIPAA encryption and audit requirements
- ✅ Automated security testing and monitoring

### Performance Requirements
- ✅ Database connection pooling with <50ms connection time
- ✅ Query optimization with automatic indexing
- ✅ Horizontal scaling support for enterprise loads
- ✅ Real-time monitoring and alerting

## Documentation and Deployment

### Documentation Requirements
- **Database Schema**: Complete ERD and table documentation
- **Security Policies**: RLS policy documentation
- **Performance Tuning**: Query optimization guide
- **Compliance**: Audit and compliance documentation

### Deployment Steps
1. **Database Setup**: Create production database with proper security
2. **Migration Deployment**: Run migrations with zero downtime
3. **Monitoring Setup**: Configure database monitoring and alerting
4. **Backup Configuration**: Implement automated tenant-aware backups

---

**Implementation Time Estimate**: 8-12 hours for complete implementation  
**Dependencies**: PostgreSQL database, Prisma CLI, proper environment configuration  
**Risk Level**: High - Foundation component that affects all other features  
**Validation**: Comprehensive testing required before other feature development  

**Quality Score Target**: 9/10 (Critical foundation component)  
- Context Completeness: 3/3 ✅
- Implementation Clarity: 3/3 ✅  
- Validation Coverage: 2/2 ✅
- Multi-Tenant Readiness: 1/1 ✅

---

*Built with ❤️ by Nexus-Master Agent*  
*Production-Ready Multi-Tenant Architecture*
