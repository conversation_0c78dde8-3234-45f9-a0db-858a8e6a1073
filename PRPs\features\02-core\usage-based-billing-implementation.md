# Usage-Based Billing Implementation

## Overview
This PRP implements a comprehensive usage-based billing system for the NEXUS SaaS Starter, enabling metered billing with real-time usage tracking, automatic consumption recording, and flexible pricing models. The system supports multiple usage metrics, tiered pricing, and detailed billing reports integrated with Stripe's metered billing API.

## Core Components

### 1. Usage Tracking Store (`src/stores/usage-tracking-store.ts`)
```typescript
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { toast } from 'sonner';

interface UsageTrackingStore {
  currentUsage: UsageMetric[];
  usageHistory: UsageHistory[];
  billingPeriod: BillingPeriod;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  trackUsage: (metricId: string, quantity: number, metadata?: Record<string, any>) => Promise<void>;
  getCurrentUsage: () => Promise<void>;
  getUsageHistory: (startDate: Date, endDate: Date) => Promise<void>;
  getBillingPeriodUsage: () => Promise<void>;
  resetUsage: (metricId: string) => Promise<void>;
}

interface UsageMetric {
  id: string;
  metricId: string;
  metricName: string;
  currentQuantity: number;
  billingPeriodQuantity: number;
  unitPrice: number;
  currency: string;
  aggregation: 'sum' | 'last_during_period' | 'last_ever' | 'max';
  lastUpdated: Date;
  nextBillingDate: Date;
}

interface UsageHistory {
  id: string;
  metricId: string;
  metricName: string;
  quantity: number;
  timestamp: Date;
  metadata: Record<string, any>;
  billingPeriodStart: Date;
  billingPeriodEnd: Date;
  cost: number;
  currency: string;
}

interface BillingPeriod {
  start: Date;
  end: Date;
  status: 'active' | 'ended' | 'pending';
  totalCost: number;
  currency: string;
}

export const useUsageTrackingStore = create<UsageTrackingStore>()(
  devtools(
    (set, get) => ({
      currentUsage: [],
      usageHistory: [],
      billingPeriod: {
        start: new Date(),
        end: new Date(),
        status: 'active',
        totalCost: 0,
        currency: 'USD',
      },
      isLoading: false,
      error: null,

      trackUsage: async (metricId: string, quantity: number, metadata = {}) => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch('/api/usage/track', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              metricId,
              quantity,
              metadata,
              timestamp: new Date().toISOString(),
            }),
          });

          if (!response.ok) {
            throw new Error('Failed to track usage');
          }

          const result = await response.json();
          
          // Update current usage
          set(state => ({
            currentUsage: state.currentUsage.map(metric => 
              metric.metricId === metricId 
                ? { ...metric, currentQuantity: result.newQuantity, lastUpdated: new Date() }
                : metric
            ),
            isLoading: false,
          }));

          // Refresh usage data
          await get().getCurrentUsage();
        } catch (error) {
          set({ error: error.message, isLoading: false });
          toast.error('Failed to track usage');
        }
      },

      getCurrentUsage: async () => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch('/api/usage/current');
          if (!response.ok) throw new Error('Failed to fetch current usage');
          
          const data = await response.json();
          set({ currentUsage: data, isLoading: false });
        } catch (error) {
          set({ error: error.message, isLoading: false });
          toast.error('Failed to load current usage');
        }
      },

      getUsageHistory: async (startDate: Date, endDate: Date) => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch(`/api/usage/history?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`);
          if (!response.ok) throw new Error('Failed to fetch usage history');
          
          const data = await response.json();
          set({ usageHistory: data, isLoading: false });
        } catch (error) {
          set({ error: error.message, isLoading: false });
          toast.error('Failed to load usage history');
        }
      },

      getBillingPeriodUsage: async () => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch('/api/usage/billing-period');
          if (!response.ok) throw new Error('Failed to fetch billing period usage');
          
          const data = await response.json();
          set({ 
            billingPeriod: data.billingPeriod,
            currentUsage: data.usage,
            isLoading: false,
          });
        } catch (error) {
          set({ error: error.message, isLoading: false });
          toast.error('Failed to load billing period usage');
        }
      },

      resetUsage: async (metricId: string) => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch('/api/usage/reset', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ metricId }),
          });

          if (!response.ok) throw new Error('Failed to reset usage');
          
          set(state => ({
            currentUsage: state.currentUsage.map(metric => 
              metric.metricId === metricId 
                ? { ...metric, currentQuantity: 0, lastUpdated: new Date() }
                : metric
            ),
            isLoading: false,
          }));

          toast.success('Usage reset successfully');
        } catch (error) {
          set({ error: error.message, isLoading: false });
          toast.error('Failed to reset usage');
        }
      },
    }),
    { name: 'usage-tracking-store' }
  )
);
```

### 2. Usage Dashboard Component (`src/components/usage/usage-dashboard.tsx`)
```tsx
'use client';

import { useEffect, useState } from 'react';
import { useUsageTrackingStore } from '@/stores/usage-tracking-store';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DatePickerWithRange } from '@/components/ui/date-picker-with-range';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Activity, Calendar, DollarSign, TrendingUp, AlertCircle, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

export function UsageDashboard() {
  const {
    currentUsage,
    usageHistory,
    billingPeriod,
    isLoading,
    error,
    getCurrentUsage,
    getUsageHistory,
    getBillingPeriodUsage,
    resetUsage,
  } = useUsageTrackingStore();

  const [dateRange, setDateRange] = useState({
    from: new Date(new Date().setDate(new Date().getDate() - 30)),
    to: new Date(),
  });

  useEffect(() => {
    getCurrentUsage();
    getBillingPeriodUsage();
  }, [getCurrentUsage, getBillingPeriodUsage]);

  useEffect(() => {
    if (dateRange.from && dateRange.to) {
      getUsageHistory(dateRange.from, dateRange.to);
    }
  }, [dateRange, getUsageHistory]);

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  };

  const getUsagePercentage = (current: number, limit?: number) => {
    if (!limit) return 0;
    return Math.min((current / limit) * 100, 100);
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-orange-600';
    if (percentage >= 50) return 'text-yellow-600';
    return 'text-green-600';
  };

  const prepareChartData = () => {
    const grouped = usageHistory.reduce((acc, item) => {
      const date = format(new Date(item.timestamp), 'yyyy-MM-dd');
      if (!acc[date]) {
        acc[date] = { date, total: 0, metrics: {} };
      }
      acc[date].total += item.quantity;
      acc[date].metrics[item.metricName] = (acc[date].metrics[item.metricName] || 0) + item.quantity;
      return acc;
    }, {} as Record<string, any>);

    return Object.values(grouped).sort((a: any, b: any) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );
  };

  if (isLoading && currentUsage.length === 0) {
    return <UsageDashboardSkeleton />;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Usage Dashboard</h2>
          <p className="text-gray-600">
            Monitor your usage and billing for the current period
          </p>
        </div>
        <div className="flex items-center gap-4">
          <DatePickerWithRange
            value={dateRange}
            onChange={setDateRange}
            className="w-[300px]"
          />
          <Button
            onClick={() => {
              getCurrentUsage();
              getBillingPeriodUsage();
            }}
            disabled={isLoading}
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              <p>{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Billing Period Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Current Billing Period
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Period</p>
              <p className="font-medium">
                {format(billingPeriod.start, 'MMM dd')} - {format(billingPeriod.end, 'MMM dd, yyyy')}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Status</p>
              <Badge variant={billingPeriod.status === 'active' ? 'default' : 'secondary'}>
                {billingPeriod.status}
              </Badge>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Total Cost</p>
              <p className="text-xl font-bold">
                {formatCurrency(billingPeriod.totalCost, billingPeriod.currency)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {currentUsage.map((metric) => (
          <Card key={metric.id}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{metric.metricName}</CardTitle>
                <Activity className="h-5 w-5 text-gray-500" />
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Current Usage</span>
                  <span className="font-medium">
                    {metric.currentQuantity.toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Billing Period</span>
                  <span className="font-medium">
                    {metric.billingPeriodQuantity.toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Unit Price</span>
                  <span className="font-medium">
                    {formatCurrency(metric.unitPrice, metric.currency)}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Estimated Cost</span>
                  <span className="font-bold">
                    {formatCurrency(
                      metric.billingPeriodQuantity * metric.unitPrice,
                      metric.currency
                    )}
                  </span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Aggregation</span>
                  <Badge variant="outline">{metric.aggregation}</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Last Updated</span>
                  <span>{format(metric.lastUpdated, 'MMM dd, HH:mm')}</span>
                </div>
              </div>

              <div className="pt-4 border-t">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="sm" className="w-full">
                      Reset Usage
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Reset Usage</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to reset the usage for {metric.metricName}? 
                        This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => resetUsage(metric.metricId)}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Reset
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Usage Analytics */}
      <Tabs defaultValue="chart" className="space-y-4">
        <TabsList>
          <TabsTrigger value="chart">Usage Chart</TabsTrigger>
          <TabsTrigger value="history">Usage History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="chart" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={prepareChartData()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="total"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      dot={{ fill: '#3b82f6' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Usage History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {usageHistory.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No usage history found for the selected period
                  </div>
                ) : (
                  <div className="space-y-2">
                    {usageHistory.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div>
                          <p className="font-medium">{item.metricName}</p>
                          <p className="text-sm text-gray-600">
                            {format(new Date(item.timestamp), 'MMM dd, yyyy HH:mm')}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{item.quantity.toLocaleString()}</p>
                          <p className="text-sm text-gray-600">
                            {formatCurrency(item.cost, item.currency)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Usage Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {currentUsage.map((metric) => (
                    <div key={metric.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{metric.metricName}</span>
                        <span className="text-sm text-gray-600">
                          {metric.billingPeriodQuantity.toLocaleString()}
                        </span>
                      </div>
                      <Progress
                        value={getUsagePercentage(metric.billingPeriodQuantity, 1000)}
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Cost Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={currentUsage}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="metricName" />
                      <YAxis />
                      <Tooltip />
                      <Bar
                        dataKey="billingPeriodQuantity"
                        fill="#3b82f6"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

function UsageDashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64 mt-2" />
        </div>
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-[300px]" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>

      <Skeleton className="h-32 w-full" />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Skeleton className="h-[500px] w-full" />
    </div>
  );
}
```

### 3. Usage Tracking Hook (`src/hooks/use-usage-tracking.ts`)
```typescript
import { useCallback } from 'react';
import { useUsageTrackingStore } from '@/stores/usage-tracking-store';

export interface TrackUsageOptions {
  metricId: string;
  quantity: number;
  metadata?: Record<string, any>;
  aggregation?: 'sum' | 'increment';
}

export function useUsageTracking() {
  const { trackUsage } = useUsageTrackingStore();

  const track = useCallback(async (options: TrackUsageOptions) => {
    try {
      await trackUsage(options.metricId, options.quantity, options.metadata);
    } catch (error) {
      console.error('Failed to track usage:', error);
      throw error;
    }
  }, [trackUsage]);

  const trackAPICall = useCallback(async (endpoint: string, metadata?: Record<string, any>) => {
    return track({
      metricId: 'api_calls',
      quantity: 1,
      metadata: {
        endpoint,
        timestamp: new Date().toISOString(),
        ...metadata,
      },
    });
  }, [track]);

  const trackDataTransfer = useCallback(async (bytes: number, type: 'upload' | 'download') => {
    return track({
      metricId: 'data_transfer',
      quantity: bytes,
      metadata: {
        type,
        timestamp: new Date().toISOString(),
      },
    });
  }, [track]);

  const trackStorageUsage = useCallback(async (bytes: number, operation: 'add' | 'remove') => {
    return track({
      metricId: 'storage_usage',
      quantity: operation === 'add' ? bytes : -bytes,
      metadata: {
        operation,
        timestamp: new Date().toISOString(),
      },
    });
  }, [track]);

  const trackComputeTime = useCallback(async (milliseconds: number, resourceType: string) => {
    return track({
      metricId: 'compute_time',
      quantity: milliseconds,
      metadata: {
        resourceType,
        timestamp: new Date().toISOString(),
      },
    });
  }, [track]);

  const trackUserActivity = useCallback(async (activityType: string, metadata?: Record<string, any>) => {
    return track({
      metricId: 'user_activity',
      quantity: 1,
      metadata: {
        activityType,
        timestamp: new Date().toISOString(),
        ...metadata,
      },
    });
  }, [track]);

  return {
    track,
    trackAPICall,
    trackDataTransfer,
    trackStorageUsage,
    trackComputeTime,
    trackUserActivity,
  };
}
```

### 4. Usage Tracking API Routes

#### Track Usage (`src/app/api/usage/track/route.ts`)
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { stripe } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { metricId, quantity, metadata, timestamp } = await request.json();

    // Validate input
    if (!metricId || typeof quantity !== 'number') {
      return NextResponse.json({ error: 'Invalid input' }, { status: 400 });
    }

    // Get user and tenant information
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        tenant: {
          include: {
            subscription: true,
          },
        },
      },
    });

    if (!user || !user.tenant) {
      return NextResponse.json({ error: 'User or tenant not found' }, { status: 404 });
    }

    // Get or create billing meter
    let billingMeter = await prisma.billingMeter.findFirst({
      where: {
        tenantId: user.tenant.id,
        metricId,
      },
    });

    if (!billingMeter) {
      // Create new billing meter
      billingMeter = await prisma.billingMeter.create({
        data: {
          tenantId: user.tenant.id,
          metricId,
          metricName: getMetricName(metricId),
          aggregation: getMetricAggregation(metricId),
          unitPrice: getMetricUnitPrice(metricId),
          currency: 'USD',
          currentQuantity: 0,
          billingPeriodQuantity: 0,
          billingPeriodStart: getBillingPeriodStart(),
          billingPeriodEnd: getBillingPeriodEnd(),
        },
      });
    }

    // Calculate new quantity based on aggregation
    const newQuantity = calculateNewQuantity(
      billingMeter.currentQuantity,
      quantity,
      billingMeter.aggregation
    );

    // Record usage event
    const usageRecord = await prisma.usageRecord.create({
      data: {
        tenantId: user.tenant.id,
        userId: session.user.id,
        billingMeterId: billingMeter.id,
        metricId,
        quantity,
        timestamp: timestamp ? new Date(timestamp) : new Date(),
        metadata: metadata || {},
        cost: quantity * billingMeter.unitPrice,
        currency: billingMeter.currency,
      },
    });

    // Update billing meter
    const updatedMeter = await prisma.billingMeter.update({
      where: { id: billingMeter.id },
      data: {
        currentQuantity: newQuantity,
        billingPeriodQuantity: billingMeter.billingPeriodQuantity + quantity,
        lastUpdated: new Date(),
      },
    });

    // Send usage to Stripe if subscription exists
    if (user.tenant.subscription?.stripeSubscriptionId) {
      await reportUsageToStripe(
        user.tenant.subscription.stripeSubscriptionId,
        metricId,
        quantity,
        timestamp
      );
    }

    return NextResponse.json({
      success: true,
      usageRecord,
      newQuantity,
      billingMeter: updatedMeter,
    });
  } catch (error) {
    console.error('Error tracking usage:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getMetricName(metricId: string): string {
  const metricNames = {
    'api_calls': 'API Calls',
    'data_transfer': 'Data Transfer',
    'storage_usage': 'Storage Usage',
    'compute_time': 'Compute Time',
    'user_activity': 'User Activity',
  };
  return metricNames[metricId] || metricId;
}

function getMetricAggregation(metricId: string): string {
  const aggregations = {
    'api_calls': 'sum',
    'data_transfer': 'sum',
    'storage_usage': 'last_during_period',
    'compute_time': 'sum',
    'user_activity': 'sum',
  };
  return aggregations[metricId] || 'sum';
}

function getMetricUnitPrice(metricId: string): number {
  const prices = {
    'api_calls': 0.001,      // $0.001 per API call
    'data_transfer': 0.0001,  // $0.0001 per MB
    'storage_usage': 0.023,   // $0.023 per GB per month
    'compute_time': 0.0001,   // $0.0001 per second
    'user_activity': 0.01,    // $0.01 per activity
  };
  return prices[metricId] || 0.001;
}

function getBillingPeriodStart(): Date {
  const now = new Date();
  return new Date(now.getFullYear(), now.getMonth(), 1);
}

function getBillingPeriodEnd(): Date {
  const now = new Date();
  return new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
}

function calculateNewQuantity(
  currentQuantity: number,
  addedQuantity: number,
  aggregation: string
): number {
  switch (aggregation) {
    case 'sum':
      return currentQuantity + addedQuantity;
    case 'last_during_period':
    case 'last_ever':
      return addedQuantity;
    case 'max':
      return Math.max(currentQuantity, addedQuantity);
    default:
      return currentQuantity + addedQuantity;
  }
}

async function reportUsageToStripe(
  subscriptionId: string,
  metricId: string,
  quantity: number,
  timestamp?: string
): Promise<void> {
  try {
    // Get subscription items
    const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
      expand: ['items.data.price.product'],
    });

    // Find the subscription item for this metric
    const subscriptionItem = subscription.items.data.find(item => {
      const product = item.price.product;
      return typeof product === 'object' && product.metadata?.metricId === metricId;
    });

    if (!subscriptionItem) {
      console.warn(`No subscription item found for metric ${metricId}`);
      return;
    }

    // Create usage record in Stripe
    await stripe.subscriptionItems.createUsageRecord(subscriptionItem.id, {
      quantity,
      timestamp: timestamp ? Math.floor(new Date(timestamp).getTime() / 1000) : undefined,
      action: 'increment',
    });
  } catch (error) {
    console.error('Failed to report usage to Stripe:', error);
    // Don't throw error to avoid breaking the main flow
  }
}
```

#### Get Current Usage (`src/app/api/usage/current/route.ts`)
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { tenant: true },
    });

    if (!user || !user.tenant) {
      return NextResponse.json({ error: 'User or tenant not found' }, { status: 404 });
    }

    const currentUsage = await prisma.billingMeter.findMany({
      where: {
        tenantId: user.tenant.id,
      },
      orderBy: {
        metricName: 'asc',
      },
    });

    return NextResponse.json(currentUsage);
  } catch (error) {
    console.error('Error fetching current usage:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

#### Get Usage History (`src/app/api/usage/history/route.ts`)
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!startDate || !endDate) {
      return NextResponse.json({ error: 'Start date and end date are required' }, { status: 400 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { tenant: true },
    });

    if (!user || !user.tenant) {
      return NextResponse.json({ error: 'User or tenant not found' }, { status: 404 });
    }

    const usageHistory = await prisma.usageRecord.findMany({
      where: {
        tenantId: user.tenant.id,
        timestamp: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
      },
      include: {
        billingMeter: {
          select: {
            metricName: true,
          },
        },
      },
      orderBy: {
        timestamp: 'desc',
      },
    });

    const formattedHistory = usageHistory.map(record => ({
      id: record.id,
      metricId: record.metricId,
      metricName: record.billingMeter.metricName,
      quantity: record.quantity,
      timestamp: record.timestamp,
      metadata: record.metadata,
      cost: record.cost,
      currency: record.currency,
      billingPeriodStart: record.billingPeriodStart,
      billingPeriodEnd: record.billingPeriodEnd,
    }));

    return NextResponse.json(formattedHistory);
  } catch (error) {
    console.error('Error fetching usage history:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

#### Get Billing Period Usage (`src/app/api/usage/billing-period/route.ts`)
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { tenant: true },
    });

    if (!user || !user.tenant) {
      return NextResponse.json({ error: 'User or tenant not found' }, { status: 404 });
    }

    // Get current billing period
    const now = new Date();
    const billingPeriodStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const billingPeriodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

    // Get usage for current billing period
    const usage = await prisma.billingMeter.findMany({
      where: {
        tenantId: user.tenant.id,
      },
      orderBy: {
        metricName: 'asc',
      },
    });

    // Calculate total cost for billing period
    const totalCost = usage.reduce((sum, meter) => {
      return sum + (meter.billingPeriodQuantity * meter.unitPrice);
    }, 0);

    // Determine billing period status
    const status = now <= billingPeriodEnd ? 'active' : 'ended';

    return NextResponse.json({
      billingPeriod: {
        start: billingPeriodStart,
        end: billingPeriodEnd,
        status,
        totalCost,
        currency: 'USD',
      },
      usage,
    });
  } catch (error) {
    console.error('Error fetching billing period usage:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### 5. Usage Middleware (`src/middleware/usage-tracking.ts`)
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';

export async function usageTrackingMiddleware(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.next();
    }

    // Track API calls automatically
    if (request.url.includes('/api/')) {
      const url = new URL(request.url);
      const endpoint = url.pathname;
      
      // Skip tracking for usage tracking endpoints to avoid infinite loops
      if (endpoint.startsWith('/api/usage/')) {
        return NextResponse.next();
      }

      // Track API call in background
      fetch(`${request.nextUrl.origin}/api/usage/track`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': request.headers.get('Authorization') || '',
        },
        body: JSON.stringify({
          metricId: 'api_calls',
          quantity: 1,
          metadata: {
            endpoint,
            method: request.method,
            userAgent: request.headers.get('User-Agent'),
            ip: request.ip,
          },
        }),
      }).catch(error => {
        console.error('Failed to track API usage:', error);
      });
    }

    return NextResponse.next();
  } catch (error) {
    console.error('Usage tracking middleware error:', error);
    return NextResponse.next();
  }
}
```

### 6. Database Schema Extension (`prisma/schema.prisma`)
```prisma
model BillingMeter {
  id                      String   @id @default(cuid())
  tenantId                String
  metricId                String
  metricName              String
  aggregation             String   @default("sum") // sum, last_during_period, last_ever, max
  unitPrice               Float
  currency                String   @default("USD")
  currentQuantity         Float    @default(0)
  billingPeriodQuantity   Float    @default(0)
  billingPeriodStart      DateTime
  billingPeriodEnd        DateTime
  lastUpdated             DateTime @default(now())
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  tenant       Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  usageRecords UsageRecord[]

  @@unique([tenantId, metricId])
  @@map("billing_meters")
}

model UsageRecord {
  id                  String   @id @default(cuid())
  tenantId            String
  userId              String
  billingMeterId      String
  metricId            String
  quantity            Float
  timestamp           DateTime @default(now())
  metadata            Json     @default("{}")
  cost                Float
  currency            String   @default("USD")
  billingPeriodStart  DateTime
  billingPeriodEnd    DateTime
  createdAt           DateTime @default(now())

  tenant       Tenant       @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  billingMeter BillingMeter @relation(fields: [billingMeterId], references: [id], onDelete: Cascade)

  @@map("usage_records")
}

// Add to existing Tenant model
model Tenant {
  // ... existing fields
  billingMeters BillingMeter[]
  usageRecords  UsageRecord[]
}

// Add to existing User model
model User {
  // ... existing fields
  usageRecords UsageRecord[]
}
```

### 7. Usage Dashboard Page (`src/app/dashboard/usage/page.tsx`)
```tsx
import { Suspense } from 'react';
import { UsageDashboard } from '@/components/usage/usage-dashboard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function UsagePage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Usage & Billing</h1>
        <p className="text-gray-600">
          Monitor your usage metrics and billing information
        </p>
      </div>

      <Suspense fallback={<UsageDashboardSkeleton />}>
        <UsageDashboard />
      </Suspense>
    </div>
  );
}

function UsageDashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64 mt-2" />
        </div>
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-[300px]" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>

      <Skeleton className="h-32 w-full" />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Skeleton className="h-[500px] w-full" />
    </div>
  );
}
```

## Testing Strategy

### 1. Unit Tests (`src/hooks/__tests__/use-usage-tracking.test.ts`)
```typescript
import { renderHook, act } from '@testing-library/react';
import { useUsageTracking } from '../use-usage-tracking';
import { useUsageTrackingStore } from '@/stores/usage-tracking-store';

jest.mock('@/stores/usage-tracking-store');

describe('useUsageTracking', () => {
  const mockTrackUsage = jest.fn();

  beforeEach(() => {
    (useUsageTrackingStore as jest.Mock).mockReturnValue({
      trackUsage: mockTrackUsage,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should track API calls', async () => {
    const { result } = renderHook(() => useUsageTracking());

    await act(async () => {
      await result.current.trackAPICall('/api/users', { userId: '123' });
    });

    expect(mockTrackUsage).toHaveBeenCalledWith('api_calls', 1, {
      endpoint: '/api/users',
      timestamp: expect.any(String),
      userId: '123',
    });
  });

  it('should track data transfer', async () => {
    const { result } = renderHook(() => useUsageTracking());

    await act(async () => {
      await result.current.trackDataTransfer(1024, 'upload');
    });

    expect(mockTrackUsage).toHaveBeenCalledWith('data_transfer', 1024, {
      type: 'upload',
      timestamp: expect.any(String),
    });
  });

  it('should track storage usage', async () => {
    const { result } = renderHook(() => useUsageTracking());

    await act(async () => {
      await result.current.trackStorageUsage(2048, 'add');
    });

    expect(mockTrackUsage).toHaveBeenCalledWith('storage_usage', 2048, {
      operation: 'add',
      timestamp: expect.any(String),
    });
  });
});
```

### 2. Integration Tests (`src/app/api/usage/track/__tests__/route.test.ts`)
```typescript
import { NextRequest } from 'next/server';
import { POST } from '../route';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { stripe } from '@/lib/stripe';

jest.mock('@/lib/auth');
jest.mock('@/lib/prisma');
jest.mock('@/lib/stripe');

describe('/api/usage/track', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should track usage successfully', async () => {
    const mockUser = {
      id: 'user-1',
      tenant: {
        id: 'tenant-1',
        subscription: {
          stripeSubscriptionId: 'sub_123',
        },
      },
    };

    (auth as jest.Mock).mockResolvedValue({ user: { id: 'user-1' } });
    (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
    (prisma.billingMeter.findFirst as jest.Mock).mockResolvedValue({
      id: 'meter-1',
      currentQuantity: 100,
      billingPeriodQuantity: 500,
      aggregation: 'sum',
      unitPrice: 0.001,
      currency: 'USD',
    });
    (prisma.usageRecord.create as jest.Mock).mockResolvedValue({
      id: 'usage-1',
      quantity: 10,
      cost: 0.01,
    });
    (prisma.billingMeter.update as jest.Mock).mockResolvedValue({
      id: 'meter-1',
      currentQuantity: 110,
      billingPeriodQuantity: 510,
    });

    const request = new NextRequest('http://localhost/api/usage/track', {
      method: 'POST',
      body: JSON.stringify({
        metricId: 'api_calls',
        quantity: 10,
        metadata: { endpoint: '/api/users' },
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.newQuantity).toBe(110);
  });

  it('should return 401 for unauthorized users', async () => {
    (auth as jest.Mock).mockResolvedValue(null);

    const request = new NextRequest('http://localhost/api/usage/track', {
      method: 'POST',
      body: JSON.stringify({
        metricId: 'api_calls',
        quantity: 10,
      }),
    });

    const response = await POST(request);

    expect(response.status).toBe(401);
  });

  it('should return 400 for invalid input', async () => {
    (auth as jest.Mock).mockResolvedValue({ user: { id: 'user-1' } });

    const request = new NextRequest('http://localhost/api/usage/track', {
      method: 'POST',
      body: JSON.stringify({
        metricId: '',
        quantity: 'invalid',
      }),
    });

    const response = await POST(request);

    expect(response.status).toBe(400);
  });
});
```

### 3. E2E Tests (`tests/e2e/usage-tracking.spec.ts`)
```typescript
import { test, expect } from '@playwright/test';

test.describe('Usage Tracking', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/dashboard/usage');
  });

  test('should display usage dashboard', async ({ page }) => {
    await expect(page.getByText('Usage & Billing')).toBeVisible();
    await expect(page.getByText('Monitor your usage metrics')).toBeVisible();
    await expect(page.getByText('Current Billing Period')).toBeVisible();
  });

  test('should show usage metrics', async ({ page }) => {
    await expect(page.getByText('API Calls')).toBeVisible();
    await expect(page.getByText('Data Transfer')).toBeVisible();
    await expect(page.getByText('Storage Usage')).toBeVisible();
  });

  test('should display usage chart', async ({ page }) => {
    await page.getByRole('tab', { name: 'Usage Chart' }).click();
    await expect(page.getByText('Usage Over Time')).toBeVisible();
  });

  test('should show usage history', async ({ page }) => {
    await page.getByRole('tab', { name: 'Usage History' }).click();
    await expect(page.getByText('Usage History')).toBeVisible();
  });

  test('should display analytics', async ({ page }) => {
    await page.getByRole('tab', { name: 'Analytics' }).click();
    await expect(page.getByText('Usage Trends')).toBeVisible();
    await expect(page.getByText('Cost Breakdown')).toBeVisible();
  });

  test('should refresh usage data', async ({ page }) => {
    await page.getByRole('button', { name: 'Refresh' }).click();
    // Wait for loading to complete
    await expect(page.getByText('Loading')).not.toBeVisible();
  });
});
```

## Security Considerations

1. **Rate Limiting**: Implement rate limiting on usage tracking endpoints
2. **Data Validation**: Validate all usage data before processing
3. **Access Control**: Ensure users can only track usage for their tenant
4. **Audit Trail**: Log all usage tracking activities
5. **Data Privacy**: Anonymize metadata when necessary
6. **Billing Accuracy**: Implement checks to prevent billing fraud

## Performance Optimizations

1. **Batch Processing**: Batch usage events for better performance
2. **Async Processing**: Process usage tracking asynchronously
3. **Caching**: Cache frequently accessed usage data
4. **Database Optimization**: Optimize queries for large datasets
5. **Background Jobs**: Use background jobs for Stripe synchronization

## Deployment Notes

1. **Environment Variables**: Configure all required environment variables
2. **Database Migration**: Run migrations for usage tracking tables
3. **Stripe Setup**: Configure Stripe products and pricing for metered billing
4. **Monitoring**: Set up monitoring for usage tracking accuracy
5. **Backup Strategy**: Ensure usage data is included in backups

This implementation provides a comprehensive usage-based billing system with real-time tracking, detailed analytics, and seamless Stripe integration for accurate metered billing.
