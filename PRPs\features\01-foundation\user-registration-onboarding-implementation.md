# User Registration & Onboarding Implementation PRP

## Feature Implementation Request

Create a comprehensive user registration and onboarding system for the NEXUS SaaS Starter that provides smooth user workflows, automated email verification, and seamless tenant assignment in a multi-tenant architecture.

## Research Findings

### Better Auth Integration Patterns
Based on Context7 research of the official Better Auth library (874 code snippets, v1.2.9):

**User Registration Flow:**
```typescript
// From better-auth patterns - email signup with verification
const { data, error } = await authClient.signUp.email({
  email: "<EMAIL>",
  password: "securePassword123",
  name: "<PERSON>",
  callbackURL: "/welcome"
}, {
  onRequest: () => setLoading(true),
  onSuccess: (ctx) => router.push("/dashboard"),
  onError: (ctx) => setError(ctx.error.message)
});
```

**Automatic Email Verification:**
```typescript
// Better Auth email verification on signup
export const auth = betterAuth({
  emailVerification: {
    sendOnSignUp: true
  }
});
```

**Post-Registration Hooks:**
```typescript
// Better Auth after hook for user creation
export const auth = betterAuth({
  hooks: {
    after: createAuthMiddleware(async (ctx) => {
      if(ctx.path.startsWith("/sign-up")){
        const newSession = ctx.context.newSession;
        if(newSession){
          await createUserTenant(newSession.user.id);
          await sendWelcomeEmail(newSession.user.email);
        }
      }
    })
  }
});
```

### Next.js 15.4+ Form Patterns
Based on Context7 research of Next.js patterns (12,464 code snippets):

**Client Component with Server Actions:**
```typescript
'use client'
import { useActionState } from 'react'
import { signUp } from '@/app/actions/auth'

export default function SignupForm() {
  const [state, action, pending] = useActionState(signUp, undefined)
  
  return (
    <form action={action}>
      <input name="email" type="email" required />
      <input name="password" type="password" required />
      <button disabled={pending} type="submit">
        {pending ? 'Creating Account...' : 'Sign Up'}
      </button>
      {state?.errors?.email && <p>{state.errors.email}</p>}
    </form>
  )
}
```

**Form Validation with useFormStatus:**
```typescript
'use client'
import { useFormStatus } from 'react-dom'

export function SubmitButton() {
  const { pending } = useFormStatus()
  return (
    <button disabled={pending} type="submit">
      {pending ? 'Creating Account...' : 'Sign Up'}
    </button>
  )
}
```

### Multi-Tenant Architecture Research

**Database Schema Considerations:**
- User-tenant relationship must be established during registration
- Default tenant creation for new users
- Tenant context propagation through session
- Role assignment within tenant scope

**Security Patterns:**
- Email verification before tenant access
- Password strength enforcement
- Rate limiting on registration endpoints
- CSRF protection for forms

## Implementation Blueprint

### Data Models and Structure

**User Registration Enhancement:**
```prisma
// Extend existing User model with onboarding fields
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  emailVerified     DateTime?
  name              String?
  image             String?
  password          String?
  
  // Onboarding fields
  onboardingStep    Int       @default(0)
  onboardingCompleted Boolean @default(false)
  welcomeEmailSent  Boolean   @default(false)
  
  // Relations
  tenantMemberships TenantMembership[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Tenant assignment during registration
model TenantMembership {
  id       String @id @default(cuid())
  userId   String
  tenantId String
  role     String @default("member")
  
  user   User   @relation(fields: [userId], references: [id])
  tenant Tenant @relation(fields: [tenantId], references: [id])
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([userId, tenantId])
}
```

**TypeScript Interfaces:**
```typescript
// User registration types
interface UserRegistrationData {
  email: string;
  password: string;
  name: string;
  company?: string;
  acceptTerms: boolean;
}

interface OnboardingState {
  step: number;
  completed: boolean;
  data: Record<string, any>;
}

interface RegistrationResult {
  success: boolean;
  user?: User;
  errors?: Record<string, string>;
  redirectTo?: string;
}
```

**Validation Schemas (valibot):**
```typescript
import * as v from 'valibot';

export const RegistrationSchema = v.object({
  email: v.pipe(
    v.string(),
    v.email('Please enter a valid email address')
  ),
  password: v.pipe(
    v.string(),
    v.minLength(8, 'Password must be at least 8 characters'),
    v.regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number')
  ),
  name: v.pipe(
    v.string(),
    v.minLength(2, 'Name must be at least 2 characters')
  ),
  company: v.optional(v.string()),
  acceptTerms: v.boolean('You must accept the terms and conditions')
});
```

### Task Breakdown

**Phase 1: Registration Form Components**
```typescript
// Pseudocode approach:
// 1. Create registration form with client-side validation
// 2. Implement real-time password strength indicator
// 3. Add email availability checking
// 4. Create loading states and error handling
// 5. Implement accessibility features
```

**Tasks:**
1. **Create Registration Form Component** (`src/components/auth/registration-form.tsx`)
   - Follow existing form patterns from `src/components/ui/form.tsx`
   - Use shadcn/ui components for consistency
   - Implement client-side validation with valibot
   - Add real-time password strength indicator
   - Include email availability checking
   - Add CSRF protection

2. **Implement Registration Server Action** (`src/app/actions/auth.ts`)
   - Follow existing auth action patterns
   - Integrate with Better Auth signup flow
   - Add email verification trigger
   - Create default tenant for new users
   - Implement rate limiting
   - Add audit logging

3. **Create Onboarding Flow Components** (`src/components/onboarding/`)
   - Multi-step onboarding wizard
   - Progress indicator component
   - Step validation and navigation
   - Data persistence between steps
   - Skip options for optional steps

4. **Email Verification Enhancement** (`src/lib/auth/email-verification.ts`)
   - Customize email templates
   - Add resend verification functionality
   - Handle verification callbacks
   - Update user onboarding state

5. **Tenant Assignment Logic** (`src/lib/tenant/assignment.ts`)
   - Create default tenant for new users
   - Assign initial role permissions
   - Generate tenant subdomain if needed
   - Set up tenant-specific resources

**Phase 2: Integration Points**

6. **Database Migrations** (`prisma/migrations/`)
   - Add onboarding fields to User model
   - Create indices for performance
   - Add constraints for data integrity

7. **API Endpoints** (`src/app/api/auth/`)
   - Registration endpoint with validation
   - Email verification callback
   - Onboarding progress tracking
   - User profile completion

8. **Middleware Updates** (`src/middleware.ts`)
   - Onboarding completion checks
   - Redirect incomplete users to onboarding
   - Protect authenticated routes

9. **Authentication Flow Integration** (`src/app/(auth)/`)
   - Registration page with form
   - Email verification page
   - Onboarding flow pages
   - Welcome/success pages

**Phase 3: User Experience Enhancements**

10. **Progressive Enhancement** (`src/components/auth/`)
    - Form works without JavaScript
    - Enhanced UX with client-side validation
    - Optimistic updates for better perceived performance
    - Accessibility compliance (WCAG 2.1 AA)

11. **Error Handling** (`src/lib/errors/`)
    - User-friendly error messages
    - Retry mechanisms for failed operations
    - Graceful degradation for network issues
    - Comprehensive logging

12. **Analytics Integration** (`src/lib/analytics/`)
    - Track registration funnel metrics
    - Monitor onboarding completion rates
    - A/B test different onboarding flows
    - User behavior analytics

### Integration Points

**Database Changes:**
- Add onboarding fields to User model
- Create TenantMembership relationship
- Add indices for email lookup performance
- Implement soft delete for user accounts

**API Endpoint Modifications:**
- `/api/auth/register` - User registration
- `/api/auth/verify-email` - Email verification
- `/api/onboarding/progress` - Track onboarding progress
- `/api/onboarding/complete` - Complete onboarding

**Frontend Component Updates:**
- Registration form with validation
- Email verification UI
- Onboarding wizard
- Progress indicators
- Error state handling

**Authentication and Authorization:**
- Email verification requirement
- Onboarding completion gates
- Tenant access control
- Role-based permissions

**Email System Integration:**
- Welcome email templates
- Verification email customization
- Onboarding reminder sequences
- Transactional email tracking

## Validation Gates

### Level 1: Syntax & Style
```bash
npm run lint                    # ESLint checks
npx tsc --noEmit               # TypeScript type checking
npm run format                 # Prettier formatting
```

### Level 2: Unit Tests
```bash
npm test                       # Jest/React Testing Library tests

# Test files to create:
# __tests__/components/auth/registration-form.test.tsx
# __tests__/lib/auth/registration.test.ts
# __tests__/lib/tenant/assignment.test.ts
```

### Level 3: Integration Tests
```bash
npm run dev                    # Start development server

# Test registration flow:
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "name": "Test User",
    "acceptTerms": true
  }'

# Test email verification:
curl -X GET http://localhost:3000/api/auth/verify-email?token=test-token

# Test onboarding progress:
curl -X POST http://localhost:3000/api/onboarding/progress \
  -H "Authorization: Bearer {session-token}" \
  -H "Content-Type: application/json" \
  -d '{"step": 2, "data": {"company": "Test Corp"}}'
```

### Level 4: End-to-End Tests
```bash
npm run build                  # Production build validation
npm run start                  # Production server testing

# Playwright tests:
# tests/e2e/auth/registration.spec.ts
# tests/e2e/onboarding/flow.spec.ts
```

### Feature-Specific Validation

**Registration Flow Testing:**
- Form validation with various input combinations
- Email verification link functionality
- Password strength requirements
- CSRF protection validation
- Rate limiting enforcement

**Multi-Tenant Testing:**
- Tenant creation on user registration
- Proper tenant assignment
- Role permission verification
- Tenant isolation validation

**Performance Testing:**
- Registration endpoint response time < 500ms
- Email sending performance
- Database query optimization
- Form rendering performance

**Security Testing:**
- SQL injection prevention
- XSS protection in forms
- CSRF token validation
- Rate limiting effectiveness
- Password hashing verification

**Accessibility Testing:**
- WCAG 2.1 AA compliance
- Screen reader compatibility
- Keyboard navigation support
- Color contrast validation
- Focus management

## Quality Standards Checklist

- [ ] All necessary context for one-pass implementation
- [ ] Validation gates that are executable by AI
- [ ] References to existing codebase patterns
- [ ] Clear implementation path with specific tasks
- [ ] Error handling and edge cases documented
- [ ] Multi-tenant architecture considerations
- [ ] Security and compliance requirements
- [ ] Performance and scalability considerations
- [ ] Integration testing scenarios
- [ ] Documentation and deployment steps

## Additional Context

### Codebase Patterns to Follow
- Use existing form components from `src/components/ui/`
- Follow authentication patterns from `src/lib/auth/`
- Implement tenant context from `src/lib/tenant/`
- Use existing error handling from `src/lib/errors/`
- Follow database patterns from `src/lib/db/`

### Technology Stack Requirements
- Next.js 15.4+ with App Router
- React 19 with Server Components
- TypeScript 5.8+ with strict mode
- Better Auth v1.2.9 for authentication
- Prisma for database operations
- Valibot for validation
- Tailwind CSS 4.0+ for styling
- Shadcn/ui for components

### Multi-Tenant Considerations
- Tenant isolation at the database level
- Role-based access control within tenants
- Tenant-specific onboarding customization
- Scalable tenant assignment logic
- Tenant resource provisioning

### Performance Optimization
- Lazy loading for onboarding components
- Optimistic updates for form interactions
- Efficient database queries with proper indexing
- CDN integration for static assets
- Server-side rendering for SEO

This PRP provides comprehensive guidance for implementing a production-ready user registration and onboarding system that integrates seamlessly with the existing NEXUS SaaS Starter architecture while maintaining security, performance, and scalability standards.
