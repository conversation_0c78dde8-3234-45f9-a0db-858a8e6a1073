# NEXUS SaaS Starter - Core Package Structure Implementation

**PRP Name**: Core Package Structure  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Foundation Implementation PRP  
**Phase**: 01-foundation  
**Framework**: Next.js 15.4+ / TypeScript 5.8+ / Turborepo  

---

## Purpose

Establish a scalable, modular package structure that supports multi-tenant SaaS architecture with clear separation of concerns, reusable components, and enterprise-grade code organization.

## Core Principles

1. **Modular Architecture**: Clear separation between packages and domains
2. **Reusability**: Shared utilities and components across applications
3. **Type Safety**: Comprehensive TypeScript definitions and contracts
4. **Scalability**: Structure that grows with team and feature complexity
5. **Maintainability**: Clear dependencies and module boundaries
6. **Enterprise Ready**: Supports multiple environments and deployment targets

---

## Goal

Create a production-ready package structure that supports multi-tenant SaaS development with shared libraries, type definitions, configurations, and utilities that can be consumed across multiple applications and services.

## Why

- **Code Reusability**: Shared packages eliminate duplication across applications
- **Type Safety**: Centralized type definitions ensure consistency
- **Scalability**: Clear module boundaries support team growth
- **Maintainability**: Isolated packages with clear dependencies
- **Testing**: Focused testing strategies for each package
- **Deployment**: Independent versioning and deployment of packages

## What

A comprehensive package structure with:
- Core utilities and shared functionality
- Type definitions and contracts
- UI component libraries
- Configuration management
- Database schemas and migrations
- API client libraries
- Testing utilities and mocks

### Success Criteria

- [ ] Modular package structure with clear boundaries
- [ ] Shared utilities and components across applications
- [ ] Comprehensive type definitions and contracts
- [ ] Configuration management system
- [ ] Database schema and migration packages
- [ ] API client libraries with type safety
- [ ] Testing utilities and mock data
- [ ] Documentation and usage examples

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://turbo.build/repo/docs/getting-started
  why: Turborepo monorepo management and build optimization
  critical: Understanding monorepo architecture and package management

- url: https://nextjs.org/docs/app/building-your-application/configuring/typescript
  why: Next.js 15.4+ TypeScript integration and configuration
  critical: TypeScript setup and compilation in Next.js

- url: https://www.typescriptlang.org/docs/handbook/project-references.html
  why: TypeScript project references for multi-package setups
  critical: TypeScript compilation and type checking across packages

- url: https://docs.npmjs.com/cli/v10/using-npm/workspaces
  why: npm workspaces for monorepo package management
  critical: Package management and dependency resolution

- url: https://pnpm.io/workspaces
  why: pnpm workspaces for efficient package management
  critical: Package installation and linking in monorepos

- url: https://nx.dev/concepts/more-concepts/library-types
  why: Library types and architectural patterns
  critical: Package organization and dependency management

- url: https://www.typescriptlang.org/docs/handbook/declaration-files/introduction.html
  why: TypeScript declaration files for type definitions
  critical: Creating and maintaining type definitions

- url: https://jestjs.io/docs/configuration#projects-arraystring--projectconfig
  why: Jest configuration for multi-package testing
  critical: Testing setup across multiple packages

- url: https://eslint.org/docs/latest/use/configure/configuration-files
  why: ESLint configuration for multi-package setups
  critical: Linting rules and configuration inheritance

- url: https://prettier.io/docs/en/configuration.html
  why: Prettier configuration for consistent code formatting
  critical: Code formatting across packages and applications
```

### Current Technology Stack

```yaml
# Package Management
- pnpm: 8.15+ (workspace support, efficient node_modules)
- Turborepo: 1.12+ (monorepo build optimization)
- npm workspaces: Package management and linking

# Build Tools
- Next.js: 15.4.1 (App Router, Turbopack, Server Components)
- TypeScript: 5.8+ (strict mode, project references)
- ESLint: 9+ (flat config, shared configurations)
- Prettier: 3.2+ (code formatting)
- PostCSS: 8.4+ (CSS processing)
- Tailwind CSS: 4.0+ (utility-first CSS)

# Testing Framework
- Jest: 29+ (unit testing, configuration)
- Playwright: 1.40+ (end-to-end testing)
- Testing Library: 14+ (React testing utilities)
- Storybook: 7.6+ (component documentation)

# Development Tools
- Husky: 9+ (Git hooks)
- lint-staged: 15+ (staged file linting)
- commitizen: 4.3+ (commit message formatting)
- changeset: 2.27+ (versioning and changelogs)
```

### Known Gotchas & Library Quirks

```typescript
// CRITICAL: pnpm + Next.js 15.4+ gotchas
// Hoisting: pnpm may not hoist dependencies correctly for Next.js
// Peer dependencies: Must be explicitly installed in consuming packages
// Symlinks: pnpm creates symlinks that may cause issues with some tools
// Cache: pnpm cache may not work correctly with Turbopack
// Workspace dependencies: Must use workspace: protocol for local packages
// Next.js plugin: May not resolve correctly with pnpm workspaces
// TypeScript paths: Path mapping may not work across packages
// ESM/CJS: Mixed module types can cause import issues
// Build cache: Turborepo cache may not work with all Next.js features
// Hot reload: Development server may not detect changes in linked packages

// CRITICAL: TypeScript + Monorepo gotchas
// Project references: Must be configured correctly for type checking
// Path mapping: baseUrl and paths must be configured in each package
// Declaration files: Must be generated correctly for consuming packages
// Circular dependencies: Easy to create circular dependencies between packages
// Type resolution: TypeScript may not resolve types correctly across packages
// Compilation order: Dependencies must be compiled before dependents
// Incremental builds: May not work correctly with project references
// Module resolution: Node module resolution may not work as expected
// Import paths: Relative vs absolute imports can cause issues
// Type exports: Must export types correctly from package entry points

// CRITICAL: Turborepo + Next.js gotchas
// Build dependencies: Must define correct build dependencies
// Cache invalidation: Cache may not invalidate correctly for all changes
// Environment variables: Must be configured correctly for each package
// Parallel builds: May cause issues with shared resources
// Output directories: Must be configured correctly for each package
// Development mode: May not work correctly with all Next.js features
// Remote caching: May not work correctly with all file types
// Pipeline configuration: Must be configured correctly for each task
```

---

## Implementation Blueprint

### Package Structure Architecture

```
packages/
├── config/                     # Configuration packages
│   ├── eslint-config/         # Shared ESLint configurations
│   ├── prettier-config/       # Shared Prettier configurations
│   ├── tailwind-config/       # Shared Tailwind CSS configurations
│   └── tsconfig/              # Shared TypeScript configurations
├── core/                      # Core business logic packages
│   ├── types/                 # Shared type definitions
│   ├── utils/                 # Shared utility functions
│   ├── constants/             # Application constants
│   └── validation/            # Shared validation schemas
├── database/                  # Database-related packages
│   ├── schema/                # Database schema definitions
│   ├── migrations/            # Database migrations
│   └── seeders/               # Database seeders
├── api/                       # API-related packages
│   ├── client/                # API client library
│   ├── types/                 # API type definitions
│   └── mock/                  # API mock data and utilities
├── ui/                        # UI-related packages
│   ├── components/            # Shared UI components
│   ├── icons/                 # Icon components
│   ├── themes/                # Theme configurations
│   └── hooks/                 # Shared React hooks
├── auth/                      # Authentication packages
│   ├── client/                # Authentication client
│   ├── server/                # Authentication server utilities
│   └── types/                 # Authentication type definitions
├── tenant/                    # Multi-tenant packages
│   ├── context/               # Tenant context utilities
│   ├── middleware/            # Tenant middleware
│   └── types/                 # Tenant type definitions
└── testing/                   # Testing utilities
    ├── mocks/                 # Mock data and utilities
    ├── fixtures/              # Test fixtures
    └── setup/                 # Test setup utilities
```

### Core Package Configurations

```json
// packages/core/types/package.json
{
  "name": "@nexus/types",
  "version": "0.1.0",
  "description": "Shared type definitions for Nexus SaaS",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "import": "./dist/index.js",
      "require": "./dist/index.js"
    }
  },
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "clean": "rm -rf dist",
    "type-check": "tsc --noEmit"
  },
  "devDependencies": {
    "typescript": "^5.8.0"
  }
}
```

```json
// packages/core/utils/package.json
{
  "name": "@nexus/utils",
  "version": "0.1.0",
  "description": "Shared utility functions for Nexus SaaS",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "import": "./dist/index.js",
      "require": "./dist/index.js"
    }
  },
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "clean": "rm -rf dist",
    "test": "jest",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "@nexus/types": "workspace:*"
  },
  "devDependencies": {
    "@types/jest": "^29.5.0",
    "jest": "^29.5.0",
    "typescript": "^5.8.0"
  }
}
```

### Task Breakdown

```yaml
Task 1: Package Structure Setup
CREATE packages/ directory structure:
  - SETUP config/ packages for shared configurations
  - CREATE core/ packages for business logic
  - SETUP database/ packages for data management
  - CREATE api/ packages for API management
  - SETUP ui/ packages for components
  - CREATE auth/ packages for authentication
  - SETUP tenant/ packages for multi-tenancy
  - CREATE testing/ packages for test utilities

Task 2: Configuration Packages
CREATE config packages:
  - SETUP @nexus/eslint-config with shared rules
  - CREATE @nexus/prettier-config for formatting
  - SETUP @nexus/tailwind-config for styling
  - CREATE @nexus/tsconfig for TypeScript
  - INTEGRATE Husky and lint-staged configurations

Task 3: Core Business Logic
CREATE core packages:
  - SETUP @nexus/types with shared type definitions
  - CREATE @nexus/utils with utility functions
  - SETUP @nexus/constants for application constants
  - CREATE @nexus/validation with schema validation

Task 4: Database Packages
CREATE database packages:
  - SETUP @nexus/database-schema with Prisma schema
  - CREATE @nexus/database-migrations for migrations
  - SETUP @nexus/database-seeders for seed data
  - INTEGRATE database connection utilities

Task 5: API Packages
CREATE api packages:
  - SETUP @nexus/api-client with typed API client
  - CREATE @nexus/api-types for API contracts
  - SETUP @nexus/api-mock for mock data
  - INTEGRATE OpenAPI specifications

Task 6: UI Packages
CREATE ui packages:
  - SETUP @nexus/ui-components with React components
  - CREATE @nexus/ui-icons for icon components
  - SETUP @nexus/ui-themes for theme management
  - CREATE @nexus/ui-hooks for React hooks

Task 7: Authentication Packages
CREATE auth packages:
  - SETUP @nexus/auth-client for client-side auth
  - CREATE @nexus/auth-server for server-side auth
  - SETUP @nexus/auth-types for auth type definitions
  - INTEGRATE better-auth configurations

Task 8: Multi-Tenant Packages
CREATE tenant packages:
  - SETUP @nexus/tenant-context for tenant management
  - CREATE @nexus/tenant-middleware for request handling
  - SETUP @nexus/tenant-types for tenant definitions
  - INTEGRATE row-level security utilities

Task 9: Testing Packages
CREATE testing packages:
  - SETUP @nexus/testing-mocks for mock data
  - CREATE @nexus/testing-fixtures for test fixtures
  - SETUP @nexus/testing-setup for test configuration
  - INTEGRATE Jest and Playwright utilities

Task 10: Build System Integration
INTEGRATE build system:
  - SETUP Turborepo configuration
  - CREATE build pipelines for each package
  - INTEGRATE TypeScript project references
  - CONFIGURE development and production builds
```

### Integration Points

```yaml
# Monorepo Integration
- Turborepo build optimization
- pnpm workspace dependency management
- TypeScript project references
- ESLint and Prettier configuration sharing
- Jest multi-package testing

# Next.js Integration
- App Router package consumption
- Server Components with shared packages
- Middleware integration with tenant packages
- API routes with shared utilities
- Type safety across applications

# Database Integration
- Prisma schema sharing
- Migration management
- Seed data utilities
- Connection pooling
- Row-level security integration

# Authentication Integration
- better-auth package integration
- Session management utilities
- Role-based access control
- Multi-tenant authentication
- API authentication middleware

# UI Integration
- Component library consumption
- Theme and styling consistency
- Icon library integration
- Hook sharing across applications
- Storybook documentation
```

---

## Validation Gates

### Level 1: Package Structure
```bash
# Verify package structure
tree packages/
ls -la packages/*/package.json

# Check workspace configuration
pnpm list --depth=0
```

### Level 2: Build System
```bash
# Test package builds
pnpm run build --filter=@nexus/types
pnpm run build --filter=@nexus/utils

# Verify Turborepo integration
turbo run build
turbo run test
```

### Level 3: Type Safety
```bash
# Type checking across packages
pnpm run type-check
tsc --build

# Verify type exports
pnpm run build --filter=@nexus/types
node -e "console.log(require('./packages/core/types/dist/index.js'))"
```

### Level 4: Dependency Management
```bash
# Check workspace dependencies
pnpm list --depth=1
pnpm audit

# Verify package linking
pnpm run dev --filter=@nexus/utils
```

### Level 5: Integration Testing
```bash
# Test package consumption
pnpm run test --filter=@nexus/utils
pnpm run test --filter=@nexus/validation

# End-to-end package testing
pnpm run test:e2e
```

---

## Quality Standards

The PRP must include:
- [x] Comprehensive package structure with clear boundaries
- [x] Shared configuration packages for consistency
- [x] Core business logic packages with type safety
- [x] Database packages with schema and migrations
- [x] API packages with client and type definitions
- [x] UI packages with components and themes
- [x] Authentication packages with client and server utilities
- [x] Multi-tenant packages with context and middleware
- [x] Testing packages with mocks and fixtures
- [x] Build system integration with Turborepo
- [x] TypeScript project references configuration
- [x] Documentation and usage examples

---

## Expected Outcomes

Upon successful implementation:

1. **Modularity**: Clear separation of concerns across packages
2. **Reusability**: Shared utilities and components across applications
3. **Type Safety**: Comprehensive type definitions and contracts
4. **Scalability**: Structure that supports team and feature growth
5. **Maintainability**: Isolated packages with clear dependencies
6. **Performance**: Optimized builds with Turborepo caching
7. **Developer Experience**: Clear APIs and documentation

---

**Framework**: NEXUS SaaS Starter Multi-Tenant Architecture  
**Technology Stack**: Next.js 15.4+ / TypeScript 5.8+ / Turborepo  
**Optimization**: Production-ready, enterprise-grade package structure
