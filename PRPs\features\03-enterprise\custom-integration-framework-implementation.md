# Custom Integration Framework - Implementation PRP

**PRP Name**: Custom Integration Framework  
**Version**: 1.0  
**Date**: January 18, 2025  
**Type**: Enterprise Feature Implementation PRP  
**Framework**: Next.js 15.4+ / React 19 / TypeScript 5.8+  
**Phase**: 03-Enterprise (Sprint 15-16: Integration Framework)  
**Priority**: High - Final Integration Framework Component  

---

## Purpose

Implement a comprehensive Custom Integration Framework that provides developers with tools, templates, and workflows for building custom integrations with third-party services. This framework enables users to create, deploy, and manage custom integrations without requiring deep technical knowledge of the underlying platform.

## Core Principles

1. **Developer-First Experience**: Intuitive tools and clear documentation
2. **Template-Driven Development**: Pre-built templates for common integration patterns
3. **Visual Workflow Builder**: Drag-and-drop interface for non-technical users
4. **Multi-Tenant Isolation**: Complete tenant separation for custom integrations
5. **Enterprise Security**: Built-in security controls and audit logging
6. **Scalable Architecture**: Handle enterprise-scale integration workloads

---

## Goal

Build a complete custom integration framework that enables tenants to:
- Create custom integrations using visual workflows or code templates
- Deploy integrations securely within their tenant boundary
- Monitor integration performance and health
- Manage integration lifecycle (create, update, deploy, retire)
- Share integration templates within their organization

## Why

- **Competitive Advantage**: Enable customers to build unique integrations
- **Reduced Support Burden**: Self-service integration development
- **Revenue Growth**: Premium feature for enterprise customers
- **Platform Stickiness**: Custom integrations increase customer retention
- **Ecosystem Growth**: Community-driven integration marketplace

## What

A comprehensive integration framework with:
- Visual workflow builder with drag-and-drop interface
- Code-based integration templates and scaffolding
- Integration runtime environment with monitoring
- Template marketplace and sharing system
- Security controls and tenant isolation
- Performance monitoring and analytics

### Success Criteria

- [ ] Visual workflow builder with 20+ pre-built connectors
- [ ] Code template system with TypeScript/JavaScript support
- [ ] Integration runtime with auto-scaling capabilities
- [ ] Template marketplace with sharing and versioning
- [ ] Complete tenant isolation for custom integrations
- [ ] Real-time monitoring and alerting system
- [ ] Security audit logging for all integration activities
- [ ] Performance benchmarks: <500ms execution time for simple workflows
- [ ] Documentation and tutorials for integration development
- [ ] Migration tools for existing integrations

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://docs.zapier.com/platform/build
  why: Industry-leading integration platform patterns and developer experience
  critical: Workflow builder patterns and integration lifecycle management

- url: https://docs.microsoft.com/en-us/power-automate/
  why: Enterprise workflow automation patterns and visual builder design
  critical: Visual workflow design patterns and enterprise security

- url: https://docs.n8n.io/
  why: Open-source workflow automation with node-based editor
  critical: Node-based workflow editor and custom node development

- url: https://docs.temporal.io/
  why: Reliable workflow orchestration and state management
  critical: Workflow execution reliability and error handling

- url: https://github.com/datadog/integrations-core
  why: Enterprise integration framework patterns and best practices
  critical: Integration configuration patterns and monitoring

- url: https://docs.github.com/en/actions/creating-actions
  why: Action-based workflow system and marketplace patterns
  critical: Action composition and marketplace design

- url: https://docs.aws.amazon.com/step-functions/
  why: Serverless workflow orchestration and state machines
  critical: State machine patterns and error handling

- url: https://supabase.com/docs/guides/functions
  why: Serverless function deployment and edge computing
  critical: Function deployment and execution environment

- url: https://better-auth.com/docs/plugins
  why: Plugin architecture and extensibility patterns
  critical: Plugin system design and security boundaries

- url: https://nextjs.org/docs/app/building-your-application/routing/middleware
  why: Next.js middleware for request processing and tenant isolation
  critical: Request routing and tenant context injection

- file: PRPs/features/03-enterprise/webhook-system-implementation.md
  why: Existing webhook system integration patterns
  critical: Event-driven architecture and webhook handling

- file: PRPs/features/03-enterprise/third-party-service-integrations-implementation.md
  why: Third-party service integration patterns and authentication
  critical: OAuth flows and service authentication

- file: PRPs/features/03-enterprise/sdk-generation-implementation.md
  why: SDK generation patterns for integration development
  critical: Code generation and developer tooling

- file: src/app/layout.tsx
  why: Current application structure and routing patterns
  critical: App router structure and middleware integration

- file: package.json
  why: Current dependencies and build configuration
  critical: Technology stack versions and compatibility
```

### Current Codebase Patterns

```typescript
// Multi-tenant context pattern from existing codebase
interface TenantContext {
  tenantId: string;
  workspaceId: string;
  userId: string;
  permissions: string[];
}

// Existing webhook system pattern
interface WebhookConfig {
  id: string;
  tenantId: string;
  url: string;
  events: string[];
  secret: string;
  active: boolean;
}

// Integration authentication pattern
interface IntegrationAuth {
  type: 'oauth2' | 'api_key' | 'basic' | 'bearer';
  credentials: Record<string, string>;
  scopes?: string[];
  refreshToken?: string;
}
```

### Technology Stack

```yaml
Core Framework:
  - Next.js: 15.4+
  - React: 19
  - TypeScript: 5.8+
  - Tailwind CSS: 4.1.11+

Backend Services:
  - Supabase: Database and real-time subscriptions
  - Better-Auth: Authentication and authorization
  - Prisma: ORM with multi-tenant support

Integration Runtime:
  - Supabase Edge Functions: Serverless execution
  - Temporal: Workflow orchestration (optional)
  - Redis: Caching and session management

Frontend Components:
  - React Flow: Visual workflow builder
  - Monaco Editor: Code editor for templates
  - Shadcn/UI: Component library
  - React Hook Form: Form management

Monitoring & Analytics:
  - Supabase Analytics: Usage tracking
  - Custom metrics: Performance monitoring
  - Audit logging: Security and compliance
```

---

## Data Models and Structure

### Database Schema (Prisma)

```prisma
// Integration Templates
model IntegrationTemplate {
  id          String   @id @default(cuid())
  tenantId    String
  name        String
  description String?
  category    String
  type        IntegrationType
  version     String   @default("1.0.0")
  
  // Template content
  workflow    Json?    // Visual workflow definition
  code        String?  // Code template
  config      Json     // Configuration schema
  
  // Metadata
  isPublic    Boolean  @default(false)
  isOfficial  Boolean  @default(false)
  downloads   Int      @default(0)
  rating      Float?
  
  // Relationships
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  instances   IntegrationInstance[]
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("integration_templates")
  @@index([tenantId])
  @@index([category])
  @@index([isPublic])
}

// Integration Instances (Deployed Integrations)
model IntegrationInstance {
  id           String   @id @default(cuid())
  tenantId     String
  templateId   String
  name         String
  description  String?
  
  // Configuration
  config       Json     // Instance-specific configuration
  environment  String   @default("production") // production, staging, development
  status       IntegrationStatus @default(DRAFT)
  
  // Runtime
  lastRun      DateTime?
  nextRun      DateTime?
  runCount     Int      @default(0)
  errorCount   Int      @default(0)
  
  // Relationships
  tenant       Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  template     IntegrationTemplate @relation(fields: [templateId], references: [id])
  executions   IntegrationExecution[]
  
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  @@map("integration_instances")
  @@index([tenantId])
  @@index([status])
  @@index([nextRun])
}

// Integration Executions (Run History)
model IntegrationExecution {
  id           String   @id @default(cuid())
  instanceId   String
  tenantId     String
  
  // Execution details
  status       ExecutionStatus
  startedAt    DateTime @default(now())
  completedAt  DateTime?
  duration     Int?     // milliseconds
  
  // Data
  input        Json?
  output       Json?
  error        String?
  logs         Json[]   @default([])
  
  // Metrics
  stepsTotal   Int      @default(0)
  stepsSuccess Int      @default(0)
  stepsFailed  Int      @default(0)
  
  // Relationships
  instance     IntegrationInstance @relation(fields: [instanceId], references: [id], onDelete: Cascade)
  
  @@map("integration_executions")
  @@index([instanceId])
  @@index([tenantId])
  @@index([status])
  @@index([startedAt])
}

// Integration Connectors (Available Services)
model IntegrationConnector {
  id           String   @id @default(cuid())
  name         String   @unique
  displayName  String
  description  String
  category     String
  
  // Configuration
  authType     String   // oauth2, api_key, basic, bearer
  configSchema Json     // JSON schema for configuration
  
  // Metadata
  iconUrl      String?
  documentationUrl String?
  isOfficial   Boolean  @default(false)
  isActive     Boolean  @default(true)
  
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  @@map("integration_connectors")
  @@index([category])
  @@index([isActive])
}

enum IntegrationType {
  VISUAL_WORKFLOW
  CODE_TEMPLATE
  HYBRID
}

enum IntegrationStatus {
  DRAFT
  TESTING
  ACTIVE
  PAUSED
  ERROR
  ARCHIVED
}

enum ExecutionStatus {
  PENDING
  RUNNING
  SUCCESS
  FAILED
  CANCELLED
  TIMEOUT
}
```

### TypeScript Interfaces

```typescript
// Workflow Definition
interface WorkflowDefinition {
  id: string;
  name: string;
  version: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  variables: WorkflowVariable[];
  triggers: WorkflowTrigger[];
}

interface WorkflowNode {
  id: string;
  type: 'trigger' | 'action' | 'condition' | 'transform' | 'delay';
  position: { x: number; y: number };
  data: {
    connector?: string;
    operation: string;
    config: Record<string, any>;
    mapping?: FieldMapping[];
  };
}

interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  condition?: string;
}

// Integration Configuration
interface IntegrationConfig {
  name: string;
  description?: string;
  schedule?: ScheduleConfig;
  authentication: AuthConfig;
  settings: Record<string, any>;
  errorHandling: ErrorHandlingConfig;
}

interface ScheduleConfig {
  type: 'manual' | 'interval' | 'cron' | 'webhook';
  interval?: number; // minutes
  cron?: string;
  timezone?: string;
}

interface ErrorHandlingConfig {
  retryCount: number;
  retryDelay: number; // seconds
  onError: 'stop' | 'continue' | 'notify';
  notificationChannels?: string[];
}

// Execution Context
interface ExecutionContext {
  executionId: string;
  instanceId: string;
  tenantId: string;
  userId: string;
  startTime: Date;
  variables: Record<string, any>;
  secrets: Record<string, string>;
}
```

### Validation Schemas (Valibot)

```typescript
import * as v from 'valibot';

export const WorkflowDefinitionSchema = v.object({
  id: v.string(),
  name: v.pipe(v.string(), v.minLength(1), v.maxLength(100)),
  version: v.pipe(v.string(), v.regex(/^\d+\.\d+\.\d+$/)),
  nodes: v.array(v.object({
    id: v.string(),
    type: v.picklist(['trigger', 'action', 'condition', 'transform', 'delay']),
    position: v.object({
      x: v.number(),
      y: v.number(),
    }),
    data: v.object({
      connector: v.optional(v.string()),
      operation: v.string(),
      config: v.record(v.string(), v.any()),
      mapping: v.optional(v.array(v.object({
        source: v.string(),
        target: v.string(),
        transform: v.optional(v.string()),
      }))),
    }),
  })),
  edges: v.array(v.object({
    id: v.string(),
    source: v.string(),
    target: v.string(),
    sourceHandle: v.optional(v.string()),
    targetHandle: v.optional(v.string()),
    condition: v.optional(v.string()),
  })),
  variables: v.array(v.object({
    name: v.string(),
    type: v.picklist(['string', 'number', 'boolean', 'object', 'array']),
    defaultValue: v.optional(v.any()),
    required: v.boolean(),
  })),
  triggers: v.array(v.object({
    type: v.picklist(['webhook', 'schedule', 'manual']),
    config: v.record(v.string(), v.any()),
  })),
});

export const IntegrationConfigSchema = v.object({
  name: v.pipe(v.string(), v.minLength(1), v.maxLength(100)),
  description: v.optional(v.pipe(v.string(), v.maxLength(500))),
  schedule: v.optional(v.object({
    type: v.picklist(['manual', 'interval', 'cron', 'webhook']),
    interval: v.optional(v.pipe(v.number(), v.minValue(1))),
    cron: v.optional(v.string()),
    timezone: v.optional(v.string()),
  })),
  authentication: v.object({
    type: v.picklist(['oauth2', 'api_key', 'basic', 'bearer']),
    credentials: v.record(v.string(), v.string()),
    scopes: v.optional(v.array(v.string())),
  }),
  settings: v.record(v.string(), v.any()),
  errorHandling: v.object({
    retryCount: v.pipe(v.number(), v.minValue(0), v.maxValue(10)),
    retryDelay: v.pipe(v.number(), v.minValue(1), v.maxValue(3600)),
    onError: v.picklist(['stop', 'continue', 'notify']),
    notificationChannels: v.optional(v.array(v.string())),
  }),
});
```

---

## Task Breakdown

### Phase 1: Core Infrastructure (Week 1-2)

#### 1.1 Database Schema Implementation
```typescript
// File: src/lib/database/schema/integrations.ts
// Implement Prisma schema for integration framework
// Include multi-tenant isolation and proper indexing
// Add migration scripts for schema deployment
```

#### 1.2 Integration Runtime Service
```typescript
// File: src/lib/integrations/runtime/executor.ts
// Implement workflow execution engine
// Support for different node types and connectors
// Error handling and retry mechanisms
// Execution context management and security
```

#### 1.3 Connector Registry System
```typescript
// File: src/lib/integrations/connectors/registry.ts
// Implement connector registration and discovery
// Support for official and custom connectors
// Authentication handling for different connector types
// Configuration schema validation
```

### Phase 2: Visual Workflow Builder (Week 3-4)

#### 2.1 React Flow Integration
```typescript
// File: src/components/integrations/workflow-builder/WorkflowCanvas.tsx
// Implement drag-and-drop workflow builder
// Custom node types for different integration steps
// Connection validation and flow logic
// Real-time collaboration support
```

#### 2.2 Node Configuration System
```typescript
// File: src/components/integrations/workflow-builder/NodeConfigPanel.tsx
// Dynamic configuration forms based on connector schemas
// Field mapping interface for data transformation
// Validation and error handling
// Preview and testing capabilities
```

#### 2.3 Workflow Testing Framework
```typescript
// File: src/lib/integrations/testing/workflow-tester.ts
// Dry-run execution for workflow validation
// Mock data generation for testing
// Step-by-step debugging interface
// Performance profiling and optimization
```

### Phase 3: Template System (Week 5-6)

#### 3.1 Template Management
```typescript
// File: src/lib/integrations/templates/manager.ts
// Template creation, versioning, and publishing
// Template marketplace with search and filtering
// Community ratings and reviews
// Template import/export functionality
```

#### 3.2 Code Template Engine
```typescript
// File: src/lib/integrations/templates/code-generator.ts
// TypeScript/JavaScript template generation
// Custom hook generation for React components
// API client generation for integrations
// Documentation generation from templates
```

#### 3.3 Template Marketplace UI
```typescript
// File: src/components/integrations/marketplace/TemplateMarketplace.tsx
// Browse and search integration templates
// Template preview and documentation
// One-click template installation
// Community features and sharing
```

### Phase 4: Monitoring & Analytics (Week 7-8)

#### 4.1 Execution Monitoring
```typescript
// File: src/lib/integrations/monitoring/execution-monitor.ts
// Real-time execution tracking and logging
// Performance metrics collection
// Error tracking and alerting
// Resource usage monitoring
```

#### 4.2 Analytics Dashboard
```typescript
// File: src/components/integrations/analytics/IntegrationDashboard.tsx
// Integration performance metrics
// Usage analytics and trends
// Error rate monitoring and alerts
// Cost tracking and optimization
```

#### 4.3 Audit Logging System
```typescript
// File: src/lib/integrations/audit/audit-logger.ts
// Comprehensive audit trail for all integration activities
// Security event logging and monitoring
// Compliance reporting and data retention
// Integration with existing audit system
```

---

## Integration Points

### 1. Authentication & Authorization
- **Better-Auth Integration**: Extend existing auth system for integration permissions
- **Tenant Context**: Ensure all integration operations respect tenant boundaries
- **Role-Based Access**: Define integration-specific roles and permissions
- **API Key Management**: Secure storage and rotation of integration credentials

### 2. Webhook System Integration
- **Existing Webhook Infrastructure**: Leverage current webhook system for triggers
- **Event Routing**: Route integration events through existing event system
- **Webhook Security**: Implement signature validation and rate limiting
- **Event Transformation**: Convert webhook payloads to integration inputs

### 3. Third-Party Service Integration
- **OAuth Flow Integration**: Reuse existing OAuth implementation
- **Credential Management**: Secure storage of third-party service credentials
- **Rate Limiting**: Implement per-service rate limiting and quotas
- **Service Health Monitoring**: Monitor third-party service availability

### 4. SDK Generation Integration
- **Auto-Generated SDKs**: Include integration framework in SDK generation
- **Type Safety**: Generate TypeScript types for custom integrations
- **Documentation**: Auto-generate integration documentation
- **Code Examples**: Provide SDK usage examples for integrations

### 5. Database & Caching
- **Supabase Integration**: Use existing database connection and pooling
- **Multi-Tenant Isolation**: Ensure complete data separation between tenants
- **Caching Strategy**: Implement Redis caching for frequently accessed data
- **Data Retention**: Implement configurable data retention policies

### 6. Monitoring & Alerting
- **Existing Monitoring**: Integrate with current monitoring infrastructure
- **Custom Metrics**: Define integration-specific metrics and KPIs
- **Alert Configuration**: Allow users to configure custom alerts
- **Performance Tracking**: Monitor integration performance and optimization

---

## API Endpoints

### Integration Templates API

```typescript
// GET /api/integrations/templates
// List integration templates with filtering and pagination
interface TemplateListResponse {
  templates: IntegrationTemplate[];
  pagination: PaginationInfo;
  filters: FilterOptions;
}

// POST /api/integrations/templates
// Create new integration template
interface CreateTemplateRequest {
  name: string;
  description?: string;
  category: string;
  type: IntegrationType;
  workflow?: WorkflowDefinition;
  code?: string;
  config: Record<string, any>;
}

// GET /api/integrations/templates/:id
// Get specific integration template
interface TemplateDetailResponse {
  template: IntegrationTemplate;
  usage: TemplateUsageStats;
  reviews: TemplateReview[];
}

// PUT /api/integrations/templates/:id
// Update integration template
interface UpdateTemplateRequest {
  name?: string;
  description?: string;
  workflow?: WorkflowDefinition;
  code?: string;
  config?: Record<string, any>;
}
```

### Integration Instances API

```typescript
// GET /api/integrations/instances
// List integration instances for current tenant
interface InstanceListResponse {
  instances: IntegrationInstance[];
  pagination: PaginationInfo;
  stats: InstanceStats;
}

// POST /api/integrations/instances
// Create new integration instance from template
interface CreateInstanceRequest {
  templateId: string;
  name: string;
  description?: string;
  config: IntegrationConfig;
  environment: string;
}

// GET /api/integrations/instances/:id
// Get specific integration instance
interface InstanceDetailResponse {
  instance: IntegrationInstance;
  executions: IntegrationExecution[];
  metrics: InstanceMetrics;
}

// POST /api/integrations/instances/:id/execute
// Manually trigger integration execution
interface ExecuteInstanceRequest {
  input?: Record<string, any>;
  dryRun?: boolean;
}
```

### Workflow Builder API

```typescript
// POST /api/integrations/workflows/validate
// Validate workflow definition
interface ValidateWorkflowRequest {
  workflow: WorkflowDefinition;
}

interface ValidateWorkflowResponse {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

// POST /api/integrations/workflows/test
// Test workflow execution with mock data
interface TestWorkflowRequest {
  workflow: WorkflowDefinition;
  input: Record<string, any>;
  mockResponses?: Record<string, any>;
}

interface TestWorkflowResponse {
  success: boolean;
  output: Record<string, any>;
  steps: ExecutionStep[];
  duration: number;
}
```

### Connectors API

```typescript
// GET /api/integrations/connectors
// List available connectors
interface ConnectorListResponse {
  connectors: IntegrationConnector[];
  categories: string[];
}

// GET /api/integrations/connectors/:id/schema
// Get connector configuration schema
interface ConnectorSchemaResponse {
  schema: JSONSchema;
  examples: Record<string, any>[];
  documentation: string;
}

// POST /api/integrations/connectors/:id/test
// Test connector configuration
interface TestConnectorRequest {
  config: Record<string, any>;
  operation: string;
}

interface TestConnectorResponse {
  success: boolean;
  result: any;
  error?: string;
}
```

---

## Frontend Components

### Workflow Builder Components

```typescript
// Main workflow builder interface
export function WorkflowBuilder({ 
  templateId, 
  onSave, 
  onTest 
}: WorkflowBuilderProps) {
  // React Flow canvas with custom nodes
  // Sidebar with available connectors
  // Configuration panel for selected nodes
  // Toolbar with save, test, and deploy actions
}

// Custom node types for different integration steps
export function TriggerNode({ data, selected }: NodeProps) {
  // Webhook, schedule, or manual trigger configuration
  // Visual indicators for trigger type and status
  // Configuration form for trigger settings
}

export function ActionNode({ data, selected }: NodeProps) {
  // Third-party service action configuration
  // Field mapping interface for data transformation
  // Error handling and retry configuration
}

export function ConditionNode({ data, selected }: NodeProps) {
  // Conditional logic builder with visual editor
  // Support for complex boolean expressions
  // Preview of condition evaluation
}

// Node configuration panel
export function NodeConfigPanel({ 
  node, 
  onUpdate, 
  connectors 
}: NodeConfigPanelProps) {
  // Dynamic form generation based on connector schema
  // Field validation and error display
  // Real-time preview of configuration changes
}
```

### Template Marketplace Components

```typescript
// Template marketplace browser
export function TemplateMarketplace({ 
  onInstall, 
  filters 
}: TemplateMarketplaceProps) {
  // Grid view of available templates
  // Search and filtering capabilities
  // Template preview and documentation
  // Installation and rating interface
}

// Template detail view
export function TemplateDetail({ 
  templateId, 
  onInstall 
}: TemplateDetailProps) {
  // Comprehensive template information
  // Visual workflow preview
  // Code examples and documentation
  // User reviews and ratings
}

// Template creation wizard
export function TemplateCreator({ 
  onSave, 
  initialData 
}: TemplateCreatorProps) {
  // Step-by-step template creation process
  // Workflow builder integration
  // Metadata and documentation editor
  // Publishing and sharing options
}
```

### Integration Management Components

```typescript
// Integration dashboard
export function IntegrationDashboard({ 
  tenantId 
}: IntegrationDashboardProps) {
  // Overview of all integrations
  // Performance metrics and health status
  // Quick actions for common tasks
  // Recent activity and notifications
}

// Integration instance manager
export function IntegrationManager({ 
  instanceId 
}: IntegrationManagerProps) {
  // Instance configuration and settings
  // Execution history and logs
  // Performance monitoring and alerts
  // Deployment and environment management
}

// Execution monitoring interface
export function ExecutionMonitor({ 
  executionId 
}: ExecutionMonitorProps) {
  // Real-time execution progress
  // Step-by-step execution details
  // Error tracking and debugging
  // Performance metrics and optimization
}
```

---

## Security Implementation

### 1. Multi-Tenant Isolation

```typescript
// Tenant context middleware for integration operations
export async function integrationTenantMiddleware(
  req: NextRequest,
  context: { params: { tenantId: string } }
) {
  const session = await getSession(req);
  const tenantId = context.params.tenantId;
  
  // Verify user has access to tenant
  const hasAccess = await verifyTenantAccess(session.userId, tenantId);
  if (!hasAccess) {
    return new Response('Forbidden', { status: 403 });
  }
  
  // Inject tenant context into request
  req.headers.set('x-tenant-id', tenantId);
  req.headers.set('x-user-id', session.userId);
  
  return NextResponse.next();
}

// Database query helper with automatic tenant filtering
export function withTenantFilter<T>(
  query: Prisma.Delegate<T>,
  tenantId: string
) {
  return {
    ...query,
    where: {
      ...query.where,
      tenantId,
    },
  };
}
```

### 2. Integration Execution Security

```typescript
// Secure execution environment for custom integrations
export class SecureExecutionEnvironment {
  private tenantId: string;
  private userId: string;
  private resourceLimits: ResourceLimits;
  
  constructor(context: ExecutionContext) {
    this.tenantId = context.tenantId;
    this.userId = context.userId;
    this.resourceLimits = this.getResourceLimits(context);
  }
  
  async executeWorkflow(
    workflow: WorkflowDefinition,
    input: Record<string, any>
  ): Promise<ExecutionResult> {
    // Create isolated execution context
    const sandbox = await this.createSandbox();
    
    try {
      // Execute workflow with resource limits
      const result = await this.runWithLimits(
        () => this.executeNodes(workflow.nodes, input, sandbox),
        this.resourceLimits
      );
      
      return {
        success: true,
        output: result,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      // Log security events and errors
      await this.logSecurityEvent('execution_error', {
        tenantId: this.tenantId,
        userId: this.userId,
        error: error.message,
      });
      
      throw error;
    } finally {
      // Clean up sandbox environment
      await this.destroySandbox(sandbox);
    }
  }
  
  private async createSandbox(): Promise<ExecutionSandbox> {
    // Create isolated execution environment
    // Limit file system access, network access, and system resources
    // Inject only necessary APIs and utilities
  }
}
```

### 3. Credential Management

```typescript
// Secure credential storage and access
export class IntegrationCredentialManager {
  private encryptionKey: string;
  
  async storeCredentials(
    tenantId: string,
    integrationId: string,
    credentials: Record<string, string>
  ): Promise<void> {
    // Encrypt credentials before storage
    const encrypted = await this.encrypt(credentials);
    
    await prisma.integrationCredentials.upsert({
      where: {
        tenantId_integrationId: {
          tenantId,
          integrationId,
        },
      },
      create: {
        tenantId,
        integrationId,
        credentials: encrypted,
        createdAt: new Date(),
      },
      update: {
        credentials: encrypted,
        updatedAt: new Date(),
      },
    });
    
    // Log credential access for audit
    await this.auditLog('credentials_stored', {
      tenantId,
      integrationId,
      userId: getCurrentUserId(),
    });
  }
  
  async getCredentials(
    tenantId: string,
    integrationId: string
  ): Promise<Record<string, string>> {
    const stored = await prisma.integrationCredentials.findUnique({
      where: {
        tenantId_integrationId: {
          tenantId,
          integrationId,
        },
      },
    });
    
    if (!stored) {
      throw new Error('Credentials not found');
    }
    
    // Decrypt credentials for use
    const decrypted = await this.decrypt(stored.credentials);
    
    // Log credential access for audit
    await this.auditLog('credentials_accessed', {
      tenantId,
      integrationId,
      userId: getCurrentUserId(),
    });
    
    return decrypted;
  }
}
```

---

## Performance Optimization

### 1. Execution Engine Optimization

```typescript
// Optimized workflow execution with parallel processing
export class OptimizedWorkflowExecutor {
  private executionPool: WorkerPool;
  private cache: ExecutionCache;
  
  async executeWorkflow(
    workflow: WorkflowDefinition,
    input: Record<string, any>
  ): Promise<ExecutionResult> {
    // Analyze workflow for parallel execution opportunities
    const executionPlan = this.analyzeWorkflow(workflow);
    
    // Execute nodes in parallel where possible
    const results = await this.executeInParallel(
      executionPlan.parallelGroups,
      input
    );
    
    // Combine results and continue with dependent nodes
    return this.combineResults(results);
  }
  
  private analyzeWorkflow(
    workflow: WorkflowDefinition
  ): ExecutionPlan {
    // Build dependency graph
    const dependencyGraph = this.buildDependencyGraph(workflow);
    
    // Identify parallel execution opportunities
    const parallelGroups = this.identifyParallelGroups(dependencyGraph);
    
    return {
      parallelGroups,
      criticalPath: this.findCriticalPath(dependencyGraph),
      estimatedDuration: this.estimateExecutionTime(workflow),
    };
  }
}

// Caching layer for frequently executed workflows
export class ExecutionCache {
  private redis: Redis;
  
  async getCachedResult(
    workflowId: string,
    inputHash: string
  ): Promise<ExecutionResult | null> {
    const cacheKey = `workflow:${workflowId}:${inputHash}`;
    const cached = await this.redis.get(cacheKey);
    
    if (cached) {
      return JSON.parse(cached);
    }
    
    return null;
  }
  
  async setCachedResult(
    workflowId: string,
    inputHash: string,
    result: ExecutionResult,
    ttl: number = 3600
  ): Promise<void> {
    const cacheKey = `workflow:${workflowId}:${inputHash}`;
    await this.redis.setex(cacheKey, ttl, JSON.stringify(result));
  }
}
```

### 2. Database Query Optimization

```typescript
// Optimized database queries with proper indexing
export class IntegrationQueryOptimizer {
  // Efficient template search with full-text search
  async searchTemplates(
    tenantId: string,
    query: string,
    filters: TemplateFilters,
    pagination: PaginationOptions
  ): Promise<TemplateSearchResult> {
    return await prisma.integrationTemplate.findMany({
      where: {
        AND: [
          { tenantId },
          {
            OR: [
              { name: { contains: query, mode: 'insensitive' } },
              { description: { contains: query, mode: 'insensitive' } },
            ],
          },
          filters.category ? { category: filters.category } : {},
          filters.type ? { type: filters.type } : {},
          filters.isPublic !== undefined ? { isPublic: filters.isPublic } : {},
        ],
      },
      include: {
        _count: {
          select: { instances: true },
        },
      },
      orderBy: [
        { rating: 'desc' },
        { downloads: 'desc' },
        { updatedAt: 'desc' },
      ],
      skip: pagination.offset,
      take: pagination.limit,
    });
  }
  
  // Efficient execution history queries with aggregation
  async getExecutionMetrics(
    instanceId: string,
    timeRange: TimeRange
  ): Promise<ExecutionMetrics> {
    const metrics = await prisma.integrationExecution.aggregate({
      where: {
        instanceId,
        startedAt: {
          gte: timeRange.start,
          lte: timeRange.end,
        },
      },
      _count: {
        id: true,
      },
      _avg: {
        duration: true,
      },
      _sum: {
        stepsTotal: true,
        stepsSuccess: true,
        stepsFailed: true,
      },
    });
    
    const statusCounts = await prisma.integrationExecution.groupBy({
      by: ['status'],
      where: {
        instanceId,
        startedAt: {
          gte: timeRange.start,
          lte: timeRange.end,
        },
      },
      _count: {
        id: true,
      },
    });
    
    return {
      totalExecutions: metrics._count.id,
      averageDuration: metrics._avg.duration,
      successRate: this.calculateSuccessRate(statusCounts),
      totalSteps: metrics._sum.stepsTotal,
      failedSteps: metrics._sum.stepsFailed,
      statusBreakdown: statusCounts,
    };
  }
}
```

---

## Testing Strategy

### 1. Unit Tests

```typescript
// Test workflow execution engine
describe('WorkflowExecutor', () => {
  let executor: WorkflowExecutor;
  let mockConnectors: MockConnectorRegistry;
  
  beforeEach(() => {
    mockConnectors = new MockConnectorRegistry();
    executor = new WorkflowExecutor(mockConnectors);
  });
  
  it('should execute simple workflow successfully', async () => {
    const workflow: WorkflowDefinition = {
      id: 'test-workflow',
      name: 'Test Workflow',
      version: '1.0.0',
      nodes: [
        {
          id: 'trigger',
          type: 'trigger',
          position: { x: 0, y: 0 },
          data: {
            operation: 'webhook',
            config: { path: '/webhook' },
          },
        },
        {
          id: 'action',
          type: 'action',
          position: { x: 200, y: 0 },
          data: {
            connector: 'http',
            operation: 'post',
            config: { url: 'https://api.example.com/data' },
          },
        },
      ],
      edges: [
        {
          id: 'trigger-action',
          source: 'trigger',
          target: 'action',
        },
      ],
      variables: [],
      triggers: [],
    };
    
    const input = { data: 'test' };
    const result = await executor.execute(workflow, input);
    
    expect(result.success).toBe(true);
    expect(result.output).toBeDefined();
  });
  
  it('should handle node execution errors gracefully', async () => {
    // Test error handling and retry logic
    mockConnectors.setError('http', 'post', new Error('Network error'));
    
    const workflow = createTestWorkflow();
    const result = await executor.execute(workflow, {});
    
    expect(result.success).toBe(false);
    expect(result.error).toContain('Network error');
  });
});

// Test template validation
describe('TemplateValidator', () => {
  let validator: TemplateValidator;
  
  beforeEach(() => {
    validator = new TemplateValidator();
  });
  
  it('should validate workflow definition schema', () => {
    const workflow: WorkflowDefinition = createValidWorkflow();
    const result = validator.validateWorkflow(workflow);
    
    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });
  
  it('should detect circular dependencies', () => {
    const workflow = createCircularWorkflow();
    const result = validator.validateWorkflow(workflow);
    
    expect(result.valid).toBe(false);
    expect(result.errors).toContainEqual(
      expect.objectContaining({
        type: 'circular_dependency',
      })
    );
  });
});
```

### 2. Integration Tests

```typescript
// Test complete integration workflow
describe('Integration Framework E2E', () => {
  let testTenant: Tenant;
  let testUser: User;
  let apiClient: TestApiClient;
  
  beforeAll(async () => {
    testTenant = await createTestTenant();
    testUser = await createTestUser(testTenant.id);
    apiClient = new TestApiClient(testUser.token);
  });
  
  afterAll(async () => {
    await cleanupTestData();
  });
  
  it('should create and execute integration end-to-end', async () => {
    // Create integration template
    const template = await apiClient.post('/api/integrations/templates', {
      name: 'Test Integration',
      category: 'automation',
      type: 'VISUAL_WORKFLOW',
      workflow: createTestWorkflow(),
    });
    
    expect(template.id).toBeDefined();
    
    // Create integration instance
    const instance = await apiClient.post('/api/integrations/instances', {
      templateId: template.id,
      name: 'Test Instance',
      config: createTestConfig(),
    });
    
    expect(instance.id).toBeDefined();
    
    // Execute integration
    const execution = await apiClient.post(
      `/api/integrations/instances/${instance.id}/execute`,
      { input: { test: 'data' } }
    );
    
    expect(execution.status).toBe('SUCCESS');
    expect(execution.output).toBeDefined();
  });
  
  it('should enforce tenant isolation', async () => {
    const otherTenant = await createTestTenant();
    const otherUser = await createTestUser(otherTenant.id);
    const otherApiClient = new TestApiClient(otherUser.token);
    
    // Create template in first tenant
    const template = await apiClient.post('/api/integrations/templates', {
      name: 'Private Template',
      category: 'automation',
      type: 'VISUAL_WORKFLOW',
      workflow: createTestWorkflow(),
    });
    
    // Try to access from other tenant
    const response = await otherApiClient.get(
      `/api/integrations/templates/${template.id}`
    );
    
    expect(response.status).toBe(404);
  });
});
```

### 3. Performance Tests

```typescript
// Test workflow execution performance
describe('Performance Tests', () => {
  it('should execute simple workflow under 500ms', async () => {
    const workflow = createSimpleWorkflow();
    const executor = new WorkflowExecutor();
    
    const startTime = Date.now();
    const result = await executor.execute(workflow, {});
    const duration = Date.now() - startTime;
    
    expect(result.success).toBe(true);
    expect(duration).toBeLessThan(500);
  });
  
  it('should handle concurrent executions', async () => {
    const workflow = createTestWorkflow();
    const executor = new WorkflowExecutor();
    
    const promises = Array.from({ length: 10 }, () =>
      executor.execute(workflow, { id: Math.random() })
    );
    
    const results = await Promise.all(promises);
    
    expect(results).toHaveLength(10);
    expect(results.every(r => r.success)).toBe(true);
  });
  
  it('should scale with workflow complexity', async () => {
    const complexWorkflow = createComplexWorkflow(50); // 50 nodes
    const executor = new WorkflowExecutor();
    
    const startTime = Date.now();
    const result = await executor.execute(complexWorkflow, {});
    const duration = Date.now() - startTime;
    
    expect(result.success).toBe(true);
    expect(duration).toBeLessThan(5000); // 5 seconds max
  });
});
```

---

## Validation Gates

### Level 1: Syntax & Style
```bash
# ESLint checks for integration framework code
npm run lint

# TypeScript type checking
npx tsc --noEmit

# Prettier code formatting
npm run format:check

# Valibot schema validation
npm run validate:schemas
```

### Level 2: Unit Tests
```bash
# Jest unit tests for all integration components
npm test -- --testPathPattern=integrations

# Test coverage reporting
npm run test:coverage -- --testPathPattern=integrations

# Workflow validation tests
npm run test:workflows

# Template validation tests
npm run test:templates
```

### Level 3: Integration Tests
```bash
# Start development server
npm run dev

# Test integration API endpoints
curl -X POST http://localhost:3000/api/integrations/templates \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TEST_TOKEN" \
  -d '{"name":"Test Template","category":"automation","type":"VISUAL_WORKFLOW"}'

# Test workflow execution
curl -X POST http://localhost:3000/api/integrations/workflows/test \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TEST_TOKEN" \
  -d '{"workflow":{"id":"test","name":"Test","version":"1.0.0","nodes":[],"edges":[],"variables":[],"triggers":[]},"input":{}}'

# Test template marketplace
curl -X GET http://localhost:3000/api/integrations/templates \
  -H "Authorization: Bearer $TEST_TOKEN"
```

### Level 4: End-to-End Tests
```bash
# Production build validation
npm run build

# Start production server
npm run start

# Playwright E2E tests for integration framework
npm run test:e2e -- --grep="integration framework"

# Performance testing with k6
k6 run tests/performance/integration-execution.js

# Security testing
npm run test:security -- --focus=integrations
```

### Level 5: Custom Integration Framework Validation
```bash
# Workflow execution performance test
node scripts/test-workflow-performance.js

# Template validation test
node scripts/validate-all-templates.js

# Connector registry test
node scripts/test-connector-registry.js

# Multi-tenant isolation test
node scripts/test-tenant-isolation.js

# Security sandbox test
node scripts/test-execution-sandbox.js

# Database migration test
npm run db:migrate:test

# Integration monitoring test
node scripts/test-integration-monitoring.js
```

---

## Documentation Requirements

### 1. Developer Documentation
- **Integration Framework Overview**: Architecture and core concepts
- **Workflow Builder Guide**: Step-by-step workflow creation tutorial
- **Connector Development**: How to create custom connectors
- **Template Creation**: Guide for building reusable integration templates
- **API Reference**: Complete API documentation with examples
- **Security Best Practices**: Security guidelines for custom integrations

### 2. User Documentation
- **Getting Started Guide**: Quick start tutorial for non-technical users
- **Workflow Builder Tutorial**: Visual guide to building integrations
- **Template Marketplace**: How to find and use integration templates
- **Troubleshooting Guide**: Common issues and solutions
- **Performance Optimization**: Tips for optimizing integration performance

### 3. Administrative Documentation
- **Deployment Guide**: How to deploy and configure the integration framework
- **Monitoring Setup**: Setting up monitoring and alerting
- **Security Configuration**: Security settings and compliance requirements
- **Backup and Recovery**: Data backup and disaster recovery procedures
- **Scaling Guide**: How to scale the integration framework for enterprise use

---

## Deployment Strategy

### 1. Database Migrations
```sql
-- Create integration framework tables
-- Run migrations in staging environment first
-- Validate data integrity and performance
-- Deploy to production with rollback plan
```

### 2. Feature Flags
```typescript
// Gradual rollout with feature flags
const integrationFrameworkEnabled = await getFeatureFlag(
  'integration-framework',
  tenantId
);

if (integrationFrameworkEnabled) {
  // Show integration framework UI
  return <IntegrationFramework />;
} else {
  // Show coming soon message
  return <ComingSoon feature="Custom Integration Framework" />;
}
```

### 3. Monitoring and Alerting
```typescript
// Set up monitoring for integration framework
const integrationMetrics = {
  executionCount: new Counter('integration_executions_total'),
  executionDuration: new Histogram('integration_execution_duration_seconds'),
  errorRate: new Counter('integration_errors_total'),
  activeInstances: new Gauge('integration_active_instances'),
};

// Alert on high error rates or performance issues
if (errorRate > 0.05) {
  await sendAlert('High integration error rate detected');
}
```

---

## Success Metrics

### Technical Metrics
- **Execution Performance**: <500ms average execution time for simple workflows
- **System Reliability**: 99.9% uptime for integration execution service
- **Error Rate**: <1% execution error rate under normal conditions
- **Scalability**: Support 1000+ concurrent workflow executions
- **Security**: Zero critical security vulnerabilities

### Business Metrics
- **User Adoption**: 80% of enterprise customers create custom integrations
- **Template Usage**: 50+ community-contributed integration templates
- **Customer Satisfaction**: 4.5+ star rating for integration framework
- **Support Reduction**: 30% reduction in integration-related support tickets
- **Revenue Impact**: 15% increase in enterprise plan conversions

### Performance Benchmarks
- **Workflow Creation**: <2 minutes to create simple integration
- **Template Installation**: <30 seconds to install and configure template
- **Execution Monitoring**: Real-time execution status updates
- **Error Resolution**: <5 minutes average time to identify and fix errors
- **Documentation Coverage**: 100% API coverage with examples

---

## Future Enhancements

### Phase 2 Features (Next Quarter)
- **AI-Powered Integration Builder**: Natural language to workflow conversion
- **Advanced Analytics**: Predictive analytics for integration performance
- **Mobile App Support**: Mobile workflow builder and monitoring
- **Enterprise SSO**: Integration with enterprise identity providers
- **Advanced Scheduling**: Complex scheduling with dependencies

### Phase 3 Features (Future)
- **Machine Learning Integration**: ML-powered data transformation
- **Real-time Collaboration**: Multi-user workflow editing
- **Version Control**: Git-like versioning for integration workflows
- **Marketplace Monetization**: Paid integration templates and connectors
- **Enterprise Governance**: Advanced approval workflows and compliance

---

**Implementation Priority**: High  
**Estimated Effort**: 8 weeks  
**Dependencies**: Webhook System, Third-Party Integrations, SDK Generation  
**Risk Level**: Medium  
**Business Impact**: High  

---

*This PRP provides comprehensive documentation for implementing the Custom Integration Framework, completing the Integration Framework Sprint and enabling customers to build powerful custom integrations within the NEXUS SaaS platform.*