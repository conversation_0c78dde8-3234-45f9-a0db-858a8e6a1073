# Workspace Management Implementation

## Overview
This PRP implements a comprehensive workspace management system that enables tenants to create, configure, and manage multiple workspaces with hierarchical organization, role-based access control, and advanced customization features. The system supports unlimited workspace nesting, flexible permission models, and enterprise-grade collaboration tools.

## Core Requirements

### 1. Workspace Architecture
- **Hierarchical Structure**: Support unlimited workspace nesting (workspace -> sub-workspace -> projects)
- **Multi-Tenant Integration**: Full integration with tenant context system
- **Entity Framework**: Use Ent framework for robust entity management
- **Performance Optimization**: Efficient queries for workspace hierarchies
- **Data Consistency**: Maintain referential integrity across workspace relationships

### 2. Workspace Management Features
- **Workspace Creation**: Easy workspace setup with templates and presets
- **Workspace Configuration**: Comprehensive settings and customization options
- **Workspace Branding**: Custom themes, logos, and visual identity
- **Workspace Templates**: Pre-configured workspace setups for common use cases
- **Workspace Archiving**: Soft delete with recovery capabilities

### 3. Access Control and Permissions
- **Role-Based Access Control**: Granular permissions per workspace
- **Permission Inheritance**: Hierarchical permission propagation
- **Custom Roles**: Tenant-specific role definitions
- **Guest Access**: External user access with limited permissions
- **Workspace Visibility**: Public, private, and restricted visibility levels

### 4. Collaboration Features
- **Team Management**: User assignment and role management
- **Workspace Sharing**: Share workspaces with internal and external users
- **Activity Tracking**: Comprehensive audit logs and activity feeds
- **Real-time Updates**: Live updates for workspace changes
- **Notification System**: Configurable notifications for workspace events

## Implementation

### 1. Workspace Entity Schema

```go
// ent/schema/workspace.go
package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"entgo.io/ent/schema/mixin"
	"time"
)

// Workspace holds the schema definition for the Workspace entity.
type Workspace struct {
	ent.Schema
}

// Mixin includes common fields and behaviors.
func (Workspace) Mixin() []ent.Mixin {
	return []ent.Mixin{
		TenantMixin{},
		TimestampMixin{},
		SoftDeleteMixin{},
	}
}

// Fields of the Workspace.
func (Workspace) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").
			NotEmpty().
			Comment("Workspace name"),
		field.Text("description").
			Optional().
			Comment("Workspace description"),
		field.String("slug").
			NotEmpty().
			Comment("URL-friendly identifier"),
		field.Enum("visibility").
			Values("public", "private", "restricted").
			Default("private").
			Comment("Workspace visibility level"),
		field.Enum("status").
			Values("active", "archived", "suspended").
			Default("active").
			Comment("Workspace status"),
		field.JSON("settings", &WorkspaceSettings{}).
			Optional().
			Comment("Workspace configuration settings"),
		field.JSON("branding", &WorkspaceBranding{}).
			Optional().
			Comment("Workspace branding configuration"),
		field.String("color").
			Optional().
			Comment("Workspace theme color"),
		field.String("icon").
			Optional().
			Comment("Workspace icon identifier"),
		field.Int("parent_id").
			Optional().
			Comment("Parent workspace ID for hierarchical structure"),
		field.String("path").
			Optional().
			Comment("Hierarchical path for efficient queries"),
		field.Int("level").
			Default(0).
			Comment("Workspace nesting level"),
		field.Int("sort_order").
			Default(0).
			Comment("Display order within parent"),
		field.Bool("is_template").
			Default(false).
			Comment("Whether this workspace is a template"),
		field.JSON("template_config", &TemplateConfig{}).
			Optional().
			Comment("Template configuration if is_template=true"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional().
			Comment("Additional metadata"),
	}
}

// Edges of the Workspace.
func (Workspace) Edges() []ent.Edge {
	return []ent.Edge{
		// Hierarchical relationship
		edge.To("children", Workspace.Type).
			From("parent").
			Field("parent_id").
			Unique(),
		
		// Workspace members
		edge.To("members", WorkspaceMember.Type).
			StorageKey(edge.Column("workspace_id")),
		
		// Workspace projects
		edge.To("projects", Project.Type).
			StorageKey(edge.Column("workspace_id")),
		
		// Workspace invitations
		edge.To("invitations", WorkspaceInvitation.Type).
			StorageKey(edge.Column("workspace_id")),
		
		// Workspace activities
		edge.To("activities", WorkspaceActivity.Type).
			StorageKey(edge.Column("workspace_id")),
		
		// Workspace files
		edge.To("files", WorkspaceFile.Type).
			StorageKey(edge.Column("workspace_id")),
		
		// Creator
		edge.From("creator", User.Type).
			Ref("created_workspaces").
			Unique(),
		
		// Tenant relationship
		edge.From("tenant", Tenant.Type).
			Ref("workspaces").
			Field("tenant_id").
			Unique().
			Required(),
	}
}

// Indexes of the Workspace.
func (Workspace) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("tenant_id", "slug").
			Unique(),
		index.Fields("tenant_id", "parent_id"),
		index.Fields("tenant_id", "status"),
		index.Fields("path"),
		index.Fields("level"),
		index.Fields("is_template"),
		index.Fields("created_at"),
		index.Fields("updated_at"),
	}
}

// WorkspaceSettings defines the workspace configuration structure.
type WorkspaceSettings struct {
	Features struct {
		Projects    bool `json:"projects"`
		Files       bool `json:"files"`
		Wiki        bool `json:"wiki"`
		Calendar    bool `json:"calendar"`
		TimeTracking bool `json:"time_tracking"`
		Integrations bool `json:"integrations"`
	} `json:"features"`
	Permissions struct {
		AllowGuestAccess     bool `json:"allow_guest_access"`
		RequireApproval      bool `json:"require_approval"`
		AllowInvitations     bool `json:"allow_invitations"`
		AllowPublicSharing   bool `json:"allow_public_sharing"`
		DefaultMemberRole    string `json:"default_member_role"`
	} `json:"permissions"`
	Notifications struct {
		EmailNotifications    bool `json:"email_notifications"`
		SlackIntegration     bool `json:"slack_integration"`
		WebhookNotifications bool `json:"webhook_notifications"`
	} `json:"notifications"`
	Limits struct {
		MaxMembers        int `json:"max_members"`
		MaxProjects       int `json:"max_projects"`
		MaxStorageGB      int `json:"max_storage_gb"`
		MaxSubWorkspaces  int `json:"max_sub_workspaces"`
	} `json:"limits"`
}

// WorkspaceBranding defines the workspace branding configuration.
type WorkspaceBranding struct {
	Logo struct {
		URL    string `json:"url"`
		Width  int    `json:"width"`
		Height int    `json:"height"`
	} `json:"logo"`
	Colors struct {
		Primary     string `json:"primary"`
		Secondary   string `json:"secondary"`
		Accent      string `json:"accent"`
		Background  string `json:"background"`
		Text        string `json:"text"`
	} `json:"colors"`
	Theme struct {
		Name       string `json:"name"`
		Mode       string `json:"mode"` // light, dark, auto
		CustomCSS  string `json:"custom_css"`
	} `json:"theme"`
	Favicon struct {
		URL  string `json:"url"`
		Type string `json:"type"`
	} `json:"favicon"`
}

// TemplateConfig defines template-specific configuration.
type TemplateConfig struct {
	Category    string                 `json:"category"`
	Tags        []string              `json:"tags"`
	PreviewURL  string                `json:"preview_url"`
	Variables   map[string]interface{} `json:"variables"`
	Instructions string               `json:"instructions"`
}
```

### 2. Workspace Member Entity

```go
// ent/schema/workspace_member.go
package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"time"
)

// WorkspaceMember holds the schema definition for workspace membership.
type WorkspaceMember struct {
	ent.Schema
}

// Mixin includes common fields and behaviors.
func (WorkspaceMember) Mixin() []ent.Mixin {
	return []ent.Mixin{
		TenantMixin{},
		TimestampMixin{},
	}
}

// Fields of the WorkspaceMember.
func (WorkspaceMember) Fields() []ent.Field {
	return []ent.Field{
		field.String("user_id").
			NotEmpty().
			Comment("User ID"),
		field.String("workspace_id").
			NotEmpty().
			Comment("Workspace ID"),
		field.Enum("role").
			Values("owner", "admin", "member", "viewer", "guest").
			Default("member").
			Comment("Member role"),
		field.Enum("status").
			Values("active", "inactive", "pending", "suspended").
			Default("active").
			Comment("Member status"),
		field.JSON("permissions", []string{}).
			Optional().
			Comment("Additional permissions"),
		field.Time("joined_at").
			Default(time.Now).
			Comment("When the user joined the workspace"),
		field.Time("last_active_at").
			Optional().
			Comment("Last activity timestamp"),
		field.String("invited_by").
			Optional().
			Comment("User ID who invited this member"),
		field.Text("invite_message").
			Optional().
			Comment("Invitation message"),
		field.JSON("metadata", map[string]interface{}{}).
			Optional().
			Comment("Additional metadata"),
	}
}

// Edges of the WorkspaceMember.
func (WorkspaceMember) Edges() []ent.Edge {
	return []ent.Edge{
		// User
		edge.From("user", User.Type).
			Ref("workspace_memberships").
			Field("user_id").
			Unique().
			Required(),
		
		// Workspace
		edge.From("workspace", Workspace.Type).
			Ref("members").
			Field("workspace_id").
			Unique().
			Required(),
		
		// Inviter
		edge.From("inviter", User.Type).
			Ref("workspace_invitations_sent").
			Field("invited_by").
			Unique(),
		
		// Tenant
		edge.From("tenant", Tenant.Type).
			Ref("workspace_members").
			Field("tenant_id").
			Unique().
			Required(),
	}
}

// Indexes of the WorkspaceMember.
func (WorkspaceMember) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("tenant_id", "workspace_id", "user_id").
			Unique(),
		index.Fields("tenant_id", "user_id"),
		index.Fields("workspace_id", "role"),
		index.Fields("workspace_id", "status"),
		index.Fields("last_active_at"),
		index.Fields("joined_at"),
	}
}
```

### 3. Workspace Service Implementation

```typescript
// services/workspace-service.ts
import { supabase } from '@/lib/supabase';
import { cache } from '@/lib/cache';
import { auditService } from '@/services/audit-service';
import { permissionService } from '@/services/permission-service';
import { useTenant } from '@/contexts/tenant-context';
import type { 
  Workspace, 
  WorkspaceMember, 
  WorkspaceSettings, 
  WorkspaceBranding,
  CreateWorkspaceRequest,
  UpdateWorkspaceRequest,
  WorkspaceInvitation
} from '@/types/workspace';

interface WorkspaceQuery {
  tenantId: string;
  parentId?: string;
  status?: 'active' | 'archived' | 'suspended';
  visibility?: 'public' | 'private' | 'restricted';
  search?: string;
  page?: number;
  limit?: number;
}

class WorkspaceService {
  private readonly CACHE_TTL = 300; // 5 minutes
  private readonly CACHE_PREFIX = 'workspace:';

  async getWorkspaces(query: WorkspaceQuery): Promise<{
    workspaces: Workspace[];
    total: number;
    hasMore: boolean;
  }> {
    const {
      tenantId,
      parentId,
      status = 'active',
      visibility,
      search,
      page = 1,
      limit = 20
    } = query;

    const cacheKey = `${this.CACHE_PREFIX}list:${JSON.stringify(query)}`;
    const cached = await cache.get<any>(cacheKey);
    
    if (cached) return cached;

    let queryBuilder = supabase
      .from('workspaces')
      .select(`
        *,
        creator:users!creator_id(id, name, email, avatar_url),
        parent:workspaces!parent_id(id, name, slug),
        members:workspace_members(
          id,
          user_id,
          role,
          status,
          user:users(id, name, email, avatar_url)
        ),
        _count:workspace_members(count)
      `)
      .eq('tenant_id', tenantId)
      .eq('status', status)
      .eq('deleted_at', null);

    if (parentId) {
      queryBuilder = queryBuilder.eq('parent_id', parentId);
    } else {
      queryBuilder = queryBuilder.is('parent_id', null);
    }

    if (visibility) {
      queryBuilder = queryBuilder.eq('visibility', visibility);
    }

    if (search) {
      queryBuilder = queryBuilder.or(
        `name.ilike.%${search}%,description.ilike.%${search}%`
      );
    }

    const offset = (page - 1) * limit;
    const { data, error, count } = await queryBuilder
      .order('sort_order', { ascending: true })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    const workspaces = data?.map(this.mapWorkspaceData) || [];
    const total = count || 0;
    const hasMore = offset + workspaces.length < total;

    const result = { workspaces, total, hasMore };
    await cache.set(cacheKey, result, this.CACHE_TTL);

    return result;
  }

  async getWorkspace(id: string, tenantId: string): Promise<Workspace | null> {
    const cacheKey = `${this.CACHE_PREFIX}${id}`;
    const cached = await cache.get<Workspace>(cacheKey);
    
    if (cached) return cached;

    const { data, error } = await supabase
      .from('workspaces')
      .select(`
        *,
        creator:users!creator_id(id, name, email, avatar_url),
        parent:workspaces!parent_id(id, name, slug),
        children:workspaces!parent_id(id, name, slug, status),
        members:workspace_members(
          id,
          user_id,
          role,
          status,
          joined_at,
          last_active_at,
          user:users(id, name, email, avatar_url)
        ),
        projects:projects(id, name, slug, status),
        _count:workspace_members(count)
      `)
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .eq('deleted_at', null)
      .single();

    if (error || !data) return null;

    const workspace = this.mapWorkspaceData(data);
    await cache.set(cacheKey, workspace, this.CACHE_TTL);

    return workspace;
  }

  async createWorkspace(
    request: CreateWorkspaceRequest,
    tenantId: string,
    userId: string
  ): Promise<Workspace> {
    const slug = await this.generateUniqueSlug(request.name, tenantId);
    
    // Calculate hierarchical path and level
    let path = slug;
    let level = 0;
    
    if (request.parentId) {
      const parent = await this.getWorkspace(request.parentId, tenantId);
      if (!parent) {
        throw new Error('Parent workspace not found');
      }
      
      // Check permissions
      const canCreate = await permissionService.checkWorkspacePermission(
        userId,
        request.parentId,
        'workspace.create'
      );
      
      if (!canCreate) {
        throw new Error('Insufficient permissions to create workspace in parent');
      }
      
      path = `${parent.path}/${slug}`;
      level = parent.level + 1;
    }

    const workspaceData = {
      name: request.name,
      description: request.description,
      slug,
      visibility: request.visibility || 'private',
      parent_id: request.parentId,
      path,
      level,
      settings: request.settings || this.getDefaultSettings(),
      branding: request.branding || this.getDefaultBranding(),
      color: request.color,
      icon: request.icon,
      tenant_id: tenantId,
      creator_id: userId,
      metadata: request.metadata || {},
    };

    const { data, error } = await supabase
      .from('workspaces')
      .insert(workspaceData)
      .select()
      .single();

    if (error) throw error;

    // Create workspace owner membership
    await this.addMember(data.id, userId, 'owner', tenantId);

    // Invalidate cache
    await this.invalidateWorkspaceCache(tenantId);

    // Audit log
    await auditService.log({
      action: 'workspace.created',
      tenantId,
      userId,
      resourceId: data.id,
      metadata: {
        workspaceName: data.name,
        parentId: request.parentId,
      },
    });

    return this.mapWorkspaceData(data);
  }

  async updateWorkspace(
    id: string,
    request: UpdateWorkspaceRequest,
    tenantId: string,
    userId: string
  ): Promise<Workspace> {
    // Check permissions
    const canUpdate = await permissionService.checkWorkspacePermission(
      userId,
      id,
      'workspace.update'
    );
    
    if (!canUpdate) {
      throw new Error('Insufficient permissions to update workspace');
    }

    const updates: any = {
      updated_at: new Date().toISOString(),
    };

    if (request.name !== undefined) {
      updates.name = request.name;
      
      // Update slug if name changed
      if (request.name) {
        updates.slug = await this.generateUniqueSlug(request.name, tenantId, id);
      }
    }

    if (request.description !== undefined) {
      updates.description = request.description;
    }

    if (request.visibility !== undefined) {
      updates.visibility = request.visibility;
    }

    if (request.settings !== undefined) {
      updates.settings = request.settings;
    }

    if (request.branding !== undefined) {
      updates.branding = request.branding;
    }

    if (request.color !== undefined) {
      updates.color = request.color;
    }

    if (request.icon !== undefined) {
      updates.icon = request.icon;
    }

    if (request.metadata !== undefined) {
      updates.metadata = request.metadata;
    }

    const { data, error } = await supabase
      .from('workspaces')
      .update(updates)
      .eq('id', id)
      .eq('tenant_id', tenantId)
      .select()
      .single();

    if (error) throw error;

    // Invalidate cache
    await this.invalidateWorkspaceCache(tenantId, id);

    // Audit log
    await auditService.log({
      action: 'workspace.updated',
      tenantId,
      userId,
      resourceId: id,
      metadata: { updates },
    });

    return this.mapWorkspaceData(data);
  }

  async deleteWorkspace(
    id: string,
    tenantId: string,
    userId: string
  ): Promise<void> {
    // Check permissions
    const canDelete = await permissionService.checkWorkspacePermission(
      userId,
      id,
      'workspace.delete'
    );
    
    if (!canDelete) {
      throw new Error('Insufficient permissions to delete workspace');
    }

    // Check for children workspaces
    const { data: children } = await supabase
      .from('workspaces')
      .select('id, name')
      .eq('parent_id', id)
      .eq('tenant_id', tenantId)
      .eq('deleted_at', null);

    if (children && children.length > 0) {
      throw new Error('Cannot delete workspace with active sub-workspaces');
    }

    // Soft delete
    const { error } = await supabase
      .from('workspaces')
      .update({
        deleted_at: new Date().toISOString(),
        status: 'archived',
      })
      .eq('id', id)
      .eq('tenant_id', tenantId);

    if (error) throw error;

    // Invalidate cache
    await this.invalidateWorkspaceCache(tenantId, id);

    // Audit log
    await auditService.log({
      action: 'workspace.deleted',
      tenantId,
      userId,
      resourceId: id,
    });
  }

  async addMember(
    workspaceId: string,
    userId: string,
    role: string,
    tenantId: string,
    invitedBy?: string
  ): Promise<WorkspaceMember> {
    const memberData = {
      workspace_id: workspaceId,
      user_id: userId,
      role,
      status: 'active',
      tenant_id: tenantId,
      invited_by: invitedBy,
      joined_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from('workspace_members')
      .insert(memberData)
      .select(`
        *,
        user:users(id, name, email, avatar_url),
        workspace:workspaces(id, name, slug)
      `)
      .single();

    if (error) throw error;

    // Invalidate cache
    await this.invalidateWorkspaceCache(tenantId, workspaceId);

    return this.mapMemberData(data);
  }

  async removeMember(
    workspaceId: string,
    userId: string,
    tenantId: string,
    removedBy: string
  ): Promise<void> {
    const { error } = await supabase
      .from('workspace_members')
      .delete()
      .eq('workspace_id', workspaceId)
      .eq('user_id', userId)
      .eq('tenant_id', tenantId);

    if (error) throw error;

    // Invalidate cache
    await this.invalidateWorkspaceCache(tenantId, workspaceId);

    // Audit log
    await auditService.log({
      action: 'workspace.member.removed',
      tenantId,
      userId: removedBy,
      resourceId: workspaceId,
      metadata: { removedUserId: userId },
    });
  }

  async updateMemberRole(
    workspaceId: string,
    userId: string,
    role: string,
    tenantId: string,
    updatedBy: string
  ): Promise<WorkspaceMember> {
    const { data, error } = await supabase
      .from('workspace_members')
      .update({ role })
      .eq('workspace_id', workspaceId)
      .eq('user_id', userId)
      .eq('tenant_id', tenantId)
      .select(`
        *,
        user:users(id, name, email, avatar_url),
        workspace:workspaces(id, name, slug)
      `)
      .single();

    if (error) throw error;

    // Invalidate cache
    await this.invalidateWorkspaceCache(tenantId, workspaceId);

    // Audit log
    await auditService.log({
      action: 'workspace.member.role_updated',
      tenantId,
      userId: updatedBy,
      resourceId: workspaceId,
      metadata: { targetUserId: userId, newRole: role },
    });

    return this.mapMemberData(data);
  }

  async getWorkspaceMembers(
    workspaceId: string,
    tenantId: string
  ): Promise<WorkspaceMember[]> {
    const cacheKey = `${this.CACHE_PREFIX}members:${workspaceId}`;
    const cached = await cache.get<WorkspaceMember[]>(cacheKey);
    
    if (cached) return cached;

    const { data, error } = await supabase
      .from('workspace_members')
      .select(`
        *,
        user:users(id, name, email, avatar_url),
        workspace:workspaces(id, name, slug)
      `)
      .eq('workspace_id', workspaceId)
      .eq('tenant_id', tenantId)
      .order('joined_at', { ascending: true });

    if (error) throw error;

    const members = data?.map(this.mapMemberData) || [];
    await cache.set(cacheKey, members, this.CACHE_TTL);

    return members;
  }

  async getWorkspacePath(
    workspaceId: string,
    tenantId: string
  ): Promise<Workspace[]> {
    const workspace = await this.getWorkspace(workspaceId, tenantId);
    if (!workspace) return [];

    const pathSegments = workspace.path.split('/');
    const pathWorkspaces: Workspace[] = [];

    for (let i = 0; i < pathSegments.length; i++) {
      const partialPath = pathSegments.slice(0, i + 1).join('/');
      
      const { data, error } = await supabase
        .from('workspaces')
        .select('id, name, slug, path, level')
        .eq('path', partialPath)
        .eq('tenant_id', tenantId)
        .single();

      if (!error && data) {
        pathWorkspaces.push(this.mapWorkspaceData(data));
      }
    }

    return pathWorkspaces;
  }

  private async generateUniqueSlug(
    name: string,
    tenantId: string,
    excludeId?: string
  ): Promise<string> {
    const baseSlug = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');

    let slug = baseSlug;
    let counter = 1;

    while (true) {
      let query = supabase
        .from('workspaces')
        .select('id')
        .eq('slug', slug)
        .eq('tenant_id', tenantId)
        .eq('deleted_at', null);

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query;

      if (error) throw error;

      if (!data || data.length === 0) {
        break;
      }

      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  private getDefaultSettings(): WorkspaceSettings {
    return {
      features: {
        projects: true,
        files: true,
        wiki: true,
        calendar: true,
        time_tracking: true,
        integrations: true,
      },
      permissions: {
        allow_guest_access: false,
        require_approval: true,
        allow_invitations: true,
        allow_public_sharing: false,
        default_member_role: 'member',
      },
      notifications: {
        email_notifications: true,
        slack_integration: false,
        webhook_notifications: false,
      },
      limits: {
        max_members: 50,
        max_projects: 10,
        max_storage_gb: 5,
        max_sub_workspaces: 5,
      },
    };
  }

  private getDefaultBranding(): WorkspaceBranding {
    return {
      logo: {
        url: '',
        width: 0,
        height: 0,
      },
      colors: {
        primary: '#3b82f6',
        secondary: '#6b7280',
        accent: '#10b981',
        background: '#ffffff',
        text: '#1f2937',
      },
      theme: {
        name: 'default',
        mode: 'light',
        custom_css: '',
      },
      favicon: {
        url: '',
        type: 'image/x-icon',
      },
    };
  }

  private mapWorkspaceData(data: any): Workspace {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      slug: data.slug,
      visibility: data.visibility,
      status: data.status,
      settings: data.settings,
      branding: data.branding,
      color: data.color,
      icon: data.icon,
      parentId: data.parent_id,
      path: data.path,
      level: data.level,
      sortOrder: data.sort_order,
      isTemplate: data.is_template,
      templateConfig: data.template_config,
      metadata: data.metadata,
      tenantId: data.tenant_id,
      creatorId: data.creator_id,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      deletedAt: data.deleted_at ? new Date(data.deleted_at) : null,
      // Relations
      creator: data.creator,
      parent: data.parent,
      children: data.children || [],
      members: data.members?.map(this.mapMemberData) || [],
      projects: data.projects || [],
      memberCount: data._count || 0,
    };
  }

  private mapMemberData(data: any): WorkspaceMember {
    return {
      id: data.id,
      userId: data.user_id,
      workspaceId: data.workspace_id,
      role: data.role,
      status: data.status,
      permissions: data.permissions || [],
      joinedAt: new Date(data.joined_at),
      lastActiveAt: data.last_active_at ? new Date(data.last_active_at) : null,
      invitedBy: data.invited_by,
      inviteMessage: data.invite_message,
      metadata: data.metadata || {},
      tenantId: data.tenant_id,
      // Relations
      user: data.user,
      workspace: data.workspace,
    };
  }

  private async invalidateWorkspaceCache(tenantId: string, workspaceId?: string): Promise<void> {
    const patterns = [
      `${this.CACHE_PREFIX}list:*${tenantId}*`,
      `${this.CACHE_PREFIX}members:*`,
    ];

    if (workspaceId) {
      patterns.push(`${this.CACHE_PREFIX}${workspaceId}`);
      patterns.push(`${this.CACHE_PREFIX}members:${workspaceId}`);
    }

    for (const pattern of patterns) {
      await cache.deletePattern(pattern);
    }
  }
}

export const workspaceService = new WorkspaceService();
```

### 4. Workspace Context Provider

```typescript
// contexts/workspace-context.tsx
'use client';

import { createContext, useContext, useReducer, useEffect, ReactNode, useCallback } from 'react';
import { useTenant } from '@/contexts/tenant-context';
import { workspaceService } from '@/services/workspace-service';
import { auditService } from '@/services/audit-service';
import type { Workspace, WorkspaceMember, CreateWorkspaceRequest, UpdateWorkspaceRequest } from '@/types/workspace';

interface WorkspaceState {
  workspaces: Workspace[];
  currentWorkspace: Workspace | null;
  members: WorkspaceMember[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
}

type WorkspaceAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_WORKSPACES'; payload: { workspaces: Workspace[]; hasMore: boolean; page: number } }
  | { type: 'ADD_WORKSPACES'; payload: { workspaces: Workspace[]; hasMore: boolean; page: number } }
  | { type: 'SET_CURRENT_WORKSPACE'; payload: Workspace | null }
  | { type: 'SET_MEMBERS'; payload: WorkspaceMember[] }
  | { type: 'ADD_WORKSPACE'; payload: Workspace }
  | { type: 'UPDATE_WORKSPACE'; payload: Workspace }
  | { type: 'REMOVE_WORKSPACE'; payload: string }
  | { type: 'ADD_MEMBER'; payload: WorkspaceMember }
  | { type: 'UPDATE_MEMBER'; payload: WorkspaceMember }
  | { type: 'REMOVE_MEMBER'; payload: string }
  | { type: 'RESET' };

interface WorkspaceContext {
  // State
  workspaces: Workspace[];
  currentWorkspace: Workspace | null;
  members: WorkspaceMember[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;

  // Actions
  loadWorkspaces: (parentId?: string, refresh?: boolean) => Promise<void>;
  loadMoreWorkspaces: () => Promise<void>;
  setCurrentWorkspace: (workspace: Workspace | null) => void;
  createWorkspace: (request: CreateWorkspaceRequest) => Promise<Workspace>;
  updateWorkspace: (id: string, request: UpdateWorkspaceRequest) => Promise<Workspace>;
  deleteWorkspace: (id: string) => Promise<void>;
  loadMembers: (workspaceId: string) => Promise<void>;
  addMember: (workspaceId: string, userId: string, role: string) => Promise<WorkspaceMember>;
  updateMemberRole: (workspaceId: string, userId: string, role: string) => Promise<WorkspaceMember>;
  removeMember: (workspaceId: string, userId: string) => Promise<void>;
  refreshCurrentWorkspace: () => Promise<void>;
}

const WorkspaceContext = createContext<WorkspaceContext | null>(null);

const initialState: WorkspaceState = {
  workspaces: [],
  currentWorkspace: null,
  members: [],
  isLoading: false,
  error: null,
  hasMore: false,
  page: 1,
};

function workspaceReducer(state: WorkspaceState, action: WorkspaceAction): WorkspaceState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'SET_WORKSPACES':
      return {
        ...state,
        workspaces: action.payload.workspaces,
        hasMore: action.payload.hasMore,
        page: action.payload.page,
      };
    
    case 'ADD_WORKSPACES':
      return {
        ...state,
        workspaces: [...state.workspaces, ...action.payload.workspaces],
        hasMore: action.payload.hasMore,
        page: action.payload.page,
      };
    
    case 'SET_CURRENT_WORKSPACE':
      return { ...state, currentWorkspace: action.payload };
    
    case 'SET_MEMBERS':
      return { ...state, members: action.payload };
    
    case 'ADD_WORKSPACE':
      return {
        ...state,
        workspaces: [action.payload, ...state.workspaces],
      };
    
    case 'UPDATE_WORKSPACE':
      return {
        ...state,
        workspaces: state.workspaces.map(workspace =>
          workspace.id === action.payload.id ? action.payload : workspace
        ),
        currentWorkspace: state.currentWorkspace?.id === action.payload.id
          ? action.payload
          : state.currentWorkspace,
      };
    
    case 'REMOVE_WORKSPACE':
      return {
        ...state,
        workspaces: state.workspaces.filter(workspace => workspace.id !== action.payload),
        currentWorkspace: state.currentWorkspace?.id === action.payload
          ? null
          : state.currentWorkspace,
      };
    
    case 'ADD_MEMBER':
      return {
        ...state,
        members: [...state.members, action.payload],
      };
    
    case 'UPDATE_MEMBER':
      return {
        ...state,
        members: state.members.map(member =>
          member.id === action.payload.id ? action.payload : member
        ),
      };
    
    case 'REMOVE_MEMBER':
      return {
        ...state,
        members: state.members.filter(member => member.id !== action.payload),
      };
    
    case 'RESET':
      return initialState;
    
    default:
      return state;
  }
}

interface WorkspaceProviderProps {
  children: ReactNode;
}

export function WorkspaceProvider({ children }: WorkspaceProviderProps) {
  const [state, dispatch] = useReducer(workspaceReducer, initialState);
  const { tenant, user } = useTenant();

  // Load workspaces
  const loadWorkspaces = useCallback(async (parentId?: string, refresh = false) => {
    if (!tenant) return;

    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const result = await workspaceService.getWorkspaces({
        tenantId: tenant.id,
        parentId,
        page: refresh ? 1 : state.page,
        limit: 20,
      });

      if (refresh) {
        dispatch({
          type: 'SET_WORKSPACES',
          payload: {
            workspaces: result.workspaces,
            hasMore: result.hasMore,
            page: 1,
          },
        });
      } else {
        dispatch({
          type: 'ADD_WORKSPACES',
          payload: {
            workspaces: result.workspaces,
            hasMore: result.hasMore,
            page: state.page,
          },
        });
      }
    } catch (error) {
      console.error('Failed to load workspaces:', error);
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [tenant, state.page]);

  // Load more workspaces
  const loadMoreWorkspaces = useCallback(async () => {
    if (!state.hasMore || state.isLoading) return;

    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const result = await workspaceService.getWorkspaces({
        tenantId: tenant!.id,
        page: state.page + 1,
        limit: 20,
      });

      dispatch({
        type: 'ADD_WORKSPACES',
        payload: {
          workspaces: result.workspaces,
          hasMore: result.hasMore,
          page: state.page + 1,
        },
      });
    } catch (error) {
      console.error('Failed to load more workspaces:', error);
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [tenant, state.page, state.hasMore, state.isLoading]);

  // Set current workspace
  const setCurrentWorkspace = useCallback((workspace: Workspace | null) => {
    dispatch({ type: 'SET_CURRENT_WORKSPACE', payload: workspace });
  }, []);

  // Create workspace
  const createWorkspace = useCallback(async (request: CreateWorkspaceRequest): Promise<Workspace> => {
    if (!tenant || !user) throw new Error('Not authenticated');

    const workspace = await workspaceService.createWorkspace(
      request,
      tenant.id,
      user.userId
    );

    dispatch({ type: 'ADD_WORKSPACE', payload: workspace });

    return workspace;
  }, [tenant, user]);

  // Update workspace
  const updateWorkspace = useCallback(async (id: string, request: UpdateWorkspaceRequest): Promise<Workspace> => {
    if (!tenant || !user) throw new Error('Not authenticated');

    const workspace = await workspaceService.updateWorkspace(
      id,
      request,
      tenant.id,
      user.userId
    );

    dispatch({ type: 'UPDATE_WORKSPACE', payload: workspace });

    return workspace;
  }, [tenant, user]);

  // Delete workspace
  const deleteWorkspace = useCallback(async (id: string): Promise<void> => {
    if (!tenant || !user) throw new Error('Not authenticated');

    await workspaceService.deleteWorkspace(id, tenant.id, user.userId);

    dispatch({ type: 'REMOVE_WORKSPACE', payload: id });
  }, [tenant, user]);

  // Load members
  const loadMembers = useCallback(async (workspaceId: string) => {
    if (!tenant) return;

    try {
      const members = await workspaceService.getWorkspaceMembers(workspaceId, tenant.id);
      dispatch({ type: 'SET_MEMBERS', payload: members });
    } catch (error) {
      console.error('Failed to load members:', error);
      dispatch({ type: 'SET_ERROR', payload: (error as Error).message });
    }
  }, [tenant]);

  // Add member
  const addMember = useCallback(async (workspaceId: string, userId: string, role: string): Promise<WorkspaceMember> => {
    if (!tenant) throw new Error('Not authenticated');

    const member = await workspaceService.addMember(workspaceId, userId, role, tenant.id);

    dispatch({ type: 'ADD_MEMBER', payload: member });

    return member;
  }, [tenant]);

  // Update member role
  const updateMemberRole = useCallback(async (workspaceId: string, userId: string, role: string): Promise<WorkspaceMember> => {
    if (!tenant || !user) throw new Error('Not authenticated');

    const member = await workspaceService.updateMemberRole(
      workspaceId,
      userId,
      role,
      tenant.id,
      user.userId
    );

    dispatch({ type: 'UPDATE_MEMBER', payload: member });

    return member;
  }, [tenant, user]);

  // Remove member
  const removeMember = useCallback(async (workspaceId: string, userId: string): Promise<void> => {
    if (!tenant || !user) throw new Error('Not authenticated');

    await workspaceService.removeMember(workspaceId, userId, tenant.id, user.userId);

    dispatch({ type: 'REMOVE_MEMBER', payload: userId });
  }, [tenant, user]);

  // Refresh current workspace
  const refreshCurrentWorkspace = useCallback(async () => {
    if (!state.currentWorkspace || !tenant) return;

    try {
      const workspace = await workspaceService.getWorkspace(state.currentWorkspace.id, tenant.id);
      if (workspace) {
        dispatch({ type: 'SET_CURRENT_WORKSPACE', payload: workspace });
      }
    } catch (error) {
      console.error('Failed to refresh workspace:', error);
    }
  }, [state.currentWorkspace, tenant]);

  // Load workspaces on tenant change
  useEffect(() => {
    if (tenant) {
      loadWorkspaces(undefined, true);
    } else {
      dispatch({ type: 'RESET' });
    }
  }, [tenant, loadWorkspaces]);

  const contextValue: WorkspaceContext = {
    // State
    workspaces: state.workspaces,
    currentWorkspace: state.currentWorkspace,
    members: state.members,
    isLoading: state.isLoading,
    error: state.error,
    hasMore: state.hasMore,
    page: state.page,

    // Actions
    loadWorkspaces,
    loadMoreWorkspaces,
    setCurrentWorkspace,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    loadMembers,
    addMember,
    updateMemberRole,
    removeMember,
    refreshCurrentWorkspace,
  };

  return (
    <WorkspaceContext.Provider value={contextValue}>
      {children}
    </WorkspaceContext.Provider>
  );
}

export function useWorkspace(): WorkspaceContext {
  const context = useContext(WorkspaceContext);
  if (!context) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
}

// Convenience hooks
export function useCurrentWorkspace(): Workspace | null {
  const { currentWorkspace } = useWorkspace();
  return currentWorkspace;
}

export function useWorkspaceMembers(): WorkspaceMember[] {
  const { members } = useWorkspace();
  return members;
}
```

### 5. Workspace Components

```typescript
// components/workspace/workspace-list.tsx
'use client';

import { useEffect, useState } from 'react';
import { useWorkspace } from '@/contexts/workspace-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Plus, 
  Search, 
  Settings, 
  Users, 
  ChevronRight, 
  Globe, 
  Lock, 
  Archive,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { WorkspaceCreateDialog } from './workspace-create-dialog';
import { WorkspaceCard } from './workspace-card';
import type { Workspace } from '@/types/workspace';

interface WorkspaceListProps {
  parentId?: string;
  showCreateButton?: boolean;
  onWorkspaceSelect?: (workspace: Workspace) => void;
}

export function WorkspaceList({
  parentId,
  showCreateButton = true,
  onWorkspaceSelect,
}: WorkspaceListProps) {
  const {
    workspaces,
    isLoading,
    error,
    hasMore,
    loadWorkspaces,
    loadMoreWorkspaces,
    setCurrentWorkspace,
  } = useWorkspace();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('active');
  const [visibilityFilter, setVisibilityFilter] = useState<string>('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  useEffect(() => {
    loadWorkspaces(parentId, true);
  }, [parentId, loadWorkspaces]);

  const filteredWorkspaces = workspaces.filter(workspace => {
    const matchesSearch = workspace.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workspace.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || workspace.status === statusFilter;
    const matchesVisibility = visibilityFilter === 'all' || workspace.visibility === visibilityFilter;
    
    return matchesSearch && matchesStatus && matchesVisibility;
  });

  const handleWorkspaceClick = (workspace: Workspace) => {
    setCurrentWorkspace(workspace);
    onWorkspaceSelect?.(workspace);
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public':
        return <Globe className="h-4 w-4" />;
      case 'private':
        return <Lock className="h-4 w-4" />;
      case 'restricted':
        return <Users className="h-4 w-4" />;
      default:
        return <Lock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'default';
      case 'archived':
        return 'secondary';
      case 'suspended':
        return 'destructive';
      default:
        return 'default';
    }
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Workspaces</h2>
          <p className="text-muted-foreground">
            Manage your workspaces and collaborate with your team
          </p>
        </div>
        {showCreateButton && (
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Workspace
          </Button>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search workspaces..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="archived">Archived</SelectItem>
            <SelectItem value="suspended">Suspended</SelectItem>
          </SelectContent>
        </Select>
        <Select value={visibilityFilter} onValueChange={setVisibilityFilter}>
          <SelectTrigger className="w-full sm:w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Visibility</SelectItem>
            <SelectItem value="public">Public</SelectItem>
            <SelectItem value="private">Private</SelectItem>
            <SelectItem value="restricted">Restricted</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Workspace Grid */}
      {isLoading && workspaces.length === 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-10 w-10 rounded" />
                    <div>
                      <Skeleton className="h-4 w-24 mb-1" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                  <Skeleton className="h-8 w-8 rounded" />
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3 mb-4" />
                <div className="flex items-center justify-between">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredWorkspaces.map((workspace) => (
              <WorkspaceCard
                key={workspace.id}
                workspace={workspace}
                onClick={() => handleWorkspaceClick(workspace)}
              />
            ))}
          </div>

          {filteredWorkspaces.length === 0 && !isLoading && (
            <div className="text-center py-12">
              <div className="mx-auto h-12 w-12 text-muted-foreground mb-4">
                <Archive className="h-full w-full" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No workspaces found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== 'all' || visibilityFilter !== 'all'
                  ? 'Try adjusting your filters'
                  : 'Create your first workspace to get started'}
              </p>
              {showCreateButton && (
                <Button onClick={() => setShowCreateDialog(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Workspace
                </Button>
              )}
            </div>
          )}

          {hasMore && (
            <div className="text-center">
              <Button
                variant="outline"
                onClick={loadMoreWorkspaces}
                disabled={isLoading}
              >
                {isLoading ? 'Loading...' : 'Load More'}
              </Button>
            </div>
          )}
        </>
      )}

      {/* Create Dialog */}
      <WorkspaceCreateDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        parentId={parentId}
      />
    </div>
  );
}
```

### 6. Validation Gates

- [ ] Workspace CRUD operations work correctly with tenant isolation
- [ ] Hierarchical workspace structure supports unlimited nesting
- [ ] Workspace permissions are properly enforced
- [ ] Workspace member management functions correctly
- [ ] Workspace branding and settings are applied properly
- [ ] Workspace search and filtering work as expected
- [ ] Workspace templates can be created and used
- [ ] Workspace archiving and recovery work properly
- [ ] Performance is optimized for large workspace hierarchies
- [ ] Integration with tenant context system is seamless

## Success Metrics

- **Workspace Creation Time**: < 500ms for new workspace creation
- **Workspace Load Time**: < 200ms for workspace list loading
- **Hierarchy Query Performance**: < 100ms for workspace hierarchy queries
- **Member Management**: < 300ms for member operations
- **Search Performance**: < 150ms for workspace search
- **Cache Hit Rate**: > 80% for workspace data
- **User Experience**: > 95% satisfaction with workspace management
- **Error Rate**: < 0.05% for workspace operations
- **Concurrent Users**: Support 1000+ concurrent workspace users
- **Data Consistency**: 100% consistency in workspace hierarchies

This comprehensive Workspace Management implementation provides a solid foundation for organizing and managing workspaces in a multi-tenant environment with enterprise-grade features and performance.
