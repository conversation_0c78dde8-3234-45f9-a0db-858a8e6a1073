# Better-Auth Integration Implementation PRP

**PRP Name**: Better-Auth Integration Implementation  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Foundation Implementation PRP  
**Framework**: Next.js 15.4+ / React 19 / TypeScript 5.8+ / better-auth / Prisma  
**Priority**: Critical Path - Authentication Foundation  

---

## Purpose

Implement a robust authentication system using better-auth with multi-tenant workspace support, session management, and integration with the existing Prisma multi-tenant database architecture.

## Context and Research

### Current Technology Stack
- **Authentication**: better-auth (modern, TypeScript-first)
- **Database**: PostgreSQL with Prisma ORM (multi-tenant ready)
- **Framework**: Next.js 15.4+ with App Router
- **TypeScript**: 5.8+ for type safety
- **UI**: Radix UI components (already installed)

### Better-Auth Framework Research

**Better-Auth Documentation**
- URL: https://better-auth.com/docs
- URL: https://better-auth.com/docs/getting-started
- Modern authentication framework built for TypeScript
- Built-in support for Next.js App Router
- Comprehensive plugin ecosystem

**Key Features**
- URL: https://better-auth.com/docs/concepts/sessions
- Type-safe session management
- Multiple authentication providers
- Built-in CSRF protection
- Rate limiting and security features

**Multi-Tenant Integration**
- URL: https://better-auth.com/docs/plugins/organization
- Organization plugin for multi-tenant support
- Role-based access control
- Tenant-scoped sessions and permissions

### Authentication Security Research

**OWASP Authentication Guidelines**
- URL: https://owasp.org/www-project-authentication/
- Session management best practices
- Password security requirements
- Multi-factor authentication standards

**JWT Security Best Practices**
- URL: https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/
- Token security and validation
- Refresh token patterns
- Secure storage practices

**Session Security**
- URL: https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies
- HttpOnly and Secure cookie flags
- SameSite cookie protection
- CSRF protection strategies

### Multi-Tenant Authentication Patterns

**Tenant-Scoped Authentication**
- URL: https://blog.frontegg.com/multi-tenant-authentication
- Tenant isolation in authentication
- Workspace-based user management
- Cross-tenant security considerations

**Enterprise SSO Integration**
- URL: https://auth0.com/docs/authenticate/single-sign-on
- SAML and OAuth 2.0 integration
- Identity provider federation
- Just-in-time provisioning

## Implementation Blueprint

### Better-Auth Configuration

```typescript
// lib/auth/auth.config.ts
import { betterAuth } from "better-auth"
import { prismaAdapter } from "better-auth/adapters/prisma"
import { organization } from "better-auth/plugins"
import { prisma } from "@/lib/database/prisma"

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    minPasswordLength: 8,
    maxPasswordLength: 128,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 1 week
    updateAge: 60 * 60 * 24, // 1 day
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5, // 5 minutes
    },
  },
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    },
    github: {
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    },
  },
  plugins: [
    organization({
      allowUserToCreateOrganization: true,
      organizationLimit: 5,
      sendInvitationEmail: async (data) => {
        // Integration with email service
        await sendInvitationEmail(data)
      },
    }),
  ],
  trustedOrigins: [
    "http://localhost:3000",
    "https://yourdomain.com",
  ],
  advanced: {
    crossSubDomainCookies: {
      enabled: true,
      domain: process.env.COOKIE_DOMAIN || "localhost",
    },
    generateId: () => {
      // Use cuid2 for better performance
      return crypto.randomUUID()
    },
  },
})

export type Session = typeof auth.$Infer.Session
export type User = typeof auth.$Infer.User
```

### API Route Integration

```typescript
// app/api/auth/[...all]/route.ts
import { auth } from "@/lib/auth/auth.config"
import { NextRequest } from "next/server"

export async function GET(request: NextRequest) {
  return auth.handler(request)
}

export async function POST(request: NextRequest) {
  return auth.handler(request)
}
```

### Authentication Middleware

```typescript
// lib/middleware/auth-middleware.ts
import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth/auth.config'

export async function withAuthMiddleware(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers,
    })

    if (!session) {
      // Redirect to login for protected routes
      if (request.nextUrl.pathname.startsWith('/dashboard')) {
        return NextResponse.redirect(new URL('/auth/signin', request.url))
      }
      return NextResponse.next()
    }

    // Add session context to request
    const response = NextResponse.next()
    response.headers.set('x-user-id', session.user.id)
    response.headers.set('x-session-id', session.id)
    
    return response
  } catch (error) {
    console.error('Auth middleware error:', error)
    return NextResponse.json(
      { error: 'Authentication error' },
      { status: 401 }
    )
  }
}
```

### Database Schema Updates

```prisma
// prisma/schema.prisma additions
model User {
  id        String   @id @default(cuid())
  tenantId  String   // Multi-tenant isolation
  email     String   
  name      String?
  avatar    String?
  role      UserRole @default(MEMBER)
  status    UserStatus @default(ACTIVE)
  
  // Better-Auth required fields
  emailVerified Boolean @default(false)
  image         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relationships
  tenant       Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  sessions     Session[]
  accounts     Account[]
  organizations OrganizationMember[]
  
  @@unique([tenantId, email])
  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

model Account {
  id           String  @id @default(cuid())
  userId       String
  provider     String
  providerAccountId String
  type         String
  access_token String?
  expires_at   Int?
  id_token     String?
  refresh_token String?
  scope        String?
  token_type   String?
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Organization {
  id          String   @id @default(cuid())
  tenantId    String   // Multi-tenant isolation
  name        String
  slug        String
  logo        String?
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  tenant      Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  members     OrganizationMember[]
  
  @@unique([tenantId, slug])
  @@map("organizations")
}

model OrganizationMember {
  id             String   @id @default(cuid())
  userId         String
  organizationId String
  role           OrganizationRole @default(MEMBER)
  invitedBy      String?
  invitedAt      DateTime?
  acceptedAt     DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  user         User @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@unique([userId, organizationId])
  @@map("organization_members")
}

enum OrganizationRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER
}
```

### Client-Side Authentication Hook

```typescript
// hooks/use-auth.ts
import { createAuthClient } from "better-auth/react"
import { organizationClient } from "better-auth/client/plugins"

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
  plugins: [organizationClient()],
})

export const {
  useSession,
  signIn,
  signOut,
  signUp,
  useUser,
  useOrganization,
  useListOrganizations,
  useActiveOrganization,
  useInvitations,
} = authClient
```

### Authentication Components

```typescript
// components/auth/signin-form.tsx
"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { signIn } from "@/hooks/use-auth"

export function SignInForm() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    try {
      const result = await signIn.email({
        email,
        password,
      })

      if (result.error) {
        setError(result.error.message)
        return
      }

      router.push("/dashboard")
    } catch (error) {
      setError("An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setError("")
    setIsLoading(true)

    try {
      await signIn.social({
        provider: "google",
        callbackURL: "/dashboard",
      })
    } catch (error) {
      setError("Failed to sign in with Google")
      setIsLoading(false)
    }
  }

  const handleGithubSignIn = async () => {
    setError("")
    setIsLoading(true)

    try {
      await signIn.social({
        provider: "github",
        callbackURL: "/dashboard",
      })
    } catch (error) {
      setError("Failed to sign in with GitHub")
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Sign In</CardTitle>
        <CardDescription>
          Enter your credentials to access your account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              required
              disabled={isLoading}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
              disabled={isLoading}
            />
          </div>
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Signing in..." : "Sign In"}
          </Button>
        </form>
        <div className="mt-4">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or continue with
              </span>
            </div>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              onClick={handleGoogleSignIn}
              disabled={isLoading}
            >
              Google
            </Button>
            <Button
              variant="outline"
              onClick={handleGithubSignIn}
              disabled={isLoading}
            >
              GitHub
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
```

### Authentication Pages

```typescript
// app/auth/signin/page.tsx
import { SignInForm } from "@/components/auth/signin-form"

export default function SignInPage() {
  return (
    <div className="container relative h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white dark:border-r lg:flex">
        <div className="absolute inset-0 bg-zinc-900" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2 h-6 w-6"
          >
            <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
          </svg>
          NEXUS SaaS Starter
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              "This platform has transformed how we manage our business operations. 
              The multi-tenant architecture is exactly what we needed for our enterprise clients."
            </p>
            <footer className="text-sm">Enterprise Customer</footer>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <SignInForm />
          <p className="px-8 text-center text-sm text-muted-foreground">
            By clicking continue, you agree to our{" "}
            <a
              href="/terms"
              className="underline underline-offset-4 hover:text-primary"
            >
              Terms of Service
            </a>{" "}
            and{" "}
            <a
              href="/privacy"
              className="underline underline-offset-4 hover:text-primary"
            >
              Privacy Policy
            </a>
            .
          </p>
        </div>
      </div>
    </div>
  )
}
```

## Task Breakdown

### Phase 1: Better-Auth Setup (2-3 hours)

1. **Install Better-Auth Dependencies**
   ```bash
   npm install better-auth
   npm install @better-auth/prisma-adapter
   npm install @better-auth/react
   ```

2. **Configure Better-Auth**
   - File: `lib/auth/auth.config.ts`
   - Setup database adapter with Prisma
   - Configure email/password authentication
   - Add social providers (Google, GitHub)

3. **Create API Routes**
   - File: `app/api/auth/[...all]/route.ts`
   - Implement authentication endpoints
   - Add proper error handling

### Phase 2: Database Schema Updates (1-2 hours)

4. **Update Prisma Schema**
   - Add better-auth required tables
   - Update user model with auth fields
   - Add organization and session models

5. **Create Database Migration**
   - Command: `npx prisma migrate dev --name add-auth-tables`
   - Verify schema changes
   - Test migration rollback

### Phase 3: Client-Side Integration (2-3 hours)

6. **Create Authentication Hook**
   - File: `hooks/use-auth.ts`
   - Setup better-auth React client
   - Add organization management

7. **Build Authentication Components**
   - File: `components/auth/signin-form.tsx`
   - File: `components/auth/signup-form.tsx`
   - Implement social login buttons
   - Add form validation and error handling

8. **Create Authentication Pages**
   - File: `app/auth/signin/page.tsx`
   - File: `app/auth/signup/page.tsx`
   - File: `app/auth/callback/page.tsx`
   - Add proper styling and responsive design

### Phase 4: Middleware and Security (1-2 hours)

9. **Implement Authentication Middleware**
   - File: `lib/middleware/auth-middleware.ts`
   - Add session validation
   - Implement route protection

10. **Create Protected Route Wrapper**
    - File: `components/auth/protected-route.tsx`
    - Add loading states
    - Implement redirect logic

### Phase 5: Multi-Tenant Integration (2-3 hours)

11. **Integrate with Tenant Context**
    - Connect authentication with tenant resolution
    - Add tenant-scoped user management
    - Implement organization switching

12. **Add Role-Based Access Control**
    - File: `lib/rbac/permissions.ts`
    - Define permission system
    - Add role validation utilities

## Integration Points

### Database Integration
- **Schema Updates**: Better-auth tables with tenant relationships
- **Migration Strategy**: Seamless integration with existing schema
- **Data Consistency**: Ensure referential integrity

### API Endpoint Integration
- **Authentication Routes**: Standardized auth endpoints
- **Session Management**: Secure session handling
- **Error Responses**: Consistent error formatting

### Frontend Integration
- **Authentication State**: Global auth state management
- **Protected Routes**: Route-level protection
- **Loading States**: Smooth authentication flows

### Multi-Tenant Integration
- **Tenant Context**: Authentication with tenant awareness
- **Organization Management**: Multi-tenant organization support
- **Role Permissions**: Tenant-scoped permissions

## Validation Gates

### Level 1: Installation & Configuration
```bash
# Install dependencies
npm install better-auth @better-auth/prisma-adapter @better-auth/react

# TypeScript validation
npx tsc --noEmit

# Prisma validation
npx prisma validate
```

### Level 2: Database Tests
```bash
# Run database migration
npx prisma migrate dev --name add-auth-tables

# Generate Prisma client
npx prisma generate

# Test database connection
npm run test:db:connection
```

### Level 3: Authentication Tests
```bash
# Test authentication flow
npm run test:auth

# Test session management
npm run test:auth:session

# Test social providers
npm run test:auth:social
```

### Level 4: Integration Tests
```bash
# Start development server
npm run dev

# Test signin endpoint
curl -X POST http://localhost:3000/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Test session endpoint
curl -X GET http://localhost:3000/api/auth/session \
  -H "Cookie: better-auth.session.token=SESSION_TOKEN"
```

### Level 5: Security Tests
```bash
# Test CSRF protection
npm run test:security:csrf

# Test rate limiting
npm run test:security:ratelimit

# Test session security
npm run test:security:session
```

## Error Handling and Edge Cases

### Authentication Errors
- **Invalid Credentials**: Clear error messages
- **Account Lockout**: Implement after failed attempts
- **Email Verification**: Handle unverified accounts

### Session Management
- **Session Expiration**: Automatic refresh tokens
- **Concurrent Sessions**: Handle multiple sessions
- **Session Hijacking**: Detect and prevent

### Social Provider Issues
- **Provider Downtime**: Graceful error handling
- **OAuth Errors**: User-friendly error messages
- **Account Linking**: Handle existing accounts

## Multi-Tenant Architecture Considerations

### Tenant-Scoped Authentication
- **Tenant Isolation**: Complete user isolation per tenant
- **Cross-Tenant Security**: Prevent cross-tenant access
- **Tenant Switching**: Secure tenant switching flow

### Organization Management
- **Multi-Tenant Organizations**: Support for tenant-specific orgs
- **Role Inheritance**: Tenant and organization roles
- **Invitation System**: Tenant-aware invitations

### Security Requirements
- **Session Security**: Tenant-scoped sessions
- **Permission System**: Multi-level permission checking
- **Audit Logging**: Complete authentication audit trail

## Success Criteria

### Functional Requirements
- ✅ Email/password authentication working
- ✅ Social login (Google, GitHub) functional
- ✅ Multi-tenant session management
- ✅ Organization management system

### Security Requirements
- ✅ CSRF protection enabled
- ✅ Rate limiting implemented
- ✅ Session security hardened
- ✅ Proper error handling

### Performance Requirements
- ✅ Sub-100ms authentication responses
- ✅ Efficient session management
- ✅ Optimized database queries
- ✅ Proper caching strategy

## Documentation and Deployment

### Documentation Requirements
- **Authentication Flow**: Complete flow documentation
- **API Documentation**: Authentication endpoints
- **Security Guide**: Security best practices
- **Integration Guide**: Multi-tenant integration

### Environment Variables
```bash
# Add to .env.local
BETTER_AUTH_SECRET=your-secret-here
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
NEXT_PUBLIC_APP_URL=http://localhost:3000
COOKIE_DOMAIN=localhost
```

### Deployment Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Social provider credentials set
- [ ] HTTPS enabled for production
- [ ] Security headers configured

---

**Implementation Time Estimate**: 8-12 hours for complete implementation  
**Dependencies**: Multi-tenant database architecture, environment setup  
**Risk Level**: Medium - Core authentication system  
**Validation**: Authentication flow testing required  

**Quality Score Target**: 9/10 (Critical authentication component)  
- Context Completeness: 3/3 ✅
- Implementation Clarity: 3/3 ✅  
- Validation Coverage: 2/2 ✅
- Multi-Tenant Readiness: 1/1 ✅

---

*Built with ❤️ by Nexus-Master Agent*  
*Modern Authentication for Multi-Tenant SaaS*
