# Third-Party Service Integrations - Popular service connections

## Purpose

This PRP provides a comprehensive implementation guide for building a robust third-party service integration system within the NEXUS SaaS Starter. The system enables seamless connections to popular services like Slack, Discord, GitHub, Microsoft Teams, and other APIs, with enterprise-grade security, multi-tenancy, and monitoring capabilities.

## Core Principles

### Context
- **Deep Integration Patterns**: Leverage Context7-verified patterns from Discord Interactions JS, GitHub Script, Slack Bolt.js, and enterprise integration frameworks
- **Multi-Service Architecture**: Support multiple integration types with unified management and consistent patterns
- **Real-time Communication**: Enable bidirectional communication with external services through webhooks and APIs

### Validation
- **Security-First Design**: Implement OAuth 2.0, API key management, signature verification, and secure credential storage
- **Rate Limiting & Resilience**: Built-in retry mechanisms, circuit breakers, and rate limiting for external API calls
- **Comprehensive Testing**: Unit, integration, and end-to-end tests covering all integration scenarios

### Information Density
- **Enterprise Integration Patterns**: Implement proven patterns for service discovery, message routing, and error handling
- **Monitoring & Observability**: Full logging, metrics, and health checks for all integrations
- **Developer Experience**: Auto-generated documentation, SDK patterns, and integration templates

### Progressive Success
- **Modular Architecture**: Each service integration is independently deployable and configurable
- **Graceful Degradation**: System continues operating when individual integrations fail
- **Incremental Rollout**: Support for feature flags and gradual service enablement

### Production-Ready
- **Scalable Design**: Handle high-volume API calls with connection pooling and caching
- **Multi-Tenant Isolation**: Complete tenant separation for credentials, configurations, and data
- **Enterprise Security**: Audit logging, compliance features, and security scanning

### Multi-Tenant
- **Tenant-Specific Configurations**: Each workspace can configure their own service integrations
- **Isolated Credentials**: Secure per-tenant credential storage and management
- **Usage Analytics**: Per-tenant usage tracking and billing integration

## Goals

1. **Unified Integration Platform**: Create a single platform for managing all third-party service integrations
2. **Developer-Friendly APIs**: Provide clean, consistent APIs for adding new service integrations
3. **Enterprise Security**: Implement industry-standard security practices for credential management
4. **Real-time Capabilities**: Support real-time bidirectional communication with external services
5. **Monitoring & Analytics**: Comprehensive observability for all integration activities
6. **Multi-Tenant Architecture**: Complete isolation and customization per workspace

## Success Criteria

- [ ] **Integration Management**: Complete CRUD operations for service integrations per workspace
- [ ] **Popular Services**: Working integrations for Slack, Discord, GitHub, Microsoft Teams
- [ ] **Security Implementation**: OAuth 2.0 flows, API key management, and signature verification
- [ ] **Real-time Communication**: Webhook handling and real-time event processing
- [ ] **Monitoring Dashboard**: Health checks, usage analytics, and error tracking
- [ ] **Developer Tools**: Integration templates, testing tools, and documentation
- [ ] **Multi-Tenant Support**: Complete tenant isolation and per-workspace configuration
- [ ] **Performance**: Sub-200ms response times for integration API calls
- [ ] **Reliability**: 99.9% uptime with proper error handling and retries

## All Needed Context

### Critical Documentation

#### Discord Integration Patterns
- **Discord Interactions JS**: Signature verification with `verifyKeyMiddleware` and `verifyKey` functions
- **Request Validation**: Ed25519 signature verification using `X-Signature-Ed25519` and `X-Signature-Timestamp` headers
- **Interaction Types**: Support for `APPLICATION_COMMAND` and `CHANNEL_MESSAGE_WITH_SOURCE` response types

#### GitHub Integration Patterns  
- **GitHub Script Action**: Pre-authenticated Octokit client with pagination plugins
- **API Context**: Access to `github`, `context`, `core`, `glob`, `io`, `exec` objects
- **GraphQL Support**: Custom GraphQL queries with variables for flexible data retrieval
- **Error Handling**: Retry mechanisms with configurable status code exemptions

#### Slack Integration Patterns
- **Bolt.js Framework**: Event-driven architecture with `app.message()`, `app.action()`, `app.command()` listeners
- **Event Subscriptions**: Support for `message.channels`, `message.groups`, `message.im`, `message.mpim`
- **Web API Client**: Pre-authenticated client with token management for multi-workspace installations
- **Real-time Features**: Socket Mode for real-time bidirectional communication

#### Enterprise Integration Patterns
- **OAuth 2.0 Flows**: Authorization code flow with PKCE for secure credential exchange
- **Webhook Security**: Signature verification, replay attack prevention, and secure payload handling
- **Rate Limiting**: Token bucket algorithms with per-service and per-tenant limits
- **Circuit Breakers**: Fail-fast patterns with exponential backoff and health checks

### Current Codebase Patterns

#### Next.js 15.4+ Route Handler Pattern
```typescript
// app/api/integrations/[service]/route.ts
export async function POST(request: Request, { params }: { params: { service: string } }) {
  const { service } = params;
  // Handle service-specific integration logic
}
```

#### Multi-Tenant Database Pattern
```typescript
// Workspace-scoped queries
const integration = await prisma.integration.findFirst({
  where: {
    workspaceId: workspace.id,
    service: 'slack'
  }
});
```

#### API Key Authentication Pattern
```typescript
// lib/auth/api-key.ts
export async function validateApiKey(request: Request) {
  const apiKey = request.headers.get('x-api-key');
  // Validate against workspace API keys
}
```

#### Webhook Signature Verification Pattern
```typescript
// lib/webhooks/verify.ts
export async function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
) {
  // Implement service-specific signature verification
}
```

### Known Gotchas

#### Next.js 15.4+ App Router
- **Route Handlers**: Use proper TypeScript types for `Request` and `Response` objects
- **Dynamic Routes**: Handle `params` correctly in dynamic route segments
- **Middleware**: Apply authentication middleware before integration handlers
- **Error Boundaries**: Implement proper error handling for external API failures

#### Third-Party Service Integration
- **Rate Limiting**: Each service has different rate limits and retry policies
- **Authentication**: OAuth flows vary significantly between services
- **Webhook Verification**: Each service uses different signature algorithms
- **API Versioning**: Handle multiple API versions and deprecation notices

#### Multi-Tenancy
- **Credential Isolation**: Never leak credentials between workspaces
- **Configuration Scoping**: All integration settings must be workspace-scoped
- **Usage Tracking**: Implement per-tenant usage monitoring and billing
- **Data Isolation**: Ensure complete data separation between tenants

#### Security Considerations
- **Credential Storage**: Use encryption at rest for all stored credentials
- **Token Refresh**: Implement automatic OAuth token refresh mechanisms
- **Audit Logging**: Log all integration activities for compliance
- **Input Validation**: Validate all external API responses and webhook payloads

#### Performance & Reliability
- **Connection Pooling**: Reuse HTTP connections for external API calls
- **Caching**: Cache frequently accessed integration data
- **Timeouts**: Set appropriate timeouts for all external API calls
- **Monitoring**: Implement comprehensive health checks and alerting

## Implementation Blueprint

### Data Models and Structure

#### Prisma Schema
```prisma
model Integration {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  service     String   // 'slack', 'discord', 'github', 'teams'
  name        String   // User-friendly name
  description String?
  
  // Configuration
  config      Json     // Service-specific configuration
  credentials Json     // Encrypted credentials
  
  // Status
  status      IntegrationStatus @default(ACTIVE)
  lastSync    DateTime?
  lastError   String?
  
  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String
  
  // Relations
  events      IntegrationEvent[]
  logs        IntegrationLog[]
  
  @@unique([workspaceId, service, name])
  @@map("integrations")
}

model IntegrationEvent {
  id            String   @id @default(cuid())
  integrationId String
  integration   Integration @relation(fields: [integrationId], references: [id], onDelete: Cascade)
  
  eventType     String   // 'webhook', 'api_call', 'sync'
  direction     EventDirection // 'inbound', 'outbound'
  
  // Event Data
  payload       Json
  response      Json?
  
  // Status
  status        EventStatus @default(PENDING)
  error         String?
  retryCount    Int @default(0)
  
  // Timing
  createdAt     DateTime @default(now())
  processedAt   DateTime?
  
  @@map("integration_events")
}

model IntegrationLog {
  id            String   @id @default(cuid())
  integrationId String
  integration   Integration @relation(fields: [integrationId], references: [id], onDelete: Cascade)
  
  level         LogLevel // 'info', 'warn', 'error'
  message       String
  metadata      Json?
  
  createdAt     DateTime @default(now())
  
  @@map("integration_logs")
}

model IntegrationTemplate {
  id          String   @id @default(cuid())
  service     String   @unique
  name        String
  description String
  
  // Template Configuration
  configSchema Json    // JSON schema for configuration
  authType     AuthType // 'oauth2', 'api_key', 'webhook'
  
  // Documentation
  setupGuide   String
  apiDocs      String?
  
  // Metadata
  version      String
  isActive     Boolean @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  @@map("integration_templates")
}

enum IntegrationStatus {
  ACTIVE
  INACTIVE
  ERROR
  CONFIGURING
}

enum EventDirection {
  INBOUND
  OUTBOUND
}

enum EventStatus {
  PENDING
  PROCESSING
  SUCCESS
  FAILED
  RETRYING
}

enum LogLevel {
  INFO
  WARN
  ERROR
  DEBUG
}

enum AuthType {
  OAUTH2
  API_KEY
  WEBHOOK
  BASIC_AUTH
}
```

### List of Tasks to be Completed

#### 1. Core Integration Service (Priority: Critical)
- [ ] **Integration Manager Service**
  - Create `lib/integrations/manager.ts` with CRUD operations
  - Implement workspace-scoped integration management
  - Add integration status tracking and health checks
  - Support for multiple authentication types

- [ ] **Credential Management System**
  - Create `lib/integrations/credentials.ts` with encryption/decryption
  - Implement secure credential storage with workspace isolation
  - Add OAuth 2.0 token refresh mechanisms
  - Support for API key rotation and management

- [ ] **Service Registry**
  - Create `lib/integrations/registry.ts` for service discovery
  - Implement plugin architecture for new service integrations
  - Add service capability detection and validation
  - Support for service-specific configuration schemas

#### 2. Popular Service Integrations (Priority: High)

- [ ] **Slack Integration**
  - Create `lib/integrations/services/slack.ts` using Bolt.js patterns
  - Implement OAuth 2.0 flow with workspace installation
  - Add webhook handling for real-time events
  - Support for message posting, channel management, and user interactions

- [ ] **Discord Integration**
  - Create `lib/integrations/services/discord.ts` using Discord Interactions JS
  - Implement signature verification with Ed25519
  - Add support for slash commands and message interactions
  - Handle Discord webhook events and bot interactions

- [ ] **GitHub Integration**
  - Create `lib/integrations/services/github.ts` using Octokit patterns
  - Implement GitHub App authentication and installation
  - Add webhook handling for repository events
  - Support for issue management, PR automation, and repository operations

- [ ] **Microsoft Teams Integration**
  - Create `lib/integrations/services/teams.ts` with Microsoft Graph API
  - Implement Azure AD OAuth 2.0 flow
  - Add support for team messaging and channel operations
  - Handle Teams webhook events and bot framework integration

#### 3. API Endpoints (Priority: High)

- [ ] **Integration Management API**
  - Create `app/api/integrations/route.ts` for CRUD operations
  - Add `app/api/integrations/[id]/route.ts` for specific integration management
  - Implement workspace-scoped access control
  - Add integration testing and validation endpoints

- [ ] **Service-Specific Endpoints**
  - Create `app/api/integrations/slack/route.ts` for Slack-specific operations
  - Add `app/api/integrations/discord/route.ts` for Discord interactions
  - Create `app/api/integrations/github/route.ts` for GitHub operations
  - Add `app/api/integrations/teams/route.ts` for Teams functionality

- [ ] **Webhook Receivers**
  - Create `app/api/webhooks/integrations/[service]/route.ts` for webhook handling
  - Implement signature verification for each service
  - Add event processing and routing logic
  - Support for webhook retry and failure handling

- [ ] **OAuth Callback Handlers**
  - Create `app/api/auth/integrations/[service]/callback/route.ts`
  - Implement OAuth 2.0 authorization code flow
  - Add state validation and CSRF protection
  - Support for workspace-specific OAuth installations

#### 4. Security & Authentication (Priority: Critical)

- [ ] **OAuth 2.0 Implementation**
  - Create `lib/auth/oauth.ts` with service-agnostic OAuth handling
  - Implement PKCE for enhanced security
  - Add state management and CSRF protection
  - Support for token refresh and revocation

- [ ] **Webhook Security**
  - Create `lib/webhooks/security.ts` for signature verification
  - Implement replay attack prevention
  - Add IP allowlisting for webhook sources
  - Support for multiple signature algorithms

- [ ] **API Key Management**
  - Create `lib/auth/api-keys.ts` for workspace API key management
  - Implement key rotation and expiration
  - Add usage tracking and rate limiting
  - Support for scoped permissions

#### 5. UI Components (Priority: Medium)

- [ ] **Integration Dashboard**
  - Create `components/integrations/IntegrationDashboard.tsx`
  - Add integration status overview and health monitoring
  - Implement real-time status updates
  - Support for bulk operations and filtering

- [ ] **Integration Setup Wizard**
  - Create `components/integrations/SetupWizard.tsx`
  - Add step-by-step integration configuration
  - Implement service-specific setup flows
  - Support for OAuth authorization and credential input

- [ ] **Integration Cards**
  - Create `components/integrations/IntegrationCard.tsx`
  - Add service-specific configuration panels
  - Implement status indicators and action buttons
  - Support for integration testing and validation

- [ ] **Event Log Viewer**
  - Create `components/integrations/EventLogViewer.tsx`
  - Add real-time event streaming and filtering
  - Implement error details and retry mechanisms
  - Support for event replay and debugging

#### 6. Monitoring & Analytics (Priority: Medium)

- [ ] **Health Check System**
  - Create `lib/monitoring/health-checks.ts`
  - Implement service-specific health validation
  - Add automated failure detection and alerting
  - Support for custom health check intervals

- [ ] **Usage Analytics**
  - Create `lib/analytics/integration-usage.ts`
  - Implement per-tenant usage tracking
  - Add API call metrics and rate limiting
  - Support for billing integration and reporting

- [ ] **Error Tracking**
  - Create `lib/monitoring/error-tracking.ts`
  - Implement comprehensive error logging and categorization
  - Add error rate monitoring and alerting
  - Support for error trend analysis and debugging

#### 7. Testing & Documentation (Priority: Medium)

- [ ] **Integration Testing Framework**
  - Create `tests/integrations/` test suite
  - Implement mock services for testing
  - Add end-to-end integration tests
  - Support for webhook testing and validation

- [ ] **API Documentation**
  - Create OpenAPI specifications for all integration endpoints
  - Add interactive API documentation
  - Implement code examples and SDKs
  - Support for service-specific documentation

## Technology Stack

### Backend
- **Framework**: Next.js 15.4+ App Router with TypeScript 5.8+
- **Database**: PostgreSQL with Prisma ORM for multi-tenant data isolation
- **Authentication**: better-auth with OAuth 2.0 and API key support
- **Validation**: Valibot for request/response validation
- **Encryption**: Node.js crypto module for credential encryption

### Integration Libraries
- **Slack**: @slack/bolt for Slack app development
- **Discord**: discord-interactions for Discord bot interactions
- **GitHub**: @octokit/rest for GitHub API integration
- **Microsoft**: @azure/msal-node for Microsoft Graph API
- **HTTP Client**: axios with retry and timeout configuration

### Monitoring & Security
- **Logging**: Winston with structured logging
- **Metrics**: Prometheus metrics collection
- **Security**: Helmet.js for security headers
- **Rate Limiting**: Redis-based rate limiting

### Frontend
- **Framework**: React 19 with TypeScript 5.8+
- **Styling**: Tailwind CSS 4.1.11+ with shadcn/ui components
- **State Management**: Zustand for integration state
- **Real-time**: WebSocket connections for live updates

## Validation Loop

### Level 1: Syntax & Style
```bash
# ESLint validation
npx eslint app/api/integrations/ lib/integrations/ --ext .ts,.tsx

# TypeScript type checking
npx tsc --noEmit

# Prettier formatting
npx prettier --check "**/*.{ts,tsx,json,md}"
```

### Level 2: Unit Tests

#### Integration Manager Tests
```typescript
// tests/lib/integrations/manager.test.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { IntegrationManager } from '@/lib/integrations/manager';

describe('IntegrationManager', () => {
  let manager: IntegrationManager;
  
  beforeEach(() => {
    manager = new IntegrationManager();
  });

  it('should create workspace-scoped integration', async () => {
    const integration = await manager.create({
      workspaceId: 'workspace-1',
      service: 'slack',
      name: 'Team Slack',
      config: { channel: '#general' }
    });
    
    expect(integration.workspaceId).toBe('workspace-1');
    expect(integration.service).toBe('slack');
  });

  it('should enforce unique integration names per workspace', async () => {
    await manager.create({
      workspaceId: 'workspace-1',
      service: 'slack',
      name: 'Team Slack',
      config: {}
    });

    await expect(manager.create({
      workspaceId: 'workspace-1',
      service: 'slack',
      name: 'Team Slack',
      config: {}
    })).rejects.toThrow('Integration name already exists');
  });
});
```

#### Slack Integration Tests
```typescript
// tests/lib/integrations/services/slack.test.ts
import { describe, it, expect, vi } from 'vitest';
import { SlackIntegration } from '@/lib/integrations/services/slack';

describe('SlackIntegration', () => {
  it('should handle OAuth callback', async () => {
    const slack = new SlackIntegration();
    const mockCode = 'oauth-code-123';
    
    const result = await slack.handleOAuthCallback(mockCode, 'workspace-1');
    
    expect(result.success).toBe(true);
    expect(result.integration).toBeDefined();
  });

  it('should verify webhook signatures', async () => {
    const slack = new SlackIntegration();
    const payload = JSON.stringify({ type: 'url_verification' });
    const signature = 'v0=signature-hash';
    const timestamp = '1234567890';
    
    const isValid = await slack.verifyWebhookSignature(
      payload, 
      signature, 
      timestamp, 
      'signing-secret'
    );
    
    expect(isValid).toBe(true);
  });
});
```

### Level 3: Integration Tests

#### Integration API Tests
```bash
# Test integration creation
curl -X POST http://localhost:3000/api/integrations \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_TOKEN" \
  -d '{
    "service": "slack",
    "name": "Team Slack",
    "config": {
      "channel": "#general",
      "notifications": true
    }
  }'

# Test integration listing
curl -X GET http://localhost:3000/api/integrations \
  -H "Authorization: Bearer $API_TOKEN"

# Test service-specific operations
curl -X POST http://localhost:3000/api/integrations/slack/message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_TOKEN" \
  -d '{
    "channel": "#general",
    "text": "Hello from NEXUS!"
  }'

# Test webhook handling
curl -X POST http://localhost:3000/api/webhooks/integrations/slack \
  -H "Content-Type: application/json" \
  -H "X-Slack-Signature: v0=signature" \
  -H "X-Slack-Request-Timestamp: 1234567890" \
  -d '{
    "type": "event_callback",
    "event": {
      "type": "message",
      "text": "Hello"
    }
  }'
```

#### OAuth Flow Tests
```bash
# Test OAuth initiation
curl -X GET "http://localhost:3000/api/auth/integrations/slack/authorize?workspace_id=workspace-1"

# Test OAuth callback
curl -X GET "http://localhost:3000/api/auth/integrations/slack/callback?code=oauth-code&state=state-token"
```

### Level 4: End-to-End & Creative Validation

#### Production Build Validation
```bash
npm run build
npm run start
```

#### E2E Testing with Playwright
```typescript
// tests/e2e/integrations.spec.ts
import { test, expect } from '@playwright/test';

test('complete integration setup flow', async ({ page }) => {
  await page.goto('/dashboard/integrations');
  
  // Click add integration
  await page.click('[data-testid="add-integration"]');
  
  // Select Slack
  await page.click('[data-testid="service-slack"]');
  
  // Fill configuration
  await page.fill('[data-testid="integration-name"]', 'Team Slack');
  
  // Start OAuth flow
  await page.click('[data-testid="authorize-slack"]');
  
  // Verify redirect to Slack
  await expect(page).toHaveURL(/slack\.com/);
});
```

#### Performance Testing with k6
```javascript
// tests/performance/integrations.js
import http from 'k6/http';
import { check } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 },
    { duration: '5m', target: 100 },
    { duration: '2m', target: 0 },
  ],
};

export default function() {
  let response = http.get('http://localhost:3000/api/integrations', {
    headers: { 'Authorization': 'Bearer test-token' }
  });
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 200ms': (r) => r.timings.duration < 200,
  });
}
```

#### Security Testing
```bash
# Test webhook signature verification
npm run test:security:webhooks

# Test OAuth state validation
npm run test:security:oauth

# Test credential encryption
npm run test:security:credentials

# Test rate limiting
npm run test:security:rate-limits
```

#### Multi-Tenant Validation
```bash
# Test workspace isolation
npm run test:multi-tenant:isolation

# Test per-tenant configuration
npm run test:multi-tenant:config

# Test credential separation
npm run test:multi-tenant:credentials
```

## Final Validation Checklist

- [ ] All integration services implement proper error handling and retries
- [ ] OAuth flows include PKCE and state validation for security
- [ ] Webhook endpoints verify signatures and prevent replay attacks
- [ ] Multi-tenant isolation is enforced at all levels
- [ ] Rate limiting is implemented per service and per tenant
- [ ] All credentials are encrypted at rest and in transit
- [ ] Health checks monitor all external service connections
- [ ] Usage analytics track per-tenant API consumption
- [ ] Integration templates support easy addition of new services
- [ ] Documentation includes setup guides for all supported services
- [ ] E2E tests cover complete integration workflows
- [ ] Performance tests validate sub-200ms response times
- [ ] Security tests verify all authentication and authorization flows

## Anti-Patterns to Avoid

1. **Hardcoded Credentials**: Never store API keys or secrets in code
2. **Shared State**: Avoid global state that could leak between tenants
3. **Blocking Operations**: Don't block the main thread with long-running API calls
4. **Insufficient Error Handling**: Always handle external API failures gracefully
5. **Missing Rate Limits**: Implement rate limiting for all external API calls
6. **Weak Signature Verification**: Always verify webhook signatures properly
7. **Insecure OAuth**: Don't skip PKCE or state validation in OAuth flows
8. **Poor Logging**: Avoid logging sensitive data like credentials or tokens
9. **Tight Coupling**: Don't couple integration logic to specific UI components
10. **Missing Monitoring**: Always implement health checks and error tracking