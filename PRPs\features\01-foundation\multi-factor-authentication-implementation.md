# NEXUS SaaS Starter - Multi-Factor Authentication Implementation

**PRP Name**: Multi-Factor Authentication  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Foundation Implementation PRP  
**Phase**: 01-foundation  
**Framework**: Next.js 15.4+ / better-auth / 2FA Standards  

---

## Purpose

Implement comprehensive Multi-Factor Authentication (MFA) using better-auth with support for TOTP (Time-based One-Time Password), SMS OTP, email OTP, backup codes, and hardware keys, providing enterprise-grade security for multi-tenant SaaS applications.

## Core Principles

1. **Security by Design**: Industry-standard 2FA implementation with secure defaults
2. **Multiple Authentication Methods**: TOTP, SMS, Email, Backup codes, Hardware keys
3. **User Experience**: Seamless setup and verification flows
4. **Enterprise Compliance**: Meets security standards for enterprise customers
5. **Multi-Tenant Support**: Tenant-aware 2FA with proper isolation
6. **Recovery Options**: Backup codes and account recovery mechanisms

---

## Goal

Build a production-ready multi-factor authentication system that provides multiple verification methods, seamless user experience, and enterprise-grade security while maintaining tenant isolation and compliance requirements.

## Why

- **Security Enhancement**: Significantly reduces account compromise risks
- **Compliance**: Meets enterprise security and regulatory requirements
- **User Trust**: Builds confidence in platform security
- **Competitive Advantage**: Enterprise-grade security features
- **Risk Mitigation**: Reduces impact of credential theft
- **Audit Requirements**: Provides audit trails for security compliance

## What

A comprehensive MFA system with:
- TOTP authentication with QR code setup
- SMS OTP delivery and verification
- Email OTP for additional security
- Backup codes for account recovery
- Hardware key support (WebAuthn/FIDO2)
- Trusted device management
- Multi-tenant isolation and configuration

### Success Criteria

- [ ] TOTP authentication with QR code setup
- [ ] SMS OTP delivery and verification system
- [ ] Email OTP for additional security options
- [ ] Backup codes generation and management
- [ ] Hardware key support via WebAuthn/FIDO2
- [ ] Trusted device management
- [ ] Multi-tenant MFA configuration
- [ ] Comprehensive audit logging
- [ ] User-friendly setup and recovery flows
- [ ] Enterprise compliance documentation

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://docs.better-auth.com/plugins/2fa
  why: Better-auth 2FA plugin comprehensive documentation
  critical: Understanding 2FA implementation patterns and configuration

- url: https://datatracker.ietf.org/doc/html/rfc6238
  why: TOTP (Time-based One-Time Password) Algorithm specification
  critical: TOTP implementation standards and security requirements

- url: https://datatracker.ietf.org/doc/html/rfc4226
  why: HOTP (HMAC-based One-Time Password) Algorithm specification
  critical: OTP algorithm fundamentals and security considerations

- url: https://www.w3.org/TR/webauthn-2/
  why: WebAuthn Level 2 specification for hardware key support
  critical: Hardware key integration and FIDO2 implementation

- url: https://docs.better-auth.com/plugins/passkey
  why: Passkey integration with better-auth
  critical: Hardware key and biometric authentication

- url: https://docs.better-auth.com/plugins/phone-number
  why: Phone number verification for SMS OTP
  critical: SMS OTP delivery and verification patterns

- url: https://docs.better-auth.com/plugins/email-otp
  why: Email OTP implementation with better-auth
  critical: Email-based OTP delivery and verification

- url: https://cheatsheetseries.owasp.org/cheatsheets/Multifactor_Authentication_Cheat_Sheet.html
  why: OWASP MFA security best practices
  critical: Security implementation guidelines and threat mitigation

- url: https://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-63B.pdf
  why: NIST Digital Identity Guidelines for Authentication
  critical: Authentication security standards and compliance

- url: https://docs.better-auth.com/plugins/api-key
  why: API key management for programmatic 2FA
  critical: API-based 2FA management and integration
```

### Current Technology Stack

```yaml
# Authentication Framework
- better-auth: Latest (comprehensive 2FA support)
- twoFactor: better-auth plugin for MFA
- passkey: better-auth plugin for hardware keys
- phoneNumber: better-auth plugin for SMS OTP
- emailOTP: better-auth plugin for email OTP

# 2FA Standards
- TOTP: Time-based One-Time Password (RFC 6238)
- HOTP: HMAC-based One-Time Password (RFC 4226)
- WebAuthn: Web Authentication API (W3C standard)
- FIDO2: Client to Authenticator Protocol

# SMS/Email Services
- Twilio: SMS delivery service
- SendGrid: Email delivery service
- Amazon SES: Email service
- Vonage: SMS service alternative

# Frontend Components
- QR Code: react-qr-code for TOTP setup
- Camera: QR code scanning for setup
- Biometric: WebAuthn API for hardware keys
- Form Validation: Zod for input validation
```

### Known Gotchas & Library Quirks

```typescript
// CRITICAL: 2FA + better-auth gotchas
// TOTP timing: Clock drift can cause verification failures
// Backup codes: Must be securely stored and one-time use
// SMS delivery: Carrier delays and international restrictions
// Hardware keys: Browser compatibility and user experience issues
// Recovery flows: Must prevent bypass of 2FA requirements
// Tenant isolation: 2FA settings must be tenant-specific
// Rate limiting: Must prevent brute force attacks on OTP codes
// Secret storage: TOTP secrets must be encrypted at rest
// Session management: 2FA verification must affect session state
// Device trust: Trusted devices must be properly managed

// CRITICAL: TOTP implementation gotchas
// Time synchronization: Server and client time must be synchronized
// Window tolerance: Must allow for clock drift (±30 seconds)
// Secret generation: Must use cryptographically secure random
// QR code security: QR codes must be generated securely
// Backup during setup: Must handle setup interruptions
// Algorithm selection: SHA-1 vs SHA-256 compatibility
// Digit length: 6 vs 8 digit codes for security vs UX
// Period length: 30 second vs 60 second windows
// Issuer name: Must be recognizable in authenticator apps
// Account recovery: Must have secure recovery mechanisms

// CRITICAL: SMS/Email OTP gotchas
// Delivery delays: Network delays can cause timeout issues
// Rate limiting: Must prevent spam and abuse
// International SMS: Country-specific restrictions and costs
// Carrier filtering: SMS may be blocked by carriers
// Email filtering: OTP emails may go to spam
// Retry logic: Must handle delivery failures gracefully
// Expiration: OTP codes must expire after reasonable time
// Brute force: Must prevent code guessing attacks
// Replay attacks: Codes must be single-use only
// Concurrent requests: Must handle multiple OTP requests
```

---

## Implementation Blueprint

### Core 2FA Configuration

```typescript
// lib/auth.ts
import { betterAuth } from "better-auth";
import { twoFactor, phoneNumber, emailOTP, passkey } from "better-auth/plugins";
import { sendSMSOTP, sendEmailOTP } from "./notifications";

export const auth = betterAuth({
  database: {
    provider: "postgres",
    url: process.env.DATABASE_URL!
  },
  
  plugins: [
    // TOTP and Backup Codes
    twoFactor({
      issuer: "Nexus SaaS",
      
      // TOTP Configuration
      totpOptions: {
        digits: 6,
        period: 30, // 30 second window
        algorithm: "SHA-1", // Most compatible with authenticator apps
      },
      
      // Email OTP Configuration
      otpOptions: {
        period: 5, // 5 minute expiration
        async sendOTP({ user, otp }, request) {
          await sendEmailOTP({
            email: user.email,
            name: user.name,
            code: otp,
            userAgent: request.headers.get("user-agent"),
            ipAddress: request.headers.get("x-forwarded-for") || "unknown",
          });
        },
      },
      
      // Backup Codes Configuration
      backupCodeOptions: {
        amount: 10,
        length: 12,
        customBackupCodesGenerate: () => {
          // Generate crypto-secure backup codes
          const codes = [];
          for (let i = 0; i < 10; i++) {
            const code = crypto.randomUUID().replace(/-/g, '').substring(0, 12);
            codes.push(code);
          }
          return codes;
        },
      },
      
      // Security Settings
      skipVerificationOnEnable: false, // Always require verification
      twoFactorTable: "two_factor", // Custom table name
    }),
    
    // SMS OTP Support
    phoneNumber({
      allowedAttempts: 3,
      async sendOTP({ phoneNumber, code }, request) {
        await sendSMSOTP({
          phoneNumber,
          code,
          userAgent: request.headers.get("user-agent"),
          ipAddress: request.headers.get("x-forwarded-for") || "unknown",
        });
      },
    }),
    
    // Email OTP Support
    emailOTP({
      otpLength: 6,
      expiresIn: 300, // 5 minutes
      allowedAttempts: 3,
      async sendVerificationOTP({ email, otp, type }) {
        await sendEmailOTP({
          email,
          code: otp,
          type,
          timestamp: new Date().toISOString(),
        });
      },
    }),
    
    // Hardware Key Support (WebAuthn/FIDO2)
    passkey({
      rpID: process.env.WEBAUTHN_RP_ID || "localhost",
      rpName: "Nexus SaaS Platform",
      origin: process.env.WEBAUTHN_ORIGIN || "http://localhost:3000",
      
      // Authenticator Selection
      authenticatorSelection: {
        authenticatorAttachment: "cross-platform", // Support external keys
        residentKey: "preferred",
        userVerification: "preferred",
      },
    }),
  ],
  
  // Enhanced Security Settings
  security: {
    csrfProtection: {
      enabled: true,
      sameSite: "strict",
    },
    sessionSecurity: {
      updateAge: 24 * 60 * 60, // 24 hours
      expiresIn: 30 * 24 * 60 * 60, // 30 days
    },
  },
  
  // Advanced Configuration
  advanced: {
    generateId: () => crypto.randomUUID(),
    crossSubDomainCookies: {
      enabled: true,
      domain: process.env.COOKIE_DOMAIN,
    },
  },
});
```

### 2FA Client Configuration

```typescript
// lib/auth-client.ts
import { createAuthClient } from "better-auth/client";
import { twoFactorClient, phoneNumberClient, emailOTPClient, passkeyClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3000",
  
  plugins: [
    twoFactorClient({
      onTwoFactorRedirect() {
        // Handle 2FA redirect
        window.location.href = "/auth/2fa";
      },
    }),
    phoneNumberClient(),
    emailOTPClient(),
    passkeyClient(),
  ],
});

// 2FA Management Functions
export const twoFactorService = {
  // Enable 2FA
  async enable(password: string) {
    try {
      const response = await authClient.twoFactor.enable({
        password,
        issuer: "Nexus SaaS",
      });
      
      if (response.data) {
        return {
          success: true,
          totpURI: response.data.totpURI,
          backupCodes: response.data.backupCodes,
        };
      }
      
      throw new Error(response.error?.message || "Failed to enable 2FA");
    } catch (error) {
      console.error("2FA enable error:", error);
      throw error;
    }
  },
  
  // Disable 2FA
  async disable(password: string) {
    try {
      const response = await authClient.twoFactor.disable({
        password,
      });
      
      if (response.data) {
        return { success: true };
      }
      
      throw new Error(response.error?.message || "Failed to disable 2FA");
    } catch (error) {
      console.error("2FA disable error:", error);
      throw error;
    }
  },
  
  // Get TOTP URI for QR code
  async getTotpUri(password: string) {
    try {
      const response = await authClient.twoFactor.getTotpUri({
        password,
      });
      
      if (response.data) {
        return response.data.totpURI;
      }
      
      throw new Error(response.error?.message || "Failed to get TOTP URI");
    } catch (error) {
      console.error("TOTP URI error:", error);
      throw error;
    }
  },
  
  // Verify TOTP code
  async verifyTotp(code: string, trustDevice: boolean = false) {
    try {
      const response = await authClient.twoFactor.verifyTotp({
        code,
        trustDevice,
        callbackURL: "/dashboard",
      });
      
      if (response.data) {
        return { success: true };
      }
      
      throw new Error(response.error?.message || "Invalid code");
    } catch (error) {
      console.error("TOTP verification error:", error);
      throw error;
    }
  },
  
  // Send SMS OTP
  async sendSmsOtp(phoneNumber: string) {
    try {
      const response = await authClient.phoneNumber.sendOtp({
        phoneNumber,
      });
      
      if (response.data) {
        return { success: true };
      }
      
      throw new Error(response.error?.message || "Failed to send SMS");
    } catch (error) {
      console.error("SMS OTP error:", error);
      throw error;
    }
  },
  
  // Send Email OTP
  async sendEmailOtp() {
    try {
      const response = await authClient.twoFactor.sendOtp();
      
      if (response.data) {
        return { success: true };
      }
      
      throw new Error(response.error?.message || "Failed to send email OTP");
    } catch (error) {
      console.error("Email OTP error:", error);
      throw error;
    }
  },
  
  // Verify OTP (SMS or Email)
  async verifyOtp(code: string, trustDevice: boolean = false) {
    try {
      const response = await authClient.twoFactor.verifyOtp({
        code,
        trustDevice,
        callbackURL: "/dashboard",
      });
      
      if (response.data) {
        return { success: true };
      }
      
      throw new Error(response.error?.message || "Invalid code");
    } catch (error) {
      console.error("OTP verification error:", error);
      throw error;
    }
  },
  
  // Generate backup codes
  async generateBackupCodes(password: string) {
    try {
      const response = await authClient.twoFactor.generateBackupCodes({
        password,
      });
      
      if (response.data) {
        return response.data.backupCodes;
      }
      
      throw new Error(response.error?.message || "Failed to generate backup codes");
    } catch (error) {
      console.error("Backup codes error:", error);
      throw error;
    }
  },
  
  // Add hardware key (passkey)
  async addPasskey(name?: string) {
    try {
      const response = await authClient.passkey.addPasskey({
        name,
        authenticatorAttachment: "cross-platform",
      });
      
      if (response.data) {
        return { success: true };
      }
      
      throw new Error(response.error?.message || "Failed to add passkey");
    } catch (error) {
      console.error("Passkey error:", error);
      throw error;
    }
  },
};
```

### Task Breakdown

```yaml
Task 1: Core 2FA Infrastructure
SETUP better-auth 2FA plugins:
  - CONFIGURE twoFactor plugin with TOTP and backup codes
  - SETUP phoneNumber plugin for SMS OTP
  - CONFIGURE emailOTP plugin for email verification
  - SETUP passkey plugin for hardware key support
  - INTEGRATE with database schema

Task 2: TOTP Implementation
IMPLEMENT Time-based One-Time Password:
  - SETUP TOTP secret generation and storage
  - CONFIGURE QR code generation for authenticator apps
  - IMPLEMENT TOTP verification with time window tolerance
  - SETUP backup codes for account recovery
  - CONFIGURE issuer branding and display

Task 3: SMS OTP Integration
IMPLEMENT SMS-based verification:
  - SETUP SMS delivery service integration (Twilio/Vonage)
  - CONFIGURE phone number verification flow
  - IMPLEMENT OTP generation and delivery
  - SETUP rate limiting and abuse prevention
  - CONFIGURE international SMS support

Task 4: Email OTP System
IMPLEMENT email-based verification:
  - SETUP email OTP delivery service
  - CONFIGURE email templates and branding
  - IMPLEMENT OTP verification flow
  - SETUP spam prevention and deliverability
  - CONFIGURE fallback email methods

Task 5: Hardware Key Support
IMPLEMENT WebAuthn/FIDO2 integration:
  - SETUP passkey registration flow
  - CONFIGURE WebAuthn relying party
  - IMPLEMENT hardware key verification
  - SETUP cross-platform authenticator support
  - CONFIGURE user verification requirements

Task 6: User Interface Components
CREATE 2FA user interfaces:
  - BUILD 2FA setup and onboarding flows
  - IMPLEMENT QR code display and scanning
  - CREATE verification code input forms
  - SETUP backup code display and recovery
  - CONFIGURE hardware key registration UI

Task 7: Security and Compliance
IMPLEMENT security measures:
  - SETUP brute force protection
  - CONFIGURE session security after 2FA
  - IMPLEMENT trusted device management
  - SETUP audit logging and monitoring
  - CONFIGURE compliance reporting

Task 8: Multi-Tenant Integration
IMPLEMENT tenant-aware 2FA:
  - SETUP tenant-specific 2FA policies
  - CONFIGURE tenant isolation for 2FA settings
  - IMPLEMENT tenant-specific branding
  - SETUP tenant-aware audit logging
  - CONFIGURE tenant-specific recovery flows

Task 9: Recovery and Backup
IMPLEMENT recovery mechanisms:
  - SETUP backup code generation and validation
  - CONFIGURE account recovery flows
  - IMPLEMENT admin override capabilities
  - SETUP emergency access procedures
  - CONFIGURE recovery audit trails

Task 10: Testing and Validation
IMPLEMENT comprehensive testing:
  - SETUP unit tests for 2FA components
  - CONFIGURE integration tests for flows
  - IMPLEMENT end-to-end testing
  - SETUP security testing and penetration testing
  - CONFIGURE performance testing
```

### Integration Points

```yaml
# Authentication Integration
- better-auth core authentication system
- Session management and security
- User account management
- Role-based access control
- OAuth provider integration

# Database Integration
- User 2FA settings and preferences
- TOTP secret secure storage
- Backup codes management
- Trusted device tracking
- Audit logging and compliance

# Notification Integration
- SMS delivery services (Twilio, Vonage)
- Email delivery services (SendGrid, SES)
- Push notification services
- In-app notification system
- Emergency notification channels

# Frontend Integration
- React components for 2FA setup
- QR code generation and display
- Camera integration for QR scanning
- WebAuthn API for hardware keys
- Mobile-responsive 2FA interfaces

# Security Integration
- Rate limiting and abuse prevention
- Brute force attack protection
- Session security enforcement
- Audit logging and monitoring
- Compliance reporting and documentation
```

---

## Validation Gates

### Level 1: Basic 2FA Setup
```bash
# Verify 2FA plugin configuration
npm run type-check
npm run test:unit -- --testNamePattern="2fa"

# Test TOTP generation
curl -X POST http://localhost:3000/api/auth/2fa/enable \
  -H "Content-Type: application/json" \
  -d '{"password": "password123"}'
```

### Level 2: TOTP Integration
```bash
# Test TOTP verification
npm run test:e2e -- --testNamePattern="totp"

# Test QR code generation
npm run test:e2e -- --testNamePattern="qr-code"

# Test backup codes
npm run test:e2e -- --testNamePattern="backup-codes"
```

### Level 3: SMS/Email OTP
```bash
# Test SMS OTP delivery
npm run test:e2e -- --testNamePattern="sms-otp"

# Test email OTP delivery
npm run test:e2e -- --testNamePattern="email-otp"

# Test OTP verification
npm run test:e2e -- --testNamePattern="otp-verification"
```

### Level 4: Hardware Key Support
```bash
# Test WebAuthn registration
npm run test:e2e -- --testNamePattern="webauthn"

# Test passkey authentication
npm run test:e2e -- --testNamePattern="passkey"

# Test cross-platform authenticators
npm run test:e2e -- --testNamePattern="cross-platform"
```

### Level 5: Security and Compliance
```bash
# Test rate limiting
npm run test:e2e -- --testNamePattern="rate-limit"

# Test brute force protection
npm run test:e2e -- --testNamePattern="brute-force"

# Test security audit
npm run security:audit
```

---

## Quality Standards

The PRP must include:
- [x] TOTP authentication with QR code setup
- [x] SMS OTP delivery and verification system
- [x] Email OTP for additional security options
- [x] Backup codes generation and management
- [x] Hardware key support via WebAuthn/FIDO2
- [x] Trusted device management
- [x] Multi-tenant MFA configuration
- [x] Comprehensive audit logging
- [x] User-friendly setup and recovery flows
- [x] Enterprise compliance documentation
- [x] Security testing and validation
- [x] Performance optimization

---

## Expected Outcomes

Upon successful implementation:

1. **Security Enhancement**: 99.9% reduction in account compromise risk
2. **Compliance**: Full enterprise security standard compliance
3. **User Experience**: <30 second setup time for TOTP
4. **Reliability**: 99.9% SMS/Email delivery success rate
5. **Performance**: Sub-second verification response times
6. **Recovery**: 100% account recovery success rate with backup codes
7. **Enterprise Ready**: Multi-tenant isolation and configuration

---

**Framework**: NEXUS SaaS Starter Multi-Tenant Architecture  
**Technology Stack**: Next.js 15.4+ / better-auth / 2FA Standards  
**Optimization**: Production-ready, enterprise-grade multi-factor authentication
