# NEXUS SaaS Starter - OAuth Provider Integration Implementation

**PRP Name**: OAuth Provider Integration  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Foundation Implementation PRP  
**Phase**: 01-foundation  
**Framework**: Next.js 15.4+ / better-auth / OAuth 2.0  

---

## Purpose

Implement comprehensive OAuth provider integration using better-auth with support for multiple social providers (Google, GitHub, Microsoft, etc.) and custom OAuth providers, enabling secure multi-tenant authentication with enterprise-grade security and compliance.

## Core Principles

1. **Security First**: OAuth 2.0 + PKCE, secure token handling, and CSRF protection
2. **Multi-Provider Support**: Support for major social providers and custom OAuth
3. **Multi-Tenant Ready**: Tenant-aware OAuth flows with proper isolation
4. **Enterprise Grade**: Compliance with enterprise security requirements
5. **Developer Experience**: Simple configuration and comprehensive error handling
6. **Scalable Architecture**: Supports unlimited OAuth providers and configurations

---

## Goal

Build a production-ready OAuth integration system that supports multiple social providers and custom OAuth implementations with proper security, multi-tenant isolation, and enterprise compliance requirements.

## Why

- **User Experience**: Reduce friction with social login options
- **Security**: Implement industry-standard OAuth 2.0 with PKCE
- **Compliance**: Meet enterprise security and audit requirements
- **Scalability**: Support unlimited OAuth providers and configurations
- **Multi-Tenant**: Tenant-aware authentication with proper isolation
- **Maintainability**: Centralized OAuth configuration and management

## What

A comprehensive OAuth provider integration with:
- Multiple social providers (Google, GitHub, Microsoft, etc.)
- Custom OAuth provider support via genericOAuth plugin
- PKCE implementation for enhanced security
- Multi-tenant OAuth flows with tenant isolation
- Enterprise-grade security and compliance features
- Comprehensive error handling and logging

### Success Criteria

- [ ] Multiple social providers configured and working
- [ ] Custom OAuth provider support via genericOAuth plugin
- [ ] PKCE implementation for enhanced security
- [ ] Multi-tenant OAuth flows with proper isolation
- [ ] Enterprise security compliance (SOC 2, GDPR)
- [ ] Comprehensive error handling and logging
- [ ] OAuth token management and refresh capabilities
- [ ] Integration with existing authentication system

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://docs.better-auth.com/concepts/oauth
  why: OAuth fundamentals and better-auth implementation
  critical: Understanding OAuth flows and security patterns

- url: https://docs.better-auth.com/plugins/generic-oauth
  why: Generic OAuth plugin for custom providers
  critical: Custom OAuth provider integration patterns

- url: https://oauth.net/2/
  why: OAuth 2.0 specification and best practices
  critical: OAuth 2.0 security and implementation standards

- url: https://datatracker.ietf.org/doc/html/rfc7636
  why: PKCE specification for OAuth security
  critical: PKCE implementation for enhanced security

- url: https://openid.net/specs/openid-connect-core-1_0.html
  why: OpenID Connect specification
  critical: OIDC integration and ID token handling

- url: https://docs.better-auth.com/authentication/google
  why: Google OAuth integration with better-auth
  critical: Google-specific OAuth implementation

- url: https://docs.better-auth.com/authentication/github
  why: GitHub OAuth integration with better-auth
  critical: GitHub-specific OAuth implementation

- url: https://docs.better-auth.com/authentication/microsoft
  why: Microsoft OAuth integration with better-auth
  critical: Microsoft-specific OAuth implementation

- url: https://docs.better-auth.com/plugins/oauth-proxy
  why: OAuth proxy for development and testing
  critical: Development and staging OAuth flows

- url: https://docs.better-auth.com/concepts/users-accounts
  why: User account management and OAuth linking
  critical: Account linking and user management patterns
```

### Current Technology Stack

```yaml
# Authentication Framework
- better-auth: Latest (comprehensive auth framework)
- OAuth 2.0: Standard protocol with PKCE support
- OpenID Connect: Identity layer on OAuth 2.0
- JOSE: JSON Web Signature and Encryption

# Social Providers
- Google: OAuth 2.0 + OpenID Connect
- GitHub: OAuth 2.0 with user API
- Microsoft: Azure AD / Entra ID OAuth
- Discord: OAuth 2.0 with user API
- LinkedIn: OAuth 2.0 with profile API

# Custom OAuth Support
- genericOAuth: better-auth plugin for custom providers
- PKCE: Proof Key for Code Exchange
- Dynamic Discovery: OIDC discovery document support
- Custom Mapping: Profile to user field mapping

# Security Features
- CSRF Protection: State parameter validation
- Token Security: Secure token storage and handling
- Refresh Tokens: Automatic token refresh
- Tenant Isolation: Multi-tenant security boundaries
```

### Known Gotchas & Library Quirks

```typescript
// CRITICAL: OAuth + better-auth gotchas
// State parameter: Must be properly validated to prevent CSRF
// Redirect URI: Must match exactly what's configured in provider
// Scope handling: Different providers have different scope requirements
// Token refresh: Not all providers support refresh tokens
// User info: Provider user APIs may have different structures
// Rate limiting: OAuth providers have rate limits on token exchanges
// Tenant isolation: OAuth flows must respect tenant boundaries
// Error handling: OAuth errors must be properly handled and logged
// ID token validation: Must validate ID tokens properly
// PKCE: Must be implemented for public clients

// CRITICAL: Social provider gotchas
// Google: Requires specific scopes for profile/email access
// GitHub: User API requires separate API call after token exchange
// Microsoft: Tenant-specific endpoints and configuration
// Discord: User API returns different structure than other providers
// LinkedIn: Limited profile information available
// Facebook: Requires app review for certain permissions
// Apple: Requires specific configuration for web applications
// Twitter: API v2 has different authentication requirements

// CRITICAL: Multi-tenant OAuth gotchas
// Tenant context: Must maintain tenant context through OAuth flow
// Callback URLs: Must include tenant information in callback
// User isolation: Users must be properly isolated by tenant
// Configuration: Different tenants may have different OAuth configs
// State management: Tenant state must be preserved through redirects
// Domain validation: Must validate domains for tenant security
// Session management: OAuth sessions must be tenant-aware
```

---

## Implementation Blueprint

### Core OAuth Configuration

```typescript
// lib/auth.ts
import { betterAuth } from "better-auth";
import { genericOAuth } from "better-auth/plugins";
import { multiTenant } from "better-auth/plugins";

export const auth = betterAuth({
  database: {
    provider: "postgres",
    url: process.env.DATABASE_URL!
  },
  
  // Social Providers Configuration
  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      // Enhanced security with PKCE
      accessType: "offline",
      prompt: "select_account+consent",
      mapProfileToUser: (profile) => ({
        firstName: profile.given_name,
        lastName: profile.family_name,
        avatar: profile.picture,
      }),
    },
    
    github: {
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
      mapProfileToUser: (profile) => ({
        firstName: profile.name?.split(" ")[0] || "",
        lastName: profile.name?.split(" ")[1] || "",
        avatar: profile.avatar_url,
      }),
    },
    
    microsoft: {
      clientId: process.env.MICROSOFT_CLIENT_ID!,
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET!,
      tenantId: process.env.MICROSOFT_TENANT_ID || "common",
      prompt: "select_account",
      mapProfileToUser: (profile) => ({
        firstName: profile.givenName,
        lastName: profile.surname,
        avatar: profile.picture,
      }),
    },
    
    discord: {
      clientId: process.env.DISCORD_CLIENT_ID!,
      clientSecret: process.env.DISCORD_CLIENT_SECRET!,
      mapProfileToUser: (profile) => ({
        firstName: profile.username,
        lastName: "",
        avatar: profile.avatar ? 
          `https://cdn.discordapp.com/avatars/${profile.id}/${profile.avatar}.png` : 
          null,
      }),
    },
    
    linkedin: {
      clientId: process.env.LINKEDIN_CLIENT_ID!,
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET!,
      mapProfileToUser: (profile) => ({
        firstName: profile.localizedFirstName,
        lastName: profile.localizedLastName,
        avatar: profile.profilePicture?.displayImage || null,
      }),
    },
  },
  
  // Custom OAuth Providers
  plugins: [
    genericOAuth({
      config: [
        // Auth0 Custom Provider
        {
          providerId: "auth0",
          clientId: process.env.AUTH0_CLIENT_ID!,
          clientSecret: process.env.AUTH0_CLIENT_SECRET!,
          discoveryUrl: `https://${process.env.AUTH0_DOMAIN}/.well-known/openid-configuration`,
          scopes: ["openid", "profile", "email"],
          pkce: true,
          mapProfileToUser: (profile) => ({
            firstName: profile.given_name,
            lastName: profile.family_name,
            avatar: profile.picture,
          }),
        },
        
        // Okta Custom Provider
        {
          providerId: "okta",
          clientId: process.env.OKTA_CLIENT_ID!,
          clientSecret: process.env.OKTA_CLIENT_SECRET!,
          discoveryUrl: `https://${process.env.OKTA_DOMAIN}/.well-known/openid-configuration`,
          scopes: ["openid", "profile", "email"],
          pkce: true,
          mapProfileToUser: (profile) => ({
            firstName: profile.given_name,
            lastName: profile.family_name,
            avatar: profile.picture,
          }),
        },
        
        // Custom Enterprise Provider
        {
          providerId: "enterprise-sso",
          clientId: process.env.ENTERPRISE_CLIENT_ID!,
          clientSecret: process.env.ENTERPRISE_CLIENT_SECRET!,
          authorizationUrl: process.env.ENTERPRISE_AUTHORIZATION_URL!,
          tokenUrl: process.env.ENTERPRISE_TOKEN_URL!,
          userInfoUrl: process.env.ENTERPRISE_USERINFO_URL!,
          scopes: ["openid", "profile", "email"],
          pkce: true,
          getUserInfo: async (tokens) => {
            const response = await fetch(process.env.ENTERPRISE_USERINFO_URL!, {
              headers: {
                Authorization: `Bearer ${tokens.accessToken}`,
              },
            });
            
            if (!response.ok) {
              throw new Error('Failed to fetch user info');
            }
            
            return response.json();
          },
          mapProfileToUser: (profile) => ({
            firstName: profile.given_name || profile.firstName,
            lastName: profile.family_name || profile.lastName,
            avatar: profile.picture || profile.avatar,
          }),
        },
      ],
    }),
    
    // Multi-tenant support
    multiTenant({
      tenantKey: "tenant_id",
      oauthSettings: {
        preserveTenantContext: true,
        tenantAwareCallbacks: true,
      },
    }),
  ],
  
  // Enhanced security settings
  security: {
    csrfProtection: {
      enabled: true,
      sameSite: "strict",
    },
    sessionSecurity: {
      updateAge: 24 * 60 * 60, // 24 hours
      expiresIn: 30 * 24 * 60 * 60, // 30 days
    },
  },
  
  // Advanced configuration
  advanced: {
    generateId: () => crypto.randomUUID(),
    crossSubDomainCookies: {
      enabled: true,
      domain: process.env.COOKIE_DOMAIN,
    },
  },
});
```

### OAuth Client Configuration

```typescript
// lib/auth-client.ts
import { createAuthClient } from "better-auth/client";
import { genericOAuthClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3000",
  plugins: [
    genericOAuthClient()
  ],
});

// OAuth Sign-in Functions
export const signInWithProvider = async (
  provider: string,
  options?: {
    callbackURL?: string;
    tenantId?: string;
    redirectTo?: string;
  }
) => {
  try {
    const response = await authClient.signIn.social({
      provider,
      callbackURL: options?.callbackURL || "/dashboard",
      // Include tenant context if provided
      ...(options?.tenantId && { 
        state: JSON.stringify({ tenantId: options.tenantId })
      }),
    });
    
    return response;
  } catch (error) {
    console.error(`OAuth sign-in failed for ${provider}:`, error);
    throw error;
  }
};

// OAuth Account Linking
export const linkSocialAccount = async (
  provider: string,
  options?: {
    tenantId?: string;
  }
) => {
  try {
    const response = await authClient.linkSocial({
      provider,
      ...(options?.tenantId && { 
        state: JSON.stringify({ tenantId: options.tenantId })
      }),
    });
    
    return response;
  } catch (error) {
    console.error(`OAuth account linking failed for ${provider}:`, error);
    throw error;
  }
};
```

### Task Breakdown

```yaml
Task 1: Core OAuth Configuration
SETUP better-auth OAuth configuration:
  - CONFIGURE social providers (Google, GitHub, Microsoft, Discord, LinkedIn)
  - SETUP genericOAuth plugin for custom providers
  - CONFIGURE PKCE for enhanced security
  - SETUP profile mapping for user data
  - INTEGRATE tenant-aware OAuth flows

Task 2: Social Provider Integration
IMPLEMENT social provider configurations:
  - SETUP Google OAuth with enhanced security settings
  - CONFIGURE GitHub OAuth with user API integration
  - SETUP Microsoft OAuth with tenant support
  - CONFIGURE Discord OAuth with avatar handling
  - SETUP LinkedIn OAuth with profile mapping

Task 3: Custom OAuth Provider Support
IMPLEMENT genericOAuth configurations:
  - SETUP Auth0 custom provider with OIDC discovery
  - CONFIGURE Okta custom provider with enterprise features
  - SETUP custom enterprise provider with manual endpoints
  - IMPLEMENT custom user info fetching logic
  - CONFIGURE custom profile mapping functions

Task 4: Multi-Tenant OAuth Integration
IMPLEMENT tenant-aware OAuth flows:
  - SETUP tenant context preservation through OAuth flow
  - CONFIGURE tenant-specific OAuth settings
  - IMPLEMENT tenant isolation in OAuth callbacks
  - SETUP tenant-aware user account linking
  - CONFIGURE tenant-specific error handling

Task 5: Security Implementation
IMPLEMENT OAuth security features:
  - SETUP CSRF protection with state parameters
  - CONFIGURE secure token storage and handling
  - IMPLEMENT PKCE for all OAuth flows
  - SETUP token refresh mechanisms
  - CONFIGURE session security settings

Task 6: Error Handling and Logging
IMPLEMENT comprehensive error handling:
  - SETUP OAuth error handling and user feedback
  - CONFIGURE audit logging for OAuth events
  - IMPLEMENT rate limiting protection
  - SETUP monitoring and alerting
  - CONFIGURE debugging and troubleshooting

Task 7: Development and Testing
SETUP OAuth development environment:
  - CONFIGURE OAuth proxy for development
  - SETUP test OAuth providers
  - IMPLEMENT OAuth flow testing
  - CONFIGURE integration testing
  - SETUP OAuth provider registration

Task 8: Documentation and Compliance
CREATE OAuth documentation:
  - SETUP OAuth provider registration guides
  - CONFIGURE security compliance documentation
  - IMPLEMENT OAuth flow diagrams
  - SETUP troubleshooting guides
  - CONFIGURE audit trail documentation
```

### Integration Points

```yaml
# Authentication Integration
- better-auth core authentication system
- Multi-tenant authentication flows
- User account management and linking
- Session management and security
- Role-based access control integration

# Database Integration
- User account storage and management
- OAuth provider account linking
- Tenant-specific user isolation
- Audit logging and compliance
- Session and token storage

# Frontend Integration
- OAuth sign-in UI components
- Account linking interfaces
- Error handling and user feedback
- Loading states and redirects
- Mobile and desktop OAuth flows

# Security Integration
- CSRF protection and validation
- Token security and refresh
- Tenant isolation and boundaries
- Audit logging and monitoring
- Compliance and reporting

# Multi-Tenant Integration
- Tenant context preservation
- Tenant-specific OAuth settings
- Tenant-aware user management
- Tenant isolation enforcement
- Tenant-specific error handling
```

---

## Validation Gates

### Level 1: Basic OAuth Setup
```bash
# Verify OAuth configuration
npm run type-check
npm run test:unit -- --testNamePattern="oauth"

# Test OAuth provider registration
curl -X POST http://localhost:3000/api/auth/sign-in/social \
  -H "Content-Type: application/json" \
  -d '{"provider": "google"}'
```

### Level 2: Social Provider Integration
```bash
# Test Google OAuth flow
npm run test:e2e -- --testNamePattern="google-oauth"

# Test GitHub OAuth flow
npm run test:e2e -- --testNamePattern="github-oauth"

# Test Microsoft OAuth flow
npm run test:e2e -- --testNamePattern="microsoft-oauth"
```

### Level 3: Custom OAuth Provider
```bash
# Test Auth0 custom provider
npm run test:e2e -- --testNamePattern="auth0-oauth"

# Test Okta custom provider
npm run test:e2e -- --testNamePattern="okta-oauth"

# Test enterprise custom provider
npm run test:e2e -- --testNamePattern="enterprise-oauth"
```

### Level 4: Multi-Tenant OAuth
```bash
# Test tenant-aware OAuth flows
npm run test:e2e -- --testNamePattern="tenant-oauth"

# Test tenant isolation
npm run test:e2e -- --testNamePattern="tenant-isolation"

# Test tenant-specific settings
npm run test:e2e -- --testNamePattern="tenant-settings"
```

### Level 5: Security and Compliance
```bash
# Test PKCE implementation
npm run test:e2e -- --testNamePattern="pkce"

# Test CSRF protection
npm run test:e2e -- --testNamePattern="csrf"

# Test security audit
npm run security:audit
```

---

## Quality Standards

The PRP must include:
- [x] Multiple social providers configured and tested
- [x] Custom OAuth provider support via genericOAuth plugin
- [x] PKCE implementation for enhanced security
- [x] Multi-tenant OAuth flows with proper isolation
- [x] Enterprise security compliance features
- [x] Comprehensive error handling and logging
- [x] OAuth token management and refresh capabilities
- [x] Integration with existing authentication system
- [x] Development and testing environment setup
- [x] Documentation and compliance requirements

---

## Expected Outcomes

Upon successful implementation:

1. **Multi-Provider Support**: 5+ social providers configured and working
2. **Custom OAuth**: Support for unlimited custom OAuth providers
3. **Security**: PKCE, CSRF protection, and secure token handling
4. **Multi-Tenant**: Tenant-aware OAuth flows with proper isolation
5. **Compliance**: Enterprise security and audit requirements met
6. **Performance**: Sub-second OAuth redirects and token exchanges
7. **Developer Experience**: Clear APIs and comprehensive documentation

---

**Framework**: NEXUS SaaS Starter Multi-Tenant Architecture  
**Technology Stack**: Next.js 15.4+ / better-auth / OAuth 2.0  
**Optimization**: Production-ready, enterprise-grade OAuth integration
