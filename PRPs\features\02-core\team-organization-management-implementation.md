# Team & Organization Management - Implementation PRP

**Generated by**: Nexus-Master Agent 🧙‍♂️  
**Date**: July 18, 2025  
**Status**: Implementation Ready  
**Sprint**: 9-10 (Core Features Phase)  
**Estimated Complexity**: High (Advanced hierarchical system)

---

## 🎯 **FEATURE OVERVIEW**

Implement comprehensive team and organization management system building on the existing Better Auth foundation with hierarchical team structures, advanced permissions, and multi-tenant capabilities.

### **Key Capabilities**
- **Multi-level Organization Structure**: Organizations → Teams → Members
- **Hierarchical Permissions**: Organization-level and team-level role management
- **Dynamic Team Creation**: Create unlimited teams within organizations
- **Advanced Member Management**: Assign members to multiple teams with different roles
- **Team-specific Resources**: Isolate resources and permissions by team
- **Organization Analytics**: Track team performance and member engagement

---

## 📋 **IMPLEMENTATION TASKS**

### **Phase 1: Database Schema & Models**
1. **Extend Organization Schema**
   - Add team hierarchy fields
   - Implement team limits and quotas
   - Add team metadata and settings

2. **Create Team Models**
   - Team entity with organization relationships
   - Team member mapping with roles
   - Team settings and configurations

3. **Update Member Models**
   - Multi-team membership support
   - Team-specific roles and permissions
   - Member activity tracking

### **Phase 2: Server-Side Implementation**
1. **Better Auth Configuration**
   - Enable teams feature with advanced settings
   - Configure team limits and permissions
   - Set up team-specific access control

2. **Team Management APIs**
   - Create, update, delete teams
   - Add/remove team members
   - Team settings management

3. **Organization Management APIs**
   - Enhanced organization CRUD operations
   - Team-aware organization operations
   - Bulk team operations

### **Phase 3: Client-Side Implementation**
1. **Team Management Components**
   - Team creation and editing forms
   - Team member management interface
   - Team settings configuration

2. **Organization Dashboard**
   - Multi-team organization overview
   - Team performance metrics
   - Member allocation across teams

3. **Member Management Interface**
   - Multi-team member assignment
   - Role management per team
   - Member activity tracking

### **Phase 4: Advanced Features**
1. **Team Templates**
   - Predefined team structures
   - Role templates for common use cases
   - Team onboarding workflows

2. **Team Analytics**
   - Team performance metrics
   - Member engagement tracking
   - Resource usage analytics

3. **Team Communication**
   - Team-specific notifications
   - Team activity feeds
   - Team collaboration tools

---

## 🛠 **TECHNICAL IMPLEMENTATION**

### **1. Database Schema Extensions**

```sql
-- Extend Better Auth organization table
ALTER TABLE organization ADD COLUMN IF NOT EXISTS team_limit INTEGER DEFAULT 10;
ALTER TABLE organization ADD COLUMN IF NOT EXISTS team_settings JSONB DEFAULT '{}';
ALTER TABLE organization ADD COLUMN IF NOT EXISTS team_templates JSONB DEFAULT '[]';

-- Better Auth team table (auto-created when teams enabled)
-- team table will be created automatically with:
-- id, name, organizationId, createdAt, updatedAt

-- Extend member table for team-specific roles
ALTER TABLE member ADD COLUMN IF NOT EXISTS team_roles JSONB DEFAULT '{}';
ALTER TABLE member ADD COLUMN IF NOT EXISTS team_permissions JSONB DEFAULT '{}';

-- Create team_members junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS team_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id UUID NOT NULL REFERENCES team(id) ON DELETE CASCADE,
    member_id UUID NOT NULL REFERENCES member(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL DEFAULT 'member',
    permissions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(team_id, member_id)
);

-- Create team_settings table for team-specific configurations
CREATE TABLE IF NOT EXISTS team_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id UUID NOT NULL REFERENCES team(id) ON DELETE CASCADE,
    settings JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(team_id)
);

-- Create team_invitations table for team-specific invitations
CREATE TABLE IF NOT EXISTS team_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id UUID NOT NULL REFERENCES team(id) ON DELETE CASCADE,
    invitation_id UUID NOT NULL REFERENCES invitation(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL DEFAULT 'member',
    permissions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(team_id, invitation_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_team_members_team_id ON team_members(team_id);
CREATE INDEX IF NOT EXISTS idx_team_members_member_id ON team_members(member_id);
CREATE INDEX IF NOT EXISTS idx_team_settings_team_id ON team_settings(team_id);
CREATE INDEX IF NOT EXISTS idx_team_invitations_team_id ON team_invitations(team_id);
```

### **2. Better Auth Configuration**

```typescript
// lib/auth.ts
import { betterAuth } from "better-auth"
import { organization } from "better-auth/plugins"
import { createAccessControl } from "better-auth/plugins/access"

// Define access control for teams
const statement = {
  organization: ["create", "update", "delete", "view"],
  team: ["create", "update", "delete", "view", "join", "leave"],
  member: ["create", "update", "delete", "view", "invite", "remove"],
  project: ["create", "update", "delete", "view", "share"],
  resource: ["create", "update", "delete", "view", "manage"]
} as const

const ac = createAccessControl(statement)

// Define roles with team-specific permissions
const owner = ac.newRole({
  organization: ["create", "update", "delete", "view"],
  team: ["create", "update", "delete", "view", "join", "leave"],
  member: ["create", "update", "delete", "view", "invite", "remove"],
  project: ["create", "update", "delete", "view", "share"],
  resource: ["create", "update", "delete", "view", "manage"]
})

const admin = ac.newRole({
  organization: ["update", "view"],
  team: ["create", "update", "delete", "view", "join", "leave"],
  member: ["create", "update", "view", "invite", "remove"],
  project: ["create", "update", "delete", "view", "share"],
  resource: ["create", "update", "delete", "view", "manage"]
})

const teamLead = ac.newRole({
  team: ["update", "view", "join", "leave"],
  member: ["view", "invite"],
  project: ["create", "update", "delete", "view", "share"],
  resource: ["create", "update", "view", "manage"]
})

const member = ac.newRole({
  team: ["view", "join", "leave"],
  member: ["view"],
  project: ["create", "update", "view"],
  resource: ["create", "update", "view"]
})

export const auth = betterAuth({
  database: {
    provider: "postgresql",
    url: process.env.DATABASE_URL!
  },
  plugins: [
    organization({
      ac,
      roles: {
        owner,
        admin,
        teamLead,
        member
      },
      teams: {
        enabled: true,
        maximumTeams: async ({ organizationId, session }) => {
          // Dynamic team limits based on organization plan
          const org = await db.organization.findUnique({
            where: { id: organizationId },
            select: { metadata: true }
          })
          
          const plan = org?.metadata?.plan || 'free'
          return plan === 'enterprise' ? 50 : plan === 'pro' ? 20 : 5
        },
        maximumMembersPerTeam: async ({ teamId, organizationId }) => {
          // Dynamic member limits based on team and organization settings
          const org = await db.organization.findUnique({
            where: { id: organizationId },
            select: { metadata: true }
          })
          
          const plan = org?.metadata?.plan || 'free'
          return plan === 'enterprise' ? 100 : plan === 'pro' ? 50 : 10
        },
        allowRemovingAllTeams: false // Prevent removing the last team
      },
      organizationCreation: {
        beforeCreate: async ({ organization, user }) => {
          // Auto-create default team when organization is created
          return {
            data: {
              ...organization,
              metadata: {
                ...organization.metadata,
                defaultTeamName: `${organization.name} Team`
              }
            }
          }
        },
        afterCreate: async ({ organization, member }) => {
          // Create default team for new organization
          await createDefaultTeam(organization.id, member.userId)
        }
      }
    })
  ]
})

async function createDefaultTeam(organizationId: string, userId: string) {
  // This will be implemented in the team service
  const teamService = await import('../services/team-service')
  await teamService.createDefaultTeam(organizationId, userId)
}
```

### **3. Team Service Implementation**

```typescript
// lib/services/team-service.ts
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"

export interface Team {
  id: string
  name: string
  organizationId: string
  settings: Record<string, any>
  memberCount: number
  createdAt: Date
  updatedAt: Date
}

export interface TeamMember {
  id: string
  teamId: string
  memberId: string
  role: string
  permissions: Record<string, any>
  user: {
    id: string
    name: string
    email: string
    image?: string
  }
  createdAt: Date
  updatedAt: Date
}

export class TeamService {
  async createTeam(organizationId: string, data: {
    name: string
    description?: string
    settings?: Record<string, any>
  }, userId: string): Promise<Team> {
    // Verify user has permission to create teams
    const canCreate = await auth.api.hasPermission({
      permissions: { team: ["create"] },
      organizationId,
      userId
    })
    
    if (!canCreate) {
      throw new Error("Insufficient permissions to create team")
    }
    
    // Check team limits
    const teamCount = await db.team.count({
      where: { organizationId }
    })
    
    const maxTeams = await this.getTeamLimit(organizationId)
    if (teamCount >= maxTeams) {
      throw new Error(`Team limit reached (${maxTeams})`)
    }
    
    // Create team using Better Auth
    const team = await auth.api.createTeam({
      name: data.name,
      organizationId
    })
    
    // Create team settings
    await db.teamSettings.create({
      data: {
        teamId: team.id,
        settings: {
          description: data.description,
          ...data.settings
        }
      }
    })
    
    return {
      id: team.id,
      name: team.name,
      organizationId: team.organizationId,
      settings: data.settings || {},
      memberCount: 0,
      createdAt: team.createdAt,
      updatedAt: team.updatedAt
    }
  }
  
  async addTeamMember(teamId: string, memberId: string, data: {
    role: string
    permissions?: Record<string, any>
  }, userId: string): Promise<TeamMember> {
    // Verify user has permission to add members
    const team = await db.team.findUnique({
      where: { id: teamId },
      select: { organizationId: true }
    })
    
    if (!team) {
      throw new Error("Team not found")
    }
    
    const canAddMember = await auth.api.hasPermission({
      permissions: { member: ["create"] },
      organizationId: team.organizationId,
      userId
    })
    
    if (!canAddMember) {
      throw new Error("Insufficient permissions to add team member")
    }
    
    // Check member limits
    const memberCount = await db.teamMembers.count({
      where: { teamId }
    })
    
    const maxMembers = await this.getTeamMemberLimit(teamId)
    if (memberCount >= maxMembers) {
      throw new Error(`Team member limit reached (${maxMembers})`)
    }
    
    // Add member to team
    const teamMember = await db.teamMembers.create({
      data: {
        teamId,
        memberId,
        role: data.role,
        permissions: data.permissions || {}
      },
      include: {
        member: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        }
      }
    })
    
    return {
      id: teamMember.id,
      teamId: teamMember.teamId,
      memberId: teamMember.memberId,
      role: teamMember.role,
      permissions: teamMember.permissions as Record<string, any>,
      user: teamMember.member.user,
      createdAt: teamMember.createdAt,
      updatedAt: teamMember.updatedAt
    }
  }
  
  async getTeamMembers(teamId: string, userId: string): Promise<TeamMember[]> {
    // Verify user has permission to view team members
    const team = await db.team.findUnique({
      where: { id: teamId },
      select: { organizationId: true }
    })
    
    if (!team) {
      throw new Error("Team not found")
    }
    
    const canView = await auth.api.hasPermission({
      permissions: { member: ["view"] },
      organizationId: team.organizationId,
      userId
    })
    
    if (!canView) {
      throw new Error("Insufficient permissions to view team members")
    }
    
    const members = await db.teamMembers.findMany({
      where: { teamId },
      include: {
        member: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'asc' }
    })
    
    return members.map(tm => ({
      id: tm.id,
      teamId: tm.teamId,
      memberId: tm.memberId,
      role: tm.role,
      permissions: tm.permissions as Record<string, any>,
      user: tm.member.user,
      createdAt: tm.createdAt,
      updatedAt: tm.updatedAt
    }))
  }
  
  async createDefaultTeam(organizationId: string, userId: string): Promise<Team> {
    const org = await db.organization.findUnique({
      where: { id: organizationId },
      select: { name: true, metadata: true }
    })
    
    if (!org) {
      throw new Error("Organization not found")
    }
    
    const defaultTeamName = (org.metadata as any)?.defaultTeamName || `${org.name} Team`
    
    return this.createTeam(organizationId, {
      name: defaultTeamName,
      description: "Default team for organization",
      settings: {
        isDefault: true,
        autoAssignNewMembers: true
      }
    }, userId)
  }
  
  private async getTeamLimit(organizationId: string): Promise<number> {
    const org = await db.organization.findUnique({
      where: { id: organizationId },
      select: { metadata: true }
    })
    
    const plan = (org?.metadata as any)?.plan || 'free'
    return plan === 'enterprise' ? 50 : plan === 'pro' ? 20 : 5
  }
  
  private async getTeamMemberLimit(teamId: string): Promise<number> {
    const team = await db.team.findUnique({
      where: { id: teamId },
      include: {
        organization: {
          select: { metadata: true }
        }
      }
    })
    
    if (!team) return 10
    
    const plan = (team.organization.metadata as any)?.plan || 'free'
    return plan === 'enterprise' ? 100 : plan === 'pro' ? 50 : 10
  }
}

export const teamService = new TeamService()
```

### **4. Team Management Components**

```typescript
// components/team/team-management.tsx
"use client"

import { useState, useEffect } from "react"
import { authClient } from "@/lib/auth-client"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Plus, Settings, Users, MoreHorizontal } from "lucide-react"
import { CreateTeamDialog } from "./create-team-dialog"
import { TeamMembersList } from "./team-members-list"
import { TeamSettingsDialog } from "./team-settings-dialog"

interface Team {
  id: string
  name: string
  organizationId: string
  memberCount: number
  settings: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export function TeamManagement() {
  const [teams, setTeams] = useState<Team[]>([])
  const [loading, setLoading] = useState(true)
  const [createTeamOpen, setCreateTeamOpen] = useState(false)
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null)
  const [settingsOpen, setSettingsOpen] = useState(false)

  const { data: activeOrg } = authClient.useActiveOrganization()

  useEffect(() => {
    if (activeOrg) {
      loadTeams()
    }
  }, [activeOrg])

  const loadTeams = async () => {
    try {
      setLoading(true)
      const response = await authClient.organization.listTeams({
        query: { organizationId: activeOrg?.id }
      })
      setTeams(response.data || [])
    } catch (error) {
      console.error("Failed to load teams:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTeam = async (teamData: { name: string; description?: string }) => {
    try {
      await authClient.organization.createTeam({
        name: teamData.name,
        organizationId: activeOrg?.id
      })
      await loadTeams()
      setCreateTeamOpen(false)
    } catch (error) {
      console.error("Failed to create team:", error)
    }
  }

  const handleDeleteTeam = async (teamId: string) => {
    if (!confirm("Are you sure you want to delete this team?")) return

    try {
      await authClient.organization.removeTeam({
        teamId,
        organizationId: activeOrg?.id
      })
      await loadTeams()
    } catch (error) {
      console.error("Failed to delete team:", error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Team Management</h2>
          <p className="text-muted-foreground">
            Manage teams and their members in {activeOrg?.name}
          </p>
        </div>
        <Button onClick={() => setCreateTeamOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Team
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {teams.map((team) => (
          <Card key={team.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{team.name}</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSelectedTeam(team)
                    setSettingsOpen(true)
                  }}
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
              <CardDescription>
                {team.settings?.description || "No description"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    {team.memberCount} members
                  </span>
                </div>
                <Badge variant={team.settings?.isDefault ? "default" : "secondary"}>
                  {team.settings?.isDefault ? "Default" : "Custom"}
                </Badge>
              </div>
              <div className="mt-4 space-y-2">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => setSelectedTeam(team)}
                >
                  <Users className="h-4 w-4 mr-2" />
                  Manage Members
                </Button>
                <Button
                  variant="ghost"
                  className="w-full text-destructive"
                  onClick={() => handleDeleteTeam(team.id)}
                  disabled={team.settings?.isDefault}
                >
                  Delete Team
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <CreateTeamDialog
        open={createTeamOpen}
        onOpenChange={setCreateTeamOpen}
        onCreateTeam={handleCreateTeam}
      />

      {selectedTeam && (
        <>
          <TeamMembersList
            team={selectedTeam}
            open={!!selectedTeam && !settingsOpen}
            onOpenChange={(open) => !open && setSelectedTeam(null)}
            onMembersChanged={loadTeams}
          />
          <TeamSettingsDialog
            team={selectedTeam}
            open={settingsOpen}
            onOpenChange={setSettingsOpen}
            onTeamUpdated={loadTeams}
          />
        </>
      )}
    </div>
  )
}
```

### **5. Team Creation Dialog**

```typescript
// components/team/create-team-dialog.tsx
"use client"

import { useState } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

interface CreateTeamDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateTeam: (teamData: { name: string; description?: string }) => Promise<void>
}

export function CreateTeamDialog({ open, onOpenChange, onCreateTeam }: CreateTeamDialogProps) {
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!name.trim()) return

    try {
      setLoading(true)
      await onCreateTeam({
        name: name.trim(),
        description: description.trim() || undefined
      })
      setName("")
      setDescription("")
    } catch (error) {
      console.error("Failed to create team:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create New Team</DialogTitle>
            <DialogDescription>
              Create a new team to organize your members and projects.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="team-name">Team Name</Label>
              <Input
                id="team-name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter team name"
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="team-description">Description (Optional)</Label>
              <Textarea
                id="team-description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Enter team description"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading || !name.trim()}>
              {loading ? "Creating..." : "Create Team"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
```

### **6. Team Members List Component**

```typescript
// components/team/team-members-list.tsx
"use client"

import { useState, useEffect } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Plus, UserMinus, Mail, Search } from "lucide-react"
import { authClient } from "@/lib/auth-client"

interface TeamMember {
  id: string
  teamId: string
  memberId: string
  role: string
  permissions: Record<string, any>
  user: {
    id: string
    name: string
    email: string
    image?: string
  }
  createdAt: Date
  updatedAt: Date
}

interface Team {
  id: string
  name: string
  organizationId: string
  memberCount: number
  settings: Record<string, any>
}

interface TeamMembersListProps {
  team: Team
  open: boolean
  onOpenChange: (open: boolean) => void
  onMembersChanged: () => void
}

export function TeamMembersList({ team, open, onOpenChange, onMembersChanged }: TeamMembersListProps) {
  const [members, setMembers] = useState<TeamMember[]>([])
  const [availableMembers, setAvailableMembers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [inviteEmail, setInviteEmail] = useState("")
  const [inviteRole, setInviteRole] = useState("member")

  useEffect(() => {
    if (open && team) {
      loadTeamMembers()
      loadAvailableMembers()
    }
  }, [open, team])

  const loadTeamMembers = async () => {
    try {
      setLoading(true)
      // Custom API call to get team members
      const response = await fetch(`/api/teams/${team.id}/members`, {
        headers: {
          'Authorization': `Bearer ${await authClient.getSession()?.then(s => s.token)}`
        }
      })
      const data = await response.json()
      setMembers(data.members || [])
    } catch (error) {
      console.error("Failed to load team members:", error)
    } finally {
      setLoading(false)
    }
  }

  const loadAvailableMembers = async () => {
    try {
      const response = await authClient.organization.listMembers({
        query: { organizationId: team.organizationId }
      })
      setAvailableMembers(response.data || [])
    } catch (error) {
      console.error("Failed to load available members:", error)
    }
  }

  const handleAddMember = async (memberId: string) => {
    try {
      await fetch(`/api/teams/${team.id}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await authClient.getSession()?.then(s => s.token)}`
        },
        body: JSON.stringify({
          memberId,
          role: 'member'
        })
      })
      await loadTeamMembers()
      onMembersChanged()
    } catch (error) {
      console.error("Failed to add member:", error)
    }
  }

  const handleRemoveMember = async (memberId: string) => {
    if (!confirm("Are you sure you want to remove this member from the team?")) return

    try {
      await fetch(`/api/teams/${team.id}/members/${memberId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${await authClient.getSession()?.then(s => s.token)}`
        }
      })
      await loadTeamMembers()
      onMembersChanged()
    } catch (error) {
      console.error("Failed to remove member:", error)
    }
  }

  const handleUpdateRole = async (memberId: string, newRole: string) => {
    try {
      await fetch(`/api/teams/${team.id}/members/${memberId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await authClient.getSession()?.then(s => s.token)}`
        },
        body: JSON.stringify({ role: newRole })
      })
      await loadTeamMembers()
    } catch (error) {
      console.error("Failed to update member role:", error)
    }
  }

  const handleInviteByEmail = async () => {
    if (!inviteEmail.trim()) return

    try {
      await authClient.organization.inviteMember({
        email: inviteEmail.trim(),
        role: inviteRole,
        teamId: team.id
      })
      setInviteEmail("")
      setInviteRole("member")
      // Refresh members list after invitation
      setTimeout(() => loadTeamMembers(), 1000)
    } catch (error) {
      console.error("Failed to invite member:", error)
    }
  }

  const filteredMembers = members.filter(member =>
    member.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.user.email.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const membersNotInTeam = availableMembers.filter(member =>
    !members.some(tm => tm.memberId === member.id)
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Team Members - {team.name}</DialogTitle>
          <DialogDescription>
            Manage members for this team ({members.length} members)
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search members..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Current Members */}
          <div className="space-y-3">
            <h3 className="font-semibold">Current Members</h3>
            {loading ? (
              <div className="flex items-center justify-center h-20">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : filteredMembers.length === 0 ? (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-center text-muted-foreground">
                    No members found
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-2">
                {filteredMembers.map((member) => (
                  <Card key={member.id}>
                    <CardContent className="pt-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={member.user.image} />
                            <AvatarFallback>
                              {member.user.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{member.user.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {member.user.email}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Select
                            value={member.role}
                            onValueChange={(value) => handleUpdateRole(member.memberId, value)}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="member">Member</SelectItem>
                              <SelectItem value="teamLead">Team Lead</SelectItem>
                              <SelectItem value="admin">Admin</SelectItem>
                            </SelectContent>
                          </Select>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveMember(member.memberId)}
                          >
                            <UserMinus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Add Members */}
          <div className="space-y-3">
            <h3 className="font-semibold">Add Members</h3>
            
            {/* Add from Organization */}
            {membersNotInTeam.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Organization Members</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {membersNotInTeam.slice(0, 5).map((member) => (
                      <div key={member.id} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={member.user.image} />
                            <AvatarFallback>
                              {member.user.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-sm">{member.user.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {member.user.email}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAddMember(member.id)}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Invite by Email */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Invite New Member</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex space-x-2">
                  <Input
                    placeholder="Enter email address"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    className="flex-1"
                  />
                  <Select value={inviteRole} onValueChange={setInviteRole}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="member">Member</SelectItem>
                      <SelectItem value="teamLead">Team Lead</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button onClick={handleInviteByEmail} disabled={!inviteEmail.trim()}>
                    <Mail className="h-4 w-4 mr-1" />
                    Invite
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
```

### **7. API Routes**

```typescript
// app/api/teams/[teamId]/members/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { teamService } from '@/lib/services/team-service'

export async function GET(
  request: NextRequest,
  { params }: { params: { teamId: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers
    })

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const members = await teamService.getTeamMembers(params.teamId, session.user.id)
    
    return NextResponse.json({ members })
  } catch (error) {
    console.error('Failed to get team members:', error)
    return NextResponse.json(
      { error: 'Failed to get team members' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { teamId: string } }
) {
  try {
    const session = await auth.api.getSession({
      headers: request.headers
    })

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { memberId, role, permissions } = await request.json()

    const teamMember = await teamService.addTeamMember(
      params.teamId,
      memberId,
      { role, permissions },
      session.user.id
    )
    
    return NextResponse.json({ teamMember })
  } catch (error) {
    console.error('Failed to add team member:', error)
    return NextResponse.json(
      { error: 'Failed to add team member' },
      { status: 500 }
    )
  }
}
```

### **8. Main Dashboard Integration**

```typescript
// app/(dashboard)/teams/page.tsx
import { auth } from "@/lib/auth"
import { redirect } from "next/navigation"
import { TeamManagement } from "@/components/team/team-management"

export default async function TeamsPage() {
  const session = await auth.api.getSession()
  
  if (!session) {
    redirect("/auth/signin")
  }

  return (
    <div className="container mx-auto py-8">
      <TeamManagement />
    </div>
  )
}
```

---

## 🧪 **TESTING REQUIREMENTS**

### **Unit Tests**
- TeamService methods (create, add member, permissions)
- Better Auth configuration and role permissions
- Database operations and constraints

### **Integration Tests**
- Team creation and member management workflows
- Organization-team relationship integrity
- Permission system across teams

### **E2E Tests**
- Complete team management user journey
- Multi-team member assignment
- Role-based access control validation

---

## 📊 **SUCCESS METRICS**

### **Technical Metrics**
- **Team Creation Time**: < 2 seconds
- **Member Addition**: < 1 second per member
- **Permission Checks**: < 100ms response time
- **Database Queries**: Optimized with proper indexing

### **User Experience Metrics**
- **Team Setup Completion**: > 90% success rate
- **Member Invitation Acceptance**: > 80% within 24 hours
- **Team Management Usage**: > 75% of organizations use teams
- **User Satisfaction**: > 4.5/5 rating

### **Business Metrics**
- **Team Adoption**: 85% of organizations create multiple teams
- **Member Engagement**: 70% increase in collaboration
- **Permission Compliance**: 100% proper access control
- **Support Tickets**: < 5% related to team management

---

## 🚀 **DEPLOYMENT STEPS**

### **Database Migration**
1. Run schema migrations for team tables
2. Update existing organization data
3. Create default teams for existing organizations

### **Feature Flag Rollout**
1. Enable teams feature for beta organizations
2. Monitor performance and user feedback
3. Gradual rollout to all organizations

### **Monitoring Setup**
1. Track team creation and member addition metrics
2. Monitor permission check performance
3. Set up alerts for failed operations

---

## 🔄 **FUTURE ENHANCEMENTS**

### **Phase 2 Features**
- **Team Templates**: Pre-configured team structures
- **Team Analytics**: Performance and engagement metrics
- **Team Workflows**: Automated team processes

### **Phase 3 Features**
- **Cross-Team Collaboration**: Project sharing between teams
- **Team Hierarchies**: Sub-teams and parent-child relationships
- **Advanced Permissions**: Custom permission sets per team

### **Phase 4 Features**
- **Team AI Assistant**: Automated team management
- **Team Insights**: ML-powered team optimization
- **Team Marketplace**: Shared team templates and tools

---

## 📋 **COMPLETION CHECKLIST**

### **Backend Implementation**
- [ ] Database schema extensions
- [ ] Better Auth configuration with teams
- [ ] TeamService implementation
- [ ] API routes for team management
- [ ] Permission system setup

### **Frontend Implementation**
- [ ] Team management dashboard
- [ ] Team creation and editing forms
- [ ] Member management interface
- [ ] Permission-based UI components
- [ ] Team settings configuration

### **Testing & Quality**
- [ ] Unit tests for all services
- [ ] Integration tests for workflows
- [ ] E2E tests for user journeys
- [ ] Performance testing
- [ ] Security testing

### **Documentation & Deployment**
- [ ] API documentation updates
- [ ] User guide for team management
- [ ] Admin documentation
- [ ] Deployment scripts
- [ ] Monitoring setup

---

**Implementation Priority**: **HIGH** (Core user management feature)  
**Estimated Timeline**: 5-7 days  
**Team Size**: 2-3 developers  
**Dependencies**: Better Auth organization plugin, existing user management

---

*This PRP provides a complete implementation plan for hierarchical team and organization management, building on Better Auth's powerful organization capabilities while adding advanced team-specific features and multi-tenant support.*

**Built with ❤️ by Nexus-Master Agent**  
*Where 125 Senior Developers Meet AI Excellence*
