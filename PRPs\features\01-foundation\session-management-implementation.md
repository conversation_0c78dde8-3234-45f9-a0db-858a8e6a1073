# NEXUS SaaS Starter - Session Management Implementation

**PRP Name**: Session Management  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Foundation Implementation PRP  
**Phase**: 01-foundation  
**Framework**: Next.js 15.4+ / better-auth / Session Security  

---

## Purpose

Implement comprehensive session management using better-auth with secure session handling, automatic expiration, refresh mechanisms, multi-device support, and enterprise-grade security features for multi-tenant SaaS applications.

## Core Principles

1. **Security First**: Secure session tokens, automatic expiration, and tamper protection
2. **Performance Optimized**: Cookie caching, efficient session validation, and minimal database calls
3. **Multi-Device Support**: Session management across multiple devices and browsers
4. **Enterprise Ready**: Session monitoring, revocation, and compliance features
5. **User Experience**: Seamless authentication flows and session persistence
6. **Multi-Tenant Isolation**: Tenant-aware session management with proper boundaries

---

## Goal

Build a production-ready session management system that provides secure, scalable, and user-friendly session handling with automatic expiration, refresh mechanisms, multi-device support, and comprehensive security features.

## Why

- **Security**: Prevents session hijacking and unauthorized access
- **Performance**: Reduces authentication overhead and improves response times
- **User Experience**: Seamless authentication across devices and sessions
- **Compliance**: Meets enterprise security and audit requirements
- **Scalability**: Handles high-volume session management efficiently
- **Maintainability**: Clear session lifecycle management and monitoring

## What

A comprehensive session management system with:
- Secure session token generation and validation
- Automatic session expiration and refresh mechanisms
- Multi-device session management and synchronization
- Session revocation and security controls
- Cookie caching for performance optimization
- Comprehensive session monitoring and audit logging

### Success Criteria

- [ ] Secure session token generation and validation
- [ ] Automatic session expiration and refresh mechanisms
- [ ] Multi-device session management and synchronization
- [ ] Session revocation and security controls
- [ ] Cookie caching for performance optimization
- [ ] Session monitoring and audit logging
- [ ] Multi-tenant session isolation
- [ ] Enterprise security compliance
- [ ] Performance optimization and scalability
- [ ] Comprehensive session recovery mechanisms

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://docs.better-auth.com/concepts/session-management
  why: Better-auth session management comprehensive documentation
  critical: Understanding session lifecycle and security patterns

- url: https://datatracker.ietf.org/doc/html/rfc6265
  why: HTTP State Management Mechanism (Cookies) specification
  critical: Cookie security and session management standards

- url: https://owasp.org/www-project-cheat-sheets/cheatsheets/Session_Management_Cheat_Sheet.html
  why: OWASP Session Management Security best practices
  critical: Security implementation guidelines and threat mitigation

- url: https://docs.better-auth.com/plugins/multi-session
  why: Multi-session management with better-auth
  critical: Multi-device session handling and synchronization

- url: https://docs.better-auth.com/concepts/client
  why: Better-auth client session management
  critical: Client-side session handling and reactive patterns

- url: https://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-63B.pdf
  why: NIST Digital Identity Guidelines for Session Management
  critical: Session security standards and compliance requirements

- url: https://docs.better-auth.com/plugins/admin
  why: Administrative session management controls
  critical: Session monitoring and administrative controls

- url: https://docs.better-auth.com/concepts/hooks
  why: Session lifecycle hooks and middleware
  critical: Session event handling and custom logic

- url: https://docs.better-auth.com/guides/optimizing-for-performance
  why: Performance optimization for session management
  critical: Cookie caching and performance best practices

- url: https://docs.better-auth.com/reference/options
  why: Session configuration options and parameters
  critical: Session configuration and customization options
```

### Current Technology Stack

```yaml
# Session Management
- better-auth: Latest (comprehensive session management)
- session: better-auth session configuration
- multiSession: better-auth multi-device support
- cookieCache: Performance optimization
- customSession: Custom session data handling

# Security Features
- CSRF Protection: Cross-site request forgery prevention
- Session Tokens: Cryptographically secure tokens
- Automatic Expiration: Time-based session expiration
- Session Refresh: Intelligent session renewal
- Device Tracking: Multi-device session monitoring

# Performance Optimization
- Cookie Caching: Reduced database calls
- Session Validation: Efficient session checking
- Background Refresh: Non-blocking session updates
- Session Cleanup: Automatic expired session removal
- Connection Pooling: Optimized database connections

# Multi-Tenant Support
- Tenant Isolation: Session isolation by tenant
- Tenant Context: Tenant-aware session management
- Tenant Policies: Tenant-specific session settings
- Tenant Monitoring: Tenant-specific session analytics
```

### Known Gotchas & Library Quirks

```typescript
// CRITICAL: Session security gotchas
// Token storage: Session tokens must be httpOnly and secure
// CSRF protection: Must validate request origin and state
// Session hijacking: IP address and user agent validation
// Session fixation: Generate new tokens on authentication
// Session replay: Tokens must be cryptographically secure
// Clock skew: Time-based expiration must handle clock differences
// Race conditions: Concurrent requests may cause issues
// Cookie security: SameSite, Secure, and HttpOnly flags required
// Session cleanup: Expired sessions must be properly cleaned up
// Memory leaks: Session data must be properly disposed

// CRITICAL: Multi-device session gotchas
// Session synchronization: Changes must propagate across devices
// Device limits: Must enforce maximum sessions per user
// Session conflicts: Concurrent logins may cause conflicts
// Device fingerprinting: Must balance security with privacy
// Session migration: Device changes must be handled gracefully
// Background refresh: Sessions must refresh without interruption
// Network failures: Must handle offline/online transitions
// Session recovery: Must handle browser crashes and restarts
// Cross-browser: Sessions must work across different browsers
// Mobile apps: Native app session handling differences

// CRITICAL: Performance optimization gotchas
// Cookie size: Session data must fit in cookie size limits
// Cache invalidation: Cached sessions must be properly invalidated
// Database load: Session validation must not overwhelm database
// Memory usage: Session cache must not consume excessive memory
// Session storage: Must choose appropriate storage backend
// Cleanup frequency: Expired session cleanup must be optimized
// Connection pooling: Database connections must be properly managed
// Cache warming: Session cache must be warmed appropriately
// Load balancing: Sessions must work across multiple servers
// CDN caching: Session cookies must not be cached by CDN
```

---

## Implementation Blueprint

### Core Session Configuration

```typescript
// lib/auth.ts
import { betterAuth } from "better-auth";
import { multiSession, customSession } from "better-auth/plugins";
import { getTenantContext } from "./tenant-context";
import { auditLogger } from "./audit-logger";

export const auth = betterAuth({
  database: {
    provider: "postgres",
    url: process.env.DATABASE_URL!
  },
  
  // Session Configuration
  session: {
    // Session expiration (7 days)
    expiresIn: 60 * 60 * 24 * 7,
    
    // Session refresh interval (1 day)
    updateAge: 60 * 60 * 24,
    
    // Fresh session requirement (5 minutes for sensitive operations)
    freshAge: 60 * 5,
    
    // Cookie caching for performance
    cookieCache: {
      enabled: true,
      maxAge: 5 * 60, // 5 minutes
    },
    
    // Additional session fields
    additionalFields: {
      tenantId: {
        type: "string",
        required: true,
      },
      deviceId: {
        type: "string",
        required: false,
      },
      deviceType: {
        type: "string",
        required: false,
      },
      lastActivity: {
        type: "date",
        required: false,
      },
      ipAddress: {
        type: "string",
        required: false,
      },
      userAgent: {
        type: "string",
        required: false,
      },
    },
    
    // Store sessions in database for audit and monitoring
    storeSessionInDatabase: true,
    
    // Preserve session records for compliance
    preserveSessionInDatabase: true,
    
    // Custom session model name
    modelName: "user_sessions",
    
    // Field mappings for existing schemas
    fields: {
      userId: "user_id",
      expiresAt: "expires_at",
      createdAt: "created_at",
      updatedAt: "updated_at",
    },
  },
  
  plugins: [
    // Multi-device session support
    multiSession({
      maximumSessions: 5, // Maximum 5 active sessions per user
    }),
    
    // Custom session data injection
    customSession(async ({ user, session }, ctx) => {
      // Get tenant context for the session
      const tenantContext = await getTenantContext(session.tenantId);
      
      // Get user roles and permissions
      const userRoles = await getUserRoles(user.id, session.tenantId);
      
      // Log session access for audit
      await auditLogger.logSessionAccess({
        userId: user.id,
        sessionId: session.id,
        tenantId: session.tenantId,
        ipAddress: ctx.request.headers.get("x-forwarded-for") || "unknown",
        userAgent: ctx.request.headers.get("user-agent") || "unknown",
        timestamp: new Date().toISOString(),
      });
      
      return {
        user: {
          ...user,
          roles: userRoles,
          permissions: userRoles.flatMap(role => role.permissions),
        },
        session: {
          ...session,
          tenant: tenantContext,
          deviceInfo: {
            id: session.deviceId,
            type: session.deviceType,
            lastActivity: session.lastActivity,
          },
        },
      };
    }),
  ],
  
  // Enhanced security settings
  security: {
    csrfProtection: {
      enabled: true,
      sameSite: "strict",
    },
    sessionSecurity: {
      updateAge: 24 * 60 * 60, // 24 hours
      expiresIn: 30 * 24 * 60 * 60, // 30 days
    },
  },
  
  // Advanced configuration
  advanced: {
    generateId: () => crypto.randomUUID(),
    crossSubDomainCookies: {
      enabled: true,
      domain: process.env.COOKIE_DOMAIN,
    },
  },
});

// Session utility functions
export const sessionUtils = {
  // Get session with tenant context
  async getSessionWithTenant(headers: Headers) {
    const session = await auth.api.getSession({ headers });
    
    if (!session) {
      return null;
    }
    
    return session;
  },
  
  // Validate session freshness
  async validateSessionFreshness(sessionId: string) {
    const session = await auth.api.getSession({
      headers: new Headers({
        cookie: `session=${sessionId}`,
      }),
    });
    
    if (!session) {
      return false;
    }
    
    // Check if session is fresh (created within freshAge)
    const now = new Date();
    const sessionAge = now.getTime() - new Date(session.session.createdAt).getTime();
    const freshAge = 60 * 5 * 1000; // 5 minutes in milliseconds
    
    return sessionAge <= freshAge;
  },
  
  // Revoke all sessions for a user
  async revokeAllUserSessions(userId: string, currentSessionId?: string) {
    const sessions = await auth.api.listUserSessions({ userId });
    
    for (const session of sessions) {
      if (session.id !== currentSessionId) {
        await auth.api.revokeSession({ sessionId: session.id });
      }
    }
    
    return sessions.length - (currentSessionId ? 1 : 0);
  },
  
  // Clean up expired sessions
  async cleanupExpiredSessions() {
    const now = new Date();
    const expiredSessions = await auth.database.session.findMany({
      where: {
        expiresAt: {
          lt: now,
        },
      },
    });
    
    for (const session of expiredSessions) {
      await auth.database.session.delete({
        where: { id: session.id },
      });
    }
    
    return expiredSessions.length;
  },
};
```

### Session Client Configuration

```typescript
// lib/auth-client.ts
import { createAuthClient } from "better-auth/client";
import { multiSessionClient, customSessionClient } from "better-auth/client/plugins";
import type { auth } from "./auth";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_AUTH_URL || "http://localhost:3000",
  
  plugins: [
    multiSessionClient(),
    customSessionClient<typeof auth>(),
  ],
  
  // Session configuration
  session: {
    // Automatic session refresh
    refreshOnWindowFocus: true,
    refreshOnMount: true,
    
    // Session persistence
    persistSession: true,
    
    // Error handling
    onSessionError: (error) => {
      console.error("Session error:", error);
      // Redirect to login on session error
      if (typeof window !== "undefined") {
        window.location.href = "/auth/signin";
      }
    },
    
    // Session refresh callback
    onSessionRefresh: (session) => {
      console.log("Session refreshed:", session);
      // Update application state
      updateApplicationState(session);
    },
  },
});

// Session management service
export const sessionService = {
  // Get current session
  async getCurrentSession() {
    try {
      const { data: session, error } = await authClient.getSession();
      
      if (error) {
        throw new Error(`Session error: ${error.message}`);
      }
      
      return session;
    } catch (error) {
      console.error("Failed to get session:", error);
      return null;
    }
  },
  
  // Refresh session
  async refreshSession() {
    try {
      const { data: session, error } = await authClient.getSession({
        query: { disableCookieCache: true },
      });
      
      if (error) {
        throw new Error(`Session refresh error: ${error.message}`);
      }
      
      return session;
    } catch (error) {
      console.error("Failed to refresh session:", error);
      return null;
    }
  },
  
  // List all user sessions
  async listUserSessions() {
    try {
      const sessions = await authClient.listSessions();
      return sessions;
    } catch (error) {
      console.error("Failed to list sessions:", error);
      return [];
    }
  },
  
  // List device sessions (multi-session)
  async listDeviceSessions() {
    try {
      const sessions = await authClient.multiSession.listDeviceSessions();
      return sessions;
    } catch (error) {
      console.error("Failed to list device sessions:", error);
      return [];
    }
  },
  
  // Set active session
  async setActiveSession(sessionToken: string) {
    try {
      await authClient.multiSession.setActive({ sessionToken });
      return true;
    } catch (error) {
      console.error("Failed to set active session:", error);
      return false;
    }
  },
  
  // Revoke specific session
  async revokeSession(sessionToken: string) {
    try {
      await authClient.revokeSession({ token: sessionToken });
      return true;
    } catch (error) {
      console.error("Failed to revoke session:", error);
      return false;
    }
  },
  
  // Revoke all other sessions
  async revokeOtherSessions() {
    try {
      await authClient.revokeOtherSessions();
      return true;
    } catch (error) {
      console.error("Failed to revoke other sessions:", error);
      return false;
    }
  },
  
  // Revoke all sessions (complete logout)
  async revokeAllSessions() {
    try {
      await authClient.revokeSessions();
      return true;
    } catch (error) {
      console.error("Failed to revoke all sessions:", error);
      return false;
    }
  },
  
  // Change password and revoke other sessions
  async changePasswordAndRevokeOtherSessions(
    newPassword: string,
    currentPassword: string
  ) {
    try {
      await authClient.changePassword({
        newPassword,
        currentPassword,
        revokeOtherSessions: true,
      });
      return true;
    } catch (error) {
      console.error("Failed to change password:", error);
      return false;
    }
  },
  
  // Session validation
  async validateSession(sessionToken?: string) {
    try {
      const session = await authClient.getSession({
        query: sessionToken ? { sessionToken } : {},
      });
      
      return session.data !== null;
    } catch (error) {
      console.error("Session validation failed:", error);
      return false;
    }
  },
  
  // Device fingerprinting
  generateDeviceFingerprint() {
    if (typeof window === "undefined") return null;
    
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    ctx?.fillText("Device fingerprint", 10, 50);
    
    const fingerprint = {
      userAgent: navigator.userAgent,
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      platform: navigator.platform,
      canvas: canvas.toDataURL(),
      timestamp: Date.now(),
    };
    
    return btoa(JSON.stringify(fingerprint));
  },
};

// Application state management
function updateApplicationState(session: any) {
  // Update global state, Redux store, or context
  // This is application-specific implementation
  console.log("Updating application state with session:", session);
}
```

### Task Breakdown

```yaml
Task 1: Core Session Infrastructure
SETUP session management configuration:
  - CONFIGURE session expiration and refresh settings
  - SETUP cookie caching for performance optimization
  - CONFIGURE session security settings
  - SETUP database session storage
  - INTEGRATE audit logging for session events

Task 2: Multi-Device Session Support
IMPLEMENT multi-device session management:
  - SETUP multiSession plugin configuration
  - CONFIGURE maximum sessions per user
  - IMPLEMENT device session synchronization
  - SETUP session conflict resolution
  - CONFIGURE device fingerprinting

Task 3: Session Security Implementation
IMPLEMENT session security features:
  - SETUP CSRF protection and validation
  - CONFIGURE secure session token generation
  - IMPLEMENT session hijacking prevention
  - SETUP IP address and user agent validation
  - CONFIGURE session fixation protection

Task 4: Performance Optimization
IMPLEMENT session performance features:
  - SETUP cookie caching for reduced database calls
  - CONFIGURE session validation optimization
  - IMPLEMENT background session refresh
  - SETUP session cleanup automation
  - CONFIGURE connection pooling

Task 5: Multi-Tenant Integration
IMPLEMENT tenant-aware session management:
  - SETUP tenant context in session data
  - CONFIGURE tenant-specific session policies
  - IMPLEMENT tenant session isolation
  - SETUP tenant-specific session monitoring
  - CONFIGURE tenant session analytics

Task 6: Administrative Controls
IMPLEMENT session management controls:
  - SETUP session monitoring and analytics
  - CONFIGURE administrative session revocation
  - IMPLEMENT session impersonation for support
  - SETUP session audit trails
  - CONFIGURE session compliance reporting

Task 7: Client-Side Session Management
IMPLEMENT client session handling:
  - SETUP reactive session state management
  - CONFIGURE automatic session refresh
  - IMPLEMENT session error handling
  - SETUP session persistence across tabs
  - CONFIGURE session recovery mechanisms

Task 8: Recovery and Cleanup
IMPLEMENT session recovery features:
  - SETUP expired session cleanup automation
  - CONFIGURE session recovery after network issues
  - IMPLEMENT session migration for device changes
  - SETUP session backup and restore
  - CONFIGURE session disaster recovery

Task 9: Monitoring and Analytics
IMPLEMENT session monitoring:
  - SETUP session analytics and metrics
  - CONFIGURE session health monitoring
  - IMPLEMENT session performance tracking
  - SETUP session security event detection
  - CONFIGURE session compliance monitoring

Task 10: Testing and Validation
IMPLEMENT comprehensive testing:
  - SETUP unit tests for session management
  - CONFIGURE integration tests for session flows
  - IMPLEMENT security testing for session security
  - SETUP performance testing for session handling
  - CONFIGURE compliance testing for session audit
```

### Integration Points

```yaml
# Authentication Integration
- better-auth core authentication system
- OAuth provider integration
- Multi-factor authentication flows
- Password change and recovery
- User account management

# Database Integration
- Session storage and retrieval
- Expired session cleanup
- Session audit logging
- Multi-tenant session isolation
- Performance optimization

# Security Integration
- CSRF protection and validation
- Session token security
- IP address and user agent tracking
- Session hijacking prevention
- Security event monitoring

# Performance Integration
- Cookie caching optimization
- Database connection pooling
- Background session refresh
- Session validation efficiency
- Memory management

# Multi-Tenant Integration
- Tenant context injection
- Tenant-specific session policies
- Tenant session isolation
- Tenant session analytics
- Tenant compliance reporting
```

---

## Validation Gates

### Level 1: Basic Session Management
```bash
# Verify session configuration
npm run type-check
npm run test:unit -- --testNamePattern="session"

# Test session creation
curl -X POST http://localhost:3000/api/auth/session \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

### Level 2: Session Security
```bash
# Test session validation
npm run test:e2e -- --testNamePattern="session-validation"

# Test CSRF protection
npm run test:e2e -- --testNamePattern="csrf"

# Test session expiration
npm run test:e2e -- --testNamePattern="session-expiration"
```

### Level 3: Multi-Device Support
```bash
# Test multi-device sessions
npm run test:e2e -- --testNamePattern="multi-device"

# Test session synchronization
npm run test:e2e -- --testNamePattern="session-sync"

# Test device session limits
npm run test:e2e -- --testNamePattern="device-limits"
```

### Level 4: Performance Optimization
```bash
# Test cookie caching
npm run test:e2e -- --testNamePattern="cookie-cache"

# Test session performance
npm run test:perf -- --testNamePattern="session"

# Test session cleanup
npm run test:e2e -- --testNamePattern="session-cleanup"
```

### Level 5: Multi-Tenant Integration
```bash
# Test tenant session isolation
npm run test:e2e -- --testNamePattern="tenant-isolation"

# Test tenant session policies
npm run test:e2e -- --testNamePattern="tenant-policies"

# Test tenant session analytics
npm run test:e2e -- --testNamePattern="tenant-analytics"
```

---

## Quality Standards

The PRP must include:
- [x] Secure session token generation and validation
- [x] Automatic session expiration and refresh mechanisms
- [x] Multi-device session management and synchronization
- [x] Session revocation and security controls
- [x] Cookie caching for performance optimization
- [x] Session monitoring and audit logging
- [x] Multi-tenant session isolation
- [x] Enterprise security compliance
- [x] Performance optimization and scalability
- [x] Comprehensive session recovery mechanisms
- [x] Administrative session management controls
- [x] Security testing and validation

---

## Expected Outcomes

Upon successful implementation:

1. **Security**: 99.9% session hijacking prevention success rate
2. **Performance**: 50% reduction in authentication-related database calls
3. **User Experience**: Seamless session management across devices
4. **Scalability**: Support for 10,000+ concurrent sessions
5. **Compliance**: Full enterprise security standard compliance
6. **Reliability**: 99.9% session availability and recovery success
7. **Multi-Tenant**: Complete tenant isolation with proper boundaries

---

**Framework**: NEXUS SaaS Starter Multi-Tenant Architecture  
**Technology Stack**: Next.js 15.4+ / better-auth / Session Security  
**Optimization**: Production-ready, enterprise-grade session management
