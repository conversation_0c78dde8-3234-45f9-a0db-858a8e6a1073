# NEXUS SaaS Starter - API Management & Documentation Implementation

**PRP Name**: API Management & Documentation System  
**Version**: 1.0  
**Date**: January 20, 2025  
**Type**: Core Feature Implementation PRP  
**Framework**: Next.js 15.4+ / React 19 / TypeScript 5.8+  
**Phase**: 02-core  

---

## Purpose

Implement a comprehensive API management and documentation system with interactive documentation, automated OpenAPI specification generation, client SDK generation, and developer-first experience for multi-tenant SaaS applications.

## Core Principles

1. **Developer First**: Interactive, comprehensive, and self-documenting APIs
2. **Type Safety**: Complete TypeScript definitions with automatic inference
3. **Multi-Tenant Aware**: Tenant-specific API patterns and isolation
4. **Production Ready**: Enterprise-grade API management and monitoring
5. **Auto-Generated**: Automated documentation from code annotations
6. **Interactive Testing**: Built-in API testing and exploration capabilities

---

## Goal

Build a production-ready API management and documentation system that enables developers to discover, test, and integrate with the SaaS platform APIs efficiently while maintaining enterprise-grade security and multi-tenant isolation.

## Why

- **Developer Experience**: Reduce API integration time from days to hours
- **API Consistency**: Standardized patterns across all endpoints
- **Documentation Quality**: Always up-to-date, interactive documentation
- **Type Safety**: Eliminate runtime API errors through TypeScript
- **Multi-Tenant Security**: Proper tenant isolation and access control
- **Monitoring**: Real-time API performance and usage analytics

## What

A complete API management and documentation system with:
- Interactive API documentation with testing capabilities
- Automated OpenAPI specification generation
- Client SDK generation for multiple languages
- API versioning and deprecation management
- Rate limiting and usage analytics
- Multi-tenant API key management
- Developer portal with guides and examples

### Success Criteria

- [ ] Interactive API documentation with testing capabilities
- [ ] Automated OpenAPI 3.0 specification generation
- [ ] TypeScript client SDK with full type safety
- [ ] Multi-tenant API key management system
- [ ] Rate limiting with tenant-specific quotas
- [ ] API versioning and backward compatibility
- [ ] Developer portal with integration guides
- [ ] Real-time API monitoring and analytics
- [ ] Comprehensive error handling and status codes
- [ ] Performance optimization with <100ms response times

---

## All Needed Context

### Documentation & References

```yaml
# CRITICAL DOCUMENTATION - Context7 Verified Patterns
- url: https://github.com/swagger-api/swagger-codegen
  why: OpenAPI code generation patterns and best practices
  critical: Automated client SDK generation and documentation templates
  context7_verified: true

- url: https://strapi.io/documentation/developer-docs/latest/developer-resources/content-api/content-api.html
  why: REST API design patterns and documentation structure
  critical: API endpoint patterns and response formatting
  context7_verified: true

- url: https://docs.frontegg.com/reference/getting-started-with-your-api
  why: Enterprise API management and multi-tenant patterns
  critical: API key management and tenant isolation
  context7_verified: true

- url: https://spec.openapis.org/oas/v3.0.3
  why: OpenAPI 3.0 specification standard
  critical: API documentation schema and validation

- url: https://scalar.com/
  why: Modern interactive API documentation
  critical: Developer-friendly documentation interface

- url: https://redocly.com/docs/
  why: API documentation best practices
  critical: Documentation structure and presentation

- url: https://nextjs.org/docs/app/building-your-application/routing/route-handlers
  why: Next.js 15.4+ API route patterns
  critical: Server-side API implementation

- url: https://www.better-auth.com/docs/introduction/concepts/api
  why: Authentication API patterns
  critical: Secure API endpoint implementation

- url: https://valibot.dev/
  why: Type-safe API validation
  critical: Request/response validation schemas

- url: https://www.prisma.io/docs/concepts/components/prisma-client/crud
  why: Database operations in API endpoints
  critical: Multi-tenant data access patterns
```

### Current Codebase Patterns

```typescript
// Existing authentication patterns from better-auth
// File: lib/auth.ts (referenced from authentication PRPs)
export const auth = betterAuth({
  database: prisma,
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
  },
  multiTenant: {
    enabled: true,
    tenantIdField: "workspaceId",
  },
});

// Existing API route pattern from Next.js 15.4+
// File: app/api/example/route.ts
export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({ headers: request.headers });
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    return NextResponse.json({ data: "success" });
  } catch (error) {
    return NextResponse.json({ error: "Internal error" }, { status: 500 });
  }
}

// Multi-tenant database pattern from Prisma
// File: prisma/schema.prisma (referenced from database PRPs)
model ApiKey {
  id          String   @id @default(cuid())
  name        String
  key         String   @unique
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id])
  permissions Json
  lastUsedAt  DateTime?
  expiresAt   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("api_keys")
}
```

### Known Gotchas & Context7 Insights

```yaml
# OpenAPI Generator Patterns (Context7 Verified)
swagger_codegen_patterns:
  - "Use markdown tables for property documentation with Name|Type|Description|Notes format"
  - "Generate TypeScript clients with proper error handling and type safety"
  - "Support multiple authentication methods (Bearer, Cookie, API Key)"
  - "Include comprehensive examples for all endpoints"
  - "Provide selective generation options for models, APIs, tests, and documentation"

# Strapi API Patterns (Context7 Verified)
strapi_api_patterns:
  - "Document Service API for core interactions with documents and fields"
  - "REST API with filters, population, sorting, and pagination"
  - "Plugin configuration with default and validator properties"
  - "Store API for configuration management with get/set methods"
  - "Content API relation management with connect/disconnect/set operations"

# Frontegg API Patterns (Context7 Verified)
frontegg_api_patterns:
  - "Tenant access token management with v1 and v2 API versions"
  - "Application management with create/retrieve/update/delete operations"
  - "SSO configuration and excluded emails management"
  - "Multi-tenant API key authentication and authorization"

# Next.js 15.4+ API Route Gotchas
nextjs_api_gotchas:
  - "Use NextRequest/NextResponse for proper type safety"
  - "Handle CORS for browser-based API testing"
  - "Implement proper error boundaries and status codes"
  - "Use middleware for authentication and rate limiting"
  - "Support both JSON and form-data content types"

# Multi-Tenant API Security
multi_tenant_security:
  - "Always validate tenant context in API middleware"
  - "Use workspace-scoped API keys for tenant isolation"
  - "Implement rate limiting per tenant, not globally"
  - "Audit log all API access with tenant context"
  - "Validate permissions at both endpoint and data level"
```

---

## Implementation Blueprint

### Prisma Schema Extensions

```prisma
// Add to existing schema.prisma
model ApiKey {
  id          String   @id @default(cuid())
  name        String
  key         String   @unique
  keyHash     String   @unique // Store hashed version for security
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // Permissions and access control
  permissions Json     // { "endpoints": ["GET:/api/users", "POST:/api/projects"], "scopes": ["read", "write"] }
  rateLimit   Int      @default(1000) // Requests per hour
  
  // Usage tracking
  lastUsedAt  DateTime?
  usageCount  Int      @default(0)
  
  // Lifecycle management
  isActive    Boolean  @default(true)
  expiresAt   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  apiLogs     ApiLog[]
  
  @@map("api_keys")
  @@index([workspaceId])
  @@index([keyHash])
}

model ApiLog {
  id          String   @id @default(cuid())
  workspaceId String
  workspace   Workspace @relation(fields: [workspaceId], references: [id], onDelete: Cascade)
  
  // Request details
  method      String   // GET, POST, PUT, DELETE
  endpoint    String   // /api/users
  statusCode  Int      // 200, 404, 500
  responseTime Int     // milliseconds
  
  // Authentication
  apiKeyId    String?
  apiKey      ApiKey?  @relation(fields: [apiKeyId], references: [id], onDelete: SetNull)
  userId      String?
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  // Request/Response data
  requestBody  Json?
  responseBody Json?
  userAgent    String?
  ipAddress    String?
  
  // Metadata
  createdAt   DateTime @default(now())
  
  @@map("api_logs")
  @@index([workspaceId, createdAt])
  @@index([endpoint, createdAt])
  @@index([apiKeyId])
}

model ApiDocumentation {
  id          String   @id @default(cuid())
  version     String   // v1, v2, etc.
  title       String
  description String
  
  // OpenAPI specification
  openApiSpec Json     // Complete OpenAPI 3.0 specification
  
  // Documentation metadata
  isPublic    Boolean  @default(false)
  isActive    Boolean  @default(true)
  
  // Lifecycle
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@map("api_documentation")
  @@unique([version])
}

model ApiEndpoint {
  id          String   @id @default(cuid())
  path        String   // /api/users/{id}
  method      String   // GET, POST, PUT, DELETE
  version     String   // v1, v2
  
  // Documentation
  summary     String
  description String?
  tags        String[] // ["Users", "Authentication"]
  
  // Schema definitions
  requestSchema  Json?  // JSON Schema for request body
  responseSchema Json   // JSON Schema for response
  
  // Security and permissions
  requiresAuth   Boolean @default(true)
  permissions    String[] // ["users:read", "users:write"]
  rateLimit      Int?     // Override default rate limit
  
  // Status
  isDeprecated   Boolean @default(false)
  deprecatedAt   DateTime?
  isActive       Boolean @default(true)
  
  // Lifecycle
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  @@map("api_endpoints")
  @@unique([path, method, version])
  @@index([version])
  @@index([tags])
}
```

### List of Tasks to be Completed

#### 1. Core API Documentation System (Priority: Critical)

```typescript
// lib/api-documentation.ts - Core documentation engine
export interface ApiDocumentationConfig {
  title: string;
  version: string;
  description: string;
  servers: Array<{ url: string; description: string }>;
  security: Array<{ [key: string]: string[] }>;
}

export class ApiDocumentationGenerator {
  private config: ApiDocumentationConfig;
  private endpoints: Map<string, ApiEndpointSpec> = new Map();
  
  constructor(config: ApiDocumentationConfig) {
    this.config = config;
  }
  
  // Auto-discover API routes from Next.js app directory
  async discoverRoutes(): Promise<void> {
    // Scan app/api directory for route.ts files
    // Extract JSDoc comments and TypeScript types
    // Generate OpenAPI specifications automatically
  }
  
  // Generate complete OpenAPI 3.0 specification
  generateOpenAPISpec(): OpenAPISpec {
    return {
      openapi: "3.0.3",
      info: {
        title: this.config.title,
        version: this.config.version,
        description: this.config.description,
      },
      servers: this.config.servers,
      paths: this.generatePaths(),
      components: this.generateComponents(),
      security: this.config.security,
    };
  }
  
  // Generate TypeScript client SDK
  async generateTypeScriptClient(): Promise<string> {
    // Use OpenAPI Generator patterns from Context7
    // Generate type-safe client with proper error handling
  }
}
```

#### 2. Interactive Documentation Interface (Priority: High)

```typescript
// app/api/docs/route.ts - Documentation serving endpoint
import { NextRequest, NextResponse } from "next/server";
import { ApiDocumentationGenerator } from "@/lib/api-documentation";

export async function GET(request: NextRequest) {
  const generator = new ApiDocumentationGenerator({
    title: "NEXUS SaaS API",
    version: "v1",
    description: "Comprehensive API for multi-tenant SaaS platform",
    servers: [
      { url: "https://api.nexus-saas.com", description: "Production API" },
      { url: "https://staging-api.nexus-saas.com", description: "Staging API" },
      { url: "http://localhost:3000", description: "Development API" }
    ],
    security: [
      { bearerAuth: [] },
      { apiKeyAuth: [] },
      { cookieAuth: [] }
    ]
  });
  
  await generator.discoverRoutes();
  const openApiSpec = generator.generateOpenAPISpec();
  
  return NextResponse.json(openApiSpec);
}

// app/docs/page.tsx - Interactive documentation page
"use client";
import { useEffect, useState } from "react";
import { ApiReference } from "@scalar/api-reference-react";

export default function ApiDocumentationPage() {
  const [spec, setSpec] = useState(null);
  
  useEffect(() => {
    fetch("/api/docs")
      .then(res => res.json())
      .then(setSpec);
  }, []);
  
  if (!spec) return <div>Loading API documentation...</div>;
  
  return (
    <div className="h-screen">
      <ApiReference
        configuration={{
          spec: { content: spec },
          theme: "purple",
          layout: "modern",
          showSidebar: true,
          authentication: {
            preferredSecurityScheme: "bearerAuth",
            apiKey: {
              token: process.env.NEXT_PUBLIC_API_KEY
            }
          }
        }}
      />
    </div>
  );
}
```

#### 3. API Key Management System (Priority: High)

```typescript
// lib/api-key-manager.ts - Multi-tenant API key management
import { prisma } from "@/lib/prisma";
import { createHash, randomBytes } from "crypto";

export class ApiKeyManager {
  // Generate new API key for workspace
  async createApiKey(params: {
    workspaceId: string;
    name: string;
    permissions: string[];
    expiresAt?: Date;
    rateLimit?: number;
  }) {
    const key = `nxs_${randomBytes(32).toString("hex")}`;
    const keyHash = createHash("sha256").update(key).digest("hex");
    
    const apiKey = await prisma.apiKey.create({
      data: {
        name: params.name,
        key: key, // Store plain key temporarily for return
        keyHash,
        workspaceId: params.workspaceId,
        permissions: { endpoints: params.permissions },
        expiresAt: params.expiresAt,
        rateLimit: params.rateLimit || 1000,
      },
    });
    
    // Return key once, then remove from database
    await prisma.apiKey.update({
      where: { id: apiKey.id },
      data: { key: "" }, // Clear plain key from database
    });
    
    return { ...apiKey, key }; // Return with plain key for user
  }
  
  // Validate API key and return permissions
  async validateApiKey(key: string): Promise<{
    isValid: boolean;
    workspaceId?: string;
    permissions?: string[];
    rateLimit?: number;
  }> {
    const keyHash = createHash("sha256").update(key).digest("hex");
    
    const apiKey = await prisma.apiKey.findUnique({
      where: { keyHash },
      include: { workspace: true },
    });
    
    if (!apiKey || !apiKey.isActive) {
      return { isValid: false };
    }
    
    if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
      return { isValid: false };
    }
    
    // Update usage tracking
    await prisma.apiKey.update({
      where: { id: apiKey.id },
      data: {
        lastUsedAt: new Date(),
        usageCount: { increment: 1 },
      },
    });
    
    return {
      isValid: true,
      workspaceId: apiKey.workspaceId,
      permissions: apiKey.permissions.endpoints || [],
      rateLimit: apiKey.rateLimit,
    };
  }
}

// middleware.ts - API authentication middleware
import { NextRequest, NextResponse } from "next/server";
import { ApiKeyManager } from "@/lib/api-key-manager";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip middleware for non-API routes
  if (!pathname.startsWith("/api/")) {
    return NextResponse.next();
  }
  
  // Skip authentication for public endpoints
  const publicEndpoints = ["/api/docs", "/api/health"];
  if (publicEndpoints.includes(pathname)) {
    return NextResponse.next();
  }
  
  const apiKeyManager = new ApiKeyManager();
  
  // Check for API key in header
  const apiKey = request.headers.get("x-api-key") || 
                 request.headers.get("authorization")?.replace("Bearer ", "");
  
  if (apiKey) {
    const validation = await apiKeyManager.validateApiKey(apiKey);
    
    if (!validation.isValid) {
      return NextResponse.json(
        { error: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // Add workspace context to request headers
    const response = NextResponse.next();
    response.headers.set("x-workspace-id", validation.workspaceId!);
    response.headers.set("x-permissions", JSON.stringify(validation.permissions));
    
    return response;
  }
  
  // Fall back to session-based authentication
  // ... existing session validation logic
  
  return NextResponse.next();
}
```

#### 4. Rate Limiting & Usage Analytics (Priority: Medium)

```typescript
// lib/rate-limiter.ts - Tenant-aware rate limiting
import { Redis } from "ioredis";

export class RateLimiter {
  private redis: Redis;
  
  constructor() {
    this.redis = new Redis(process.env.REDIS_URL!);
  }
  
  async checkRateLimit(params: {
    workspaceId: string;
    endpoint: string;
    limit: number;
    window: number; // seconds
  }): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    const key = `rate_limit:${params.workspaceId}:${params.endpoint}`;
    const now = Math.floor(Date.now() / 1000);
    const window = Math.floor(now / params.window) * params.window;
    
    const current = await this.redis.incr(`${key}:${window}`);
    
    if (current === 1) {
      await this.redis.expire(`${key}:${window}`, params.window);
    }
    
    return {
      allowed: current <= params.limit,
      remaining: Math.max(0, params.limit - current),
      resetTime: window + params.window,
    };
  }
}

// lib/api-analytics.ts - API usage analytics
export class ApiAnalytics {
  async logApiRequest(params: {
    workspaceId: string;
    method: string;
    endpoint: string;
    statusCode: number;
    responseTime: number;
    apiKeyId?: string;
    userId?: string;
    userAgent?: string;
    ipAddress?: string;
  }) {
    await prisma.apiLog.create({
      data: params,
    });
  }
  
  async getUsageStats(workspaceId: string, timeRange: "1h" | "24h" | "7d" | "30d") {
    const since = new Date();
    switch (timeRange) {
      case "1h": since.setHours(since.getHours() - 1); break;
      case "24h": since.setDate(since.getDate() - 1); break;
      case "7d": since.setDate(since.getDate() - 7); break;
      case "30d": since.setDate(since.getDate() - 30); break;
    }
    
    const stats = await prisma.apiLog.groupBy({
      by: ["endpoint", "statusCode"],
      where: {
        workspaceId,
        createdAt: { gte: since },
      },
      _count: true,
      _avg: { responseTime: true },
    });
    
    return stats;
  }
}
```

#### 5. Client SDK Generation (Priority: Medium)

```typescript
// lib/sdk-generator.ts - TypeScript client SDK generation
export class SDKGenerator {
  async generateTypeScriptClient(openApiSpec: any): Promise<string> {
    // Based on OpenAPI Generator patterns from Context7
    const clientCode = `
// Generated TypeScript client for NEXUS SaaS API
// DO NOT EDIT - This file is auto-generated

export interface ApiClientConfig {
  baseUrl: string;
  apiKey?: string;
  timeout?: number;
}

export class NexusSaasApiClient {
  private config: ApiClientConfig;
  
  constructor(config: ApiClientConfig) {
    this.config = {
      timeout: 10000,
      ...config,
    };
  }
  
  private async request<T>(
    method: string,
    path: string,
    data?: any
  ): Promise<T> {
    const url = \`\${this.config.baseUrl}\${path}\`;
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };
    
    if (this.config.apiKey) {
      headers["x-api-key"] = this.config.apiKey;
    }
    
    const response = await fetch(url, {
      method,
      headers,
      body: data ? JSON.stringify(data) : undefined,
      signal: AbortSignal.timeout(this.config.timeout!),
    });
    
    if (!response.ok) {
      throw new ApiError(response.status, await response.text());
    }
    
    return response.json();
  }
  
  // Auto-generated methods for each endpoint
  ${this.generateEndpointMethods(openApiSpec)}
}

export class ApiError extends Error {
  constructor(
    public status: number,
    public message: string
  ) {
    super(\`API Error \${status}: \${message}\`);
  }
}
`;
    
    return clientCode;
  }
  
  private generateEndpointMethods(spec: any): string {
    // Generate type-safe methods for each endpoint
    // Based on OpenAPI specification paths
    return "";
  }
}
```

#### 6. Developer Portal (Priority: Low)

```typescript
// app/developers/page.tsx - Developer portal
export default function DeveloperPortalPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Quick Start Guide */}
        <div className="lg:col-span-2">
          <h1 className="text-3xl font-bold mb-6">Developer Portal</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold mb-4">Quick Start</h2>
              <div className="bg-gray-50 p-6 rounded-lg">
                <pre className="text-sm">
{`# Install the TypeScript client
npm install @nexus-saas/api-client

# Initialize the client
import { NexusSaasApiClient } from '@nexus-saas/api-client';

const client = new NexusSaasApiClient({
  baseUrl: 'https://api.nexus-saas.com',
  apiKey: 'your-api-key-here'
});

# Make your first API call
const users = await client.users.list();`}
                </pre>
              </div>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold mb-4">Authentication</h2>
              <p className="text-gray-600 mb-4">
                The NEXUS SaaS API supports multiple authentication methods:
              </p>
              <ul className="list-disc list-inside space-y-2">
                <li>API Keys (recommended for server-to-server)</li>
                <li>Bearer Tokens (for user-authenticated requests)</li>
                <li>Session Cookies (for browser-based applications)</li>
              </ul>
            </section>
          </div>
        </div>
        
        {/* Sidebar */}
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg border">
            <h3 className="font-semibold mb-4">API Reference</h3>
            <a 
              href="/docs" 
              className="text-blue-600 hover:underline"
            >
              Interactive API Documentation →
            </a>
          </div>
          
          <div className="bg-white p-6 rounded-lg border">
            <h3 className="font-semibold mb-4">SDKs & Libraries</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-blue-600 hover:underline">TypeScript/JavaScript</a></li>
              <li><a href="#" className="text-blue-600 hover:underline">Python</a></li>
              <li><a href="#" className="text-blue-600 hover:underline">Go</a></li>
              <li><a href="#" className="text-blue-600 hover:underline">PHP</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### Technology Stack

```yaml
# API Documentation & Management
- OpenAPI 3.0: API specification standard
- Scalar: Modern interactive documentation interface
- TypeDoc: TypeScript documentation generation
- Swagger Codegen: Multi-language client generation

# API Infrastructure
- Next.js 15.4+: API route handlers and middleware
- Valibot: Type-safe request/response validation
- Prisma: Database ORM with multi-tenant support
- Redis: Rate limiting and caching

# Authentication & Security
- Better-Auth: Multi-tenant authentication
- API Key Management: Workspace-scoped access control
- Rate Limiting: Tenant-aware request throttling
- Audit Logging: Comprehensive API access tracking

# Developer Experience
- TypeScript: Full type safety for client SDKs
- Auto-generation: Documentation from code annotations
- Interactive Testing: Built-in API exploration
- Multi-language SDKs: Generated client libraries
```

---

## Validation Gates

### Level 1: Syntax & Style
```bash
# TypeScript compilation
npx tsc --noEmit

# ESLint validation
npm run lint

# Prettier formatting
npm run format

# API schema validation
npm run validate:openapi
```

### Level 2: Unit Tests
```typescript
// __tests__/api-documentation.test.ts
import { ApiDocumentationGenerator } from "@/lib/api-documentation";

describe("ApiDocumentationGenerator", () => {
  test("generates valid OpenAPI specification", async () => {
    const generator = new ApiDocumentationGenerator({
      title: "Test API",
      version: "v1",
      description: "Test API documentation",
      servers: [{ url: "http://localhost:3000", description: "Test" }],
      security: [{ bearerAuth: [] }],
    });
    
    const spec = generator.generateOpenAPISpec();
    
    expect(spec.openapi).toBe("3.0.3");
    expect(spec.info.title).toBe("Test API");
    expect(spec.info.version).toBe("v1");
  });
});

// __tests__/api-key-manager.test.ts
import { ApiKeyManager } from "@/lib/api-key-manager";

describe("ApiKeyManager", () => {
  test("creates and validates API keys", async () => {
    const manager = new ApiKeyManager();
    
    const apiKey = await manager.createApiKey({
      workspaceId: "test-workspace",
      name: "Test Key",
      permissions: ["users:read", "projects:write"],
    });
    
    expect(apiKey.key).toMatch(/^nxs_[a-f0-9]{64}$/);
    
    const validation = await manager.validateApiKey(apiKey.key);
    expect(validation.isValid).toBe(true);
    expect(validation.workspaceId).toBe("test-workspace");
  });
});
```

### Level 3: Integration Tests
```bash
# Start development server
npm run dev

# Test API documentation endpoint
curl -X GET http://localhost:3000/api/docs \
  -H "Accept: application/json" | jq .

# Test API key creation
curl -X POST http://localhost:3000/api/keys \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $SESSION_TOKEN" \
  -d '{
    "name": "Test Integration Key",
    "permissions": ["users:read"]
  }'

# Test API key authentication
curl -X GET http://localhost:3000/api/users \
  -H "x-api-key: $API_KEY"

# Test rate limiting
for i in {1..10}; do
  curl -X GET http://localhost:3000/api/users \
    -H "x-api-key: $API_KEY" \
    -w "%{http_code}\n"
done
```

### Level 4: End-to-End & Creative Validation
```typescript
// e2e/api-documentation.spec.ts
import { test, expect } from "@playwright/test";

test("API documentation is interactive and functional", async ({ page }) => {
  await page.goto("/docs");
  
  // Check that documentation loads
  await expect(page.locator("h1")).toContainText("NEXUS SaaS API");
  
  // Test interactive API calls
  await page.click('[data-testid="try-it-button"]');
  await page.fill('[data-testid="api-key-input"]', process.env.TEST_API_KEY!);
  await page.click('[data-testid="send-request"]');
  
  // Verify response
  await expect(page.locator('[data-testid="response-status"]')).toContainText("200");
});

// Performance testing with k6
// k6-api-performance.js
import http from 'k6/http';
import { check } from 'k6';

export let options = {
  stages: [
    { duration: '30s', target: 100 },
    { duration: '1m', target: 500 },
    { duration: '30s', target: 0 },
  ],
};

export default function() {
  const response = http.get('http://localhost:3000/api/docs');
  
  check(response, {
    'API documentation loads': (r) => r.status === 200,
    'Response time < 100ms': (r) => r.timings.duration < 100,
  });
}
```

### Final Validation Checklist

- [ ] OpenAPI 3.0 specification validates without errors
- [ ] Interactive documentation loads and functions correctly
- [ ] API key creation and validation works across tenants
- [ ] Rate limiting enforces tenant-specific quotas
- [ ] TypeScript client SDK generates without errors
- [ ] All API endpoints return consistent error formats
- [ ] Multi-tenant isolation prevents cross-tenant data access
- [ ] Performance meets <100ms response time requirements
- [ ] Security audit passes with zero critical vulnerabilities
- [ ] Documentation auto-updates when API changes

### Anti-Patterns to Avoid

```yaml
# Documentation Anti-Patterns
- Manual documentation that gets out of sync with code
- Missing or incomplete request/response examples
- Inconsistent error response formats across endpoints
- No interactive testing capabilities in documentation

# API Management Anti-Patterns
- Global rate limiting instead of tenant-specific limits
- Storing API keys in plain text
- Missing audit logging for API access
- No API versioning strategy

# Security Anti-Patterns
- Exposing internal error details in API responses
- Missing input validation on API endpoints
- No authentication on documentation endpoints
- Cross-tenant data leakage in API responses

# Performance Anti-Patterns
- N+1 queries in API endpoints
- Missing database indexes for API queries
- No caching for frequently accessed data
- Synchronous processing for long-running operations
```

---

**Implementation Priority**: Core Feature (Phase 02)  
**Estimated Complexity**: High  
**Dependencies**: Authentication system, database schema, multi-tenant architecture  
**Success Metrics**: <100ms API response times, 99.9% uptime, zero security vulnerabilities