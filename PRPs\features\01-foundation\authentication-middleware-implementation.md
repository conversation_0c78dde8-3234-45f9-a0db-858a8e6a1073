# NEXUS SaaS Starter - Authentication Middleware Implementation

**PRP Name**: Authentication Middleware  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Foundation Implementation PRP  
**Phase**: 01-foundation  
**Framework**: Next.js 15.4+ / better-auth / Middleware Security  

---

## Purpose

Implement comprehensive authentication middleware using better-auth with request validation, tenant context injection, role-based access control, and secure authentication flows for multi-tenant SaaS applications.

## Core Principles

1. **Security First**: Secure request validation, session verification, and access control
2. **Performance Optimized**: Efficient authentication checks with minimal overhead
3. **Multi-Tenant Aware**: Tenant context injection and isolation
4. **Role-Based Access**: Granular permission checking and authorization
5. **Developer Experience**: Clear error messages and debugging capabilities
6. **Enterprise Ready**: Audit logging, monitoring, and compliance features

---

## Goal

Build a production-ready authentication middleware system that provides secure request validation, tenant context injection, role-based access control, and comprehensive authentication flows with monitoring and audit capabilities.

## Why

- **Security**: Prevents unauthorized access and validates all requests
- **Performance**: Optimizes authentication checks and reduces database calls
- **Multi-Tenancy**: Enforces tenant isolation and context management
- **Authorization**: Provides fine-grained access control and permissions
- **Monitoring**: Enables comprehensive audit trails and security monitoring
- **Maintainability**: Clear middleware structure and error handling

## What

A comprehensive authentication middleware system with:
- Secure request validation and session verification
- Tenant context injection and isolation
- Role-based access control and permissions
- Authentication flow protection and routing
- Comprehensive audit logging and monitoring
- Error handling and developer-friendly debugging

### Success Criteria

- [ ] Secure request validation and session verification
- [ ] Tenant context injection and isolation
- [ ] Role-based access control and permissions
- [ ] Authentication flow protection and routing
- [ ] Comprehensive audit logging and monitoring
- [ ] Error handling and developer-friendly debugging
- [ ] Multi-tenant middleware configuration
- [ ] Performance optimization and caching
- [ ] Enterprise security compliance
- [ ] Comprehensive middleware testing

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://www.better-auth.com/docs/introduction/integrations/next
  why: Next.js middleware integration with better-auth
  critical: Understanding Next.js middleware patterns and session handling

- url: https://www.better-auth.com/docs/introduction/concepts/plugins
  why: Better-auth plugin system for custom middleware
  critical: Creating custom middleware and authentication flows

- url: https://www.better-auth.com/docs/introduction/concepts/hooks
  why: Better-auth hooks for request interception
  critical: Before/after hooks for authentication flow control

- url: https://www.better-auth.com/docs/introduction/plugins/admin
  why: Administrative controls and permission checking
  critical: Role-based access control and permission validation

- url: https://www.better-auth.com/docs/introduction/concepts/api
  why: Better-auth API for server-side authentication
  critical: Server-side session validation and user management

- url: https://nextjs.org/docs/app/building-your-application/routing/middleware
  why: Next.js middleware system and request handling
  critical: Request interception and response modification

- url: https://www.better-auth.com/docs/introduction/plugins/bearer
  why: Bearer token authentication and validation
  critical: Token-based authentication for API endpoints

- url: https://www.better-auth.com/docs/introduction/concepts/session-management
  why: Session management and validation patterns
  critical: Session security and expiration handling

- url: https://www.better-auth.com/docs/introduction/plugins/organization
  why: Organization-based access control
  critical: Multi-tenant permission management

- url: https://owasp.org/www-project-cheat-sheets/cheatsheets/Authentication_Cheat_Sheet.html
  why: OWASP authentication security best practices
  critical: Security implementation guidelines and threat mitigation
```

### Current Technology Stack

```yaml
# Authentication Middleware
- better-auth: Latest (comprehensive middleware framework)
- sessionMiddleware: Session validation and injection
- requireAuth: Authentication requirement enforcement
- permissionMiddleware: Role-based access control
- auditMiddleware: Request logging and monitoring

# Next.js Integration
- Next.js Middleware: Request interception and routing
- App Router: Server-side authentication handling
- Route Handlers: API endpoint protection
- Server Components: Authenticated component rendering
- Client Components: Reactive authentication state

# Security Features
- CSRF Protection: Cross-site request forgery prevention
- Session Validation: Secure session token verification
- Role-Based Access: Granular permission checking
- Tenant Isolation: Multi-tenant context enforcement
- Audit Logging: Comprehensive request monitoring

# Performance Optimization
- Cookie Caching: Session data caching for performance
- Middleware Chaining: Efficient middleware composition
- Database Optimization: Reduced authentication queries
- Memory Management: Efficient session handling
- Response Optimization: Minimal response overhead
```

### Known Gotchas & Library Quirks

```typescript
// CRITICAL: Next.js middleware gotchas
// Middleware execution: Runs before every request, must be optimized
// Runtime limitations: Edge runtime has Node.js API restrictions
// Session validation: Full session API requires Node.js runtime
// Cookie handling: Secure, HttpOnly, SameSite flags required
// Response modification: Must handle redirects and rewrites properly
// Error handling: Middleware errors can break entire application
// Performance: Middleware runs on every request, must be fast
// Memory usage: Edge runtime has memory limitations
// Database calls: Minimize database queries in middleware
// Network requests: Avoid external API calls in middleware

// CRITICAL: better-auth middleware gotchas
// Session validation: getSession API calls can be expensive
// Cookie parsing: Manual cookie parsing for performance
// Plugin ordering: Middleware plugins must be ordered correctly
// Context injection: Request context must be properly typed
// Error propagation: Middleware errors must be handled gracefully
// Hook execution: Before/after hooks affect performance
// Permission checking: Role validation can be database intensive
// Tenant context: Multi-tenant isolation requires careful handling
// Token validation: Bearer tokens need proper validation
// Request modification: Context changes must be properly propagated

// CRITICAL: Security middleware gotchas
// Session fixation: New sessions must be generated on authentication
// CSRF protection: Must validate request origin and headers
// Permission caching: Cached permissions must be invalidated properly
// Token expiration: Expired tokens must be handled gracefully
// Rate limiting: Authentication attempts must be rate limited
// Input validation: Request data must be validated and sanitized
// Error messages: Security errors must not leak sensitive information
// Audit logging: Security events must be logged without performance impact
// Access control: Permission checks must be fail-secure
// Session hijacking: Session tokens must be protected from theft
```

---

## Implementation Blueprint

### Core Authentication Middleware

```typescript
// lib/auth-middleware.ts
import { NextRequest, NextResponse } from "next/server";
import { getSessionCookie } from "better-auth/cookies";
import { auth } from "@/lib/auth";
import { auditLogger } from "@/lib/audit-logger";
import { getTenantContext } from "@/lib/tenant-context";
import { checkPermissions } from "@/lib/permission-checker";

// Middleware configuration
export const authMiddleware = {
  // Routes that require authentication
  protectedRoutes: [
    "/dashboard",
    "/api/protected",
    "/admin",
    "/settings",
    "/workspace",
  ],
  
  // Routes that should redirect if authenticated
  authRoutes: ["/signin", "/signup", "/forgot-password"],
  
  // Public routes (no authentication required)
  publicRoutes: ["/", "/about", "/pricing", "/api/public"],
  
  // API routes that require authentication
  apiRoutes: ["/api/user", "/api/workspace", "/api/admin"],
  
  // Admin routes (require admin role)
  adminRoutes: ["/admin", "/api/admin"],
  
  // Fresh session routes (require recent authentication)
  freshRoutes: ["/settings/security", "/admin/users"],
};

// Main middleware function
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const startTime = Date.now();
  
  try {
    // Performance optimization: Skip middleware for static assets
    if (isStaticAsset(pathname)) {
      return NextResponse.next();
    }
    
    // Check if route requires authentication
    const requiresAuth = authMiddleware.protectedRoutes.some(route =>
      pathname.startsWith(route)
    );
    
    // Check if route should redirect authenticated users
    const isAuthRoute = authMiddleware.authRoutes.some(route =>
      pathname.startsWith(route)
    );
    
    // Check if route is public
    const isPublicRoute = authMiddleware.publicRoutes.some(route =>
      pathname === route || pathname.startsWith(route)
    );
    
    // Get session information
    const sessionCookie = getSessionCookie(request);
    let session = null;
    
    // For performance, use cookie check for simple redirects
    if (sessionCookie && (requiresAuth || isAuthRoute)) {
      // For Next.js 15.2.0+ with Node.js runtime
      if (process.env.NODE_ENV === "production") {
        session = await auth.api.getSession({
          headers: request.headers,
        });
      } else {
        // For development or Edge runtime, use cookie presence
        session = { user: { id: "temp" }, session: { id: "temp" } };
      }
    }
    
    // Handle unauthenticated requests to protected routes
    if (requiresAuth && !sessionCookie) {
      await logSecurityEvent(request, "UNAUTHORIZED_ACCESS_ATTEMPT", {
        path: pathname,
        reason: "No session cookie",
      });
      
      return NextResponse.redirect(new URL("/signin", request.url));
    }
    
    // Handle authenticated requests to auth routes
    if (isAuthRoute && sessionCookie) {
      await logSecurityEvent(request, "REDIRECT_AUTHENTICATED_USER", {
        path: pathname,
        reason: "Already authenticated",
      });
      
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }
    
    // Handle API route authentication
    if (pathname.startsWith("/api/")) {
      return await handleApiAuthentication(request, pathname);
    }
    
    // Handle admin route authorization
    if (authMiddleware.adminRoutes.some(route => pathname.startsWith(route))) {
      return await handleAdminAuthorization(request, pathname, session);
    }
    
    // Handle fresh session routes
    if (authMiddleware.freshRoutes.some(route => pathname.startsWith(route))) {
      return await handleFreshSessionCheck(request, pathname, session);
    }
    
    // Inject tenant context for authenticated requests
    let response = NextResponse.next();
    
    if (sessionCookie && session) {
      response = await injectTenantContext(request, response, session);
    }
    
    // Log request for audit
    await logRequestAudit(request, response, {
      authenticated: !!sessionCookie,
      processingTime: Date.now() - startTime,
    });
    
    return response;
    
  } catch (error) {
    console.error("Middleware error:", error);
    
    await logSecurityEvent(request, "MIDDLEWARE_ERROR", {
      error: error.message,
      path: pathname,
    });
    
    // Return error response for API routes
    if (pathname.startsWith("/api/")) {
      return NextResponse.json(
        { error: "Authentication error" },
        { status: 500 }
      );
    }
    
    // Redirect to error page for regular routes
    return NextResponse.redirect(new URL("/error", request.url));
  }
}

// Handle API route authentication
async function handleApiAuthentication(
  request: NextRequest,
  pathname: string
): Promise<NextResponse> {
  const isProtectedApi = authMiddleware.apiRoutes.some(route =>
    pathname.startsWith(route)
  );
  
  if (!isProtectedApi) {
    return NextResponse.next();
  }
  
  // Check for Bearer token authentication
  const authHeader = request.headers.get("authorization");
  const sessionCookie = getSessionCookie(request);
  
  if (!authHeader && !sessionCookie) {
    return NextResponse.json(
      { error: "Authentication required" },
      { status: 401 }
    );
  }
  
  try {
    let session = null;
    
    // Validate Bearer token
    if (authHeader?.startsWith("Bearer ")) {
      const token = authHeader.substring(7);
      session = await auth.api.getSession({
        headers: new Headers({
          authorization: `Bearer ${token}`,
        }),
      });
    } else if (sessionCookie) {
      // Validate session cookie
      session = await auth.api.getSession({
        headers: request.headers,
      });
    }
    
    if (!session) {
      return NextResponse.json(
        { error: "Invalid authentication" },
        { status: 401 }
      );
    }
    
    // Inject session context into request
    const response = NextResponse.next();
    response.headers.set("x-user-id", session.user.id);
    response.headers.set("x-session-id", session.session.id);
    
    return response;
    
  } catch (error) {
    console.error("API authentication error:", error);
    return NextResponse.json(
      { error: "Authentication failed" },
      { status: 401 }
    );
  }
}

// Handle admin authorization
async function handleAdminAuthorization(
  request: NextRequest,
  pathname: string,
  session: any
): Promise<NextResponse> {
  if (!session) {
    return NextResponse.redirect(new URL("/signin", request.url));
  }
  
  try {
    // Check if user has admin permissions
    const hasPermission = await auth.api.userHasPermission({
      body: {
        userId: session.user.id,
        permissions: {
          admin: ["access"],
        },
      },
    });
    
    if (!hasPermission) {
      await logSecurityEvent(request, "UNAUTHORIZED_ADMIN_ACCESS", {
        userId: session.user.id,
        path: pathname,
      });
      
      return NextResponse.redirect(new URL("/403", request.url));
    }
    
    return NextResponse.next();
    
  } catch (error) {
    console.error("Admin authorization error:", error);
    return NextResponse.redirect(new URL("/error", request.url));
  }
}

// Handle fresh session requirement
async function handleFreshSessionCheck(
  request: NextRequest,
  pathname: string,
  session: any
): Promise<NextResponse> {
  if (!session) {
    return NextResponse.redirect(new URL("/signin", request.url));
  }
  
  try {
    // Check if session is fresh (created within last 5 minutes)
    const sessionAge = Date.now() - new Date(session.session.createdAt).getTime();
    const freshAge = 5 * 60 * 1000; // 5 minutes
    
    if (sessionAge > freshAge) {
      // Redirect to re-authentication
      const redirectUrl = new URL("/signin", request.url);
      redirectUrl.searchParams.set("fresh", "true");
      redirectUrl.searchParams.set("redirect", pathname);
      
      return NextResponse.redirect(redirectUrl);
    }
    
    return NextResponse.next();
    
  } catch (error) {
    console.error("Fresh session check error:", error);
    return NextResponse.redirect(new URL("/error", request.url));
  }
}

// Inject tenant context
async function injectTenantContext(
  request: NextRequest,
  response: NextResponse,
  session: any
): Promise<NextResponse> {
  try {
    // Get tenant context from session
    const tenantContext = await getTenantContext(session.session.tenantId);
    
    if (tenantContext) {
      response.headers.set("x-tenant-id", tenantContext.id);
      response.headers.set("x-tenant-name", tenantContext.name);
      response.headers.set("x-tenant-plan", tenantContext.plan);
    }
    
    return response;
    
  } catch (error) {
    console.error("Tenant context injection error:", error);
    return response;
  }
}

// Security event logging
async function logSecurityEvent(
  request: NextRequest,
  event: string,
  details: any
) {
  try {
    await auditLogger.logSecurityEvent({
      event,
      details,
      request: {
        method: request.method,
        url: request.url,
        headers: Object.fromEntries(request.headers.entries()),
        ip: request.ip || request.headers.get("x-forwarded-for") || "unknown",
        userAgent: request.headers.get("user-agent") || "unknown",
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Failed to log security event:", error);
  }
}

// Request audit logging
async function logRequestAudit(
  request: NextRequest,
  response: NextResponse,
  metadata: any
) {
  try {
    await auditLogger.logRequest({
      request: {
        method: request.method,
        url: request.url,
        headers: Object.fromEntries(request.headers.entries()),
        ip: request.ip || request.headers.get("x-forwarded-for") || "unknown",
        userAgent: request.headers.get("user-agent") || "unknown",
      },
      response: {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
      },
      metadata,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Failed to log request audit:", error);
  }
}

// Check if path is static asset
function isStaticAsset(pathname: string): boolean {
  const staticExtensions = [
    ".js", ".css", ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico",
    ".woff", ".woff2", ".ttf", ".eot", ".map", ".json", ".xml"
  ];
  
  return staticExtensions.some(ext => pathname.endsWith(ext)) ||
         pathname.startsWith("/_next/") ||
         pathname.startsWith("/static/");
}

// Middleware configuration
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public folder)
     */
    "/((?!_next/static|_next/image|favicon.ico|public/).*)",
  ],
};
```

### Authentication Hooks and Plugins

```typescript
// lib/auth-hooks.ts
import { betterAuth } from "better-auth";
import { createAuthMiddleware } from "better-auth/api";
import { sessionMiddleware } from "better-auth/api";
import { auditLogger } from "@/lib/audit-logger";
import { rateLimiter } from "@/lib/rate-limiter";
import { getTenantContext } from "@/lib/tenant-context";

// Custom authentication plugin
export const authenticationPlugin = () => {
  return {
    id: "authentication-middleware",
    
    // Before hooks for request validation
    hooks: {
      before: [
        {
          // Rate limiting for authentication requests
          matcher: (context) => 
            context.path.startsWith("/sign-in") ||
            context.path.startsWith("/sign-up") ||
            context.path.startsWith("/forgot-password"),
          
          handler: createAuthMiddleware(async (ctx) => {
            const ip = ctx.headers.get("x-forwarded-for") || "unknown";
            const userAgent = ctx.headers.get("user-agent") || "unknown";
            
            // Apply rate limiting
            const rateLimitResult = await rateLimiter.check({
              key: `auth:${ip}`,
              limit: 5,
              window: 60000, // 1 minute
            });
            
            if (!rateLimitResult.success) {
              await auditLogger.logSecurityEvent({
                event: "RATE_LIMIT_EXCEEDED",
                details: {
                  ip,
                  userAgent,
                  path: ctx.path,
                  remaining: rateLimitResult.remaining,
                },
                timestamp: new Date().toISOString(),
              });
              
              throw new Error("Rate limit exceeded");
            }
            
            return { context: ctx };
          }),
        },
        
        {
          // Tenant context validation
          matcher: (context) => 
            context.path.startsWith("/workspace") ||
            context.path.startsWith("/api/workspace"),
          
          handler: createAuthMiddleware(async (ctx) => {
            const tenantId = ctx.headers.get("x-tenant-id") || 
                           ctx.query.tenantId ||
                           ctx.body?.tenantId;
            
            if (!tenantId) {
              throw new Error("Tenant ID required");
            }
            
            // Validate tenant access
            const tenantContext = await getTenantContext(tenantId);
            if (!tenantContext) {
              throw new Error("Invalid tenant");
            }
            
            // Inject tenant context
            return {
              context: {
                ...ctx,
                tenantId,
                tenantContext,
              },
            };
          }),
        },
        
        {
          // Input validation and sanitization
          matcher: (context) => 
            context.path.startsWith("/sign-up") ||
            context.path.startsWith("/update-profile"),
          
          handler: createAuthMiddleware(async (ctx) => {
            const { email, name, password } = ctx.body;
            
            // Validate email format
            if (email && !isValidEmail(email)) {
              throw new Error("Invalid email format");
            }
            
            // Sanitize name input
            if (name && typeof name === "string") {
              ctx.body.name = sanitizeInput(name);
            }
            
            // Validate password strength
            if (password && !isStrongPassword(password)) {
              throw new Error("Password does not meet requirements");
            }
            
            return { context: ctx };
          }),
        },
      ],
      
      // After hooks for response processing
      after: [
        {
          // Log successful authentication
          matcher: (context) => 
            context.path === "/sign-in/email" ||
            context.path === "/sign-up/email",
          
          handler: createAuthMiddleware(async (ctx) => {
            const user = ctx.context.user;
            const session = ctx.context.session;
            
            await auditLogger.logSecurityEvent({
              event: "SUCCESSFUL_AUTHENTICATION",
              details: {
                userId: user?.id,
                sessionId: session?.id,
                path: ctx.path,
                ip: ctx.headers.get("x-forwarded-for") || "unknown",
                userAgent: ctx.headers.get("user-agent") || "unknown",
              },
              timestamp: new Date().toISOString(),
            });
            
            return { context: ctx };
          }),
        },
        
        {
          // Update last activity timestamp
          matcher: (context) => true, // All authenticated requests
          
          handler: createAuthMiddleware(async (ctx) => {
            const session = ctx.context.session;
            
            if (session) {
              // Update last activity (implement as needed)
              await updateLastActivity(session.id);
            }
            
            return { context: ctx };
          }),
        },
      ],
    },
    
    // Custom middleware for endpoints
    middlewares: [
      {
        path: "/require-fresh-session",
        middleware: createAuthMiddleware(async (ctx) => {
          const session = ctx.context.session;
          
          if (!session) {
            throw new Error("Authentication required");
          }
          
          // Check if session is fresh (created within last 5 minutes)
          const sessionAge = Date.now() - new Date(session.createdAt).getTime();
          const freshAge = 5 * 60 * 1000; // 5 minutes
          
          if (sessionAge > freshAge) {
            throw new Error("Fresh authentication required");
          }
          
          return { context: ctx };
        }),
      },
      
      {
        path: "/require-admin",
        middleware: createAuthMiddleware(async (ctx) => {
          const session = ctx.context.session;
          
          if (!session) {
            throw new Error("Authentication required");
          }
          
          // Check admin permissions
          const hasAdminPermission = await auth.api.userHasPermission({
            body: {
              userId: session.userId,
              permissions: {
                admin: ["access"],
              },
            },
          });
          
          if (!hasAdminPermission) {
            throw new Error("Admin access required");
          }
          
          return { context: ctx };
        }),
      },
    ],
    
    // Custom endpoints
    endpoints: {
      // Health check endpoint
      healthCheck: createAuthEndpoint("/auth/health", {
        method: "GET",
      }, async (ctx) => {
        return ctx.json({
          status: "healthy",
          timestamp: new Date().toISOString(),
        });
      }),
      
      // Session info endpoint
      sessionInfo: createAuthEndpoint("/auth/session-info", {
        method: "GET",
        use: [sessionMiddleware],
      }, async (ctx) => {
        const session = ctx.context.session;
        
        if (!session) {
          return ctx.json({ error: "No session" }, { status: 401 });
        }
        
        return ctx.json({
          user: {
            id: session.userId,
            email: session.user.email,
            name: session.user.name,
          },
          session: {
            id: session.id,
            createdAt: session.createdAt,
            expiresAt: session.expiresAt,
          },
        });
      }),
    },
  };
};

// Utility functions
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, "");
}

function isStrongPassword(password: string): boolean {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  return (
    password.length >= minLength &&
    hasUpperCase &&
    hasLowerCase &&
    hasNumbers &&
    hasSpecialChar
  );
}

async function updateLastActivity(sessionId: string) {
  try {
    // Update session last activity timestamp
    await auth.database.session.update({
      where: { id: sessionId },
      data: { lastActivity: new Date() },
    });
  } catch (error) {
    console.error("Failed to update last activity:", error);
  }
}
```

### Task Breakdown

```yaml
Task 1: Core Middleware Infrastructure
SETUP authentication middleware foundation:
  - CONFIGURE Next.js middleware with better-auth integration
  - SETUP protected route definitions and patterns
  - CONFIGURE session validation and cookie handling
  - SETUP error handling and response management
  - INTEGRATE audit logging and security monitoring

Task 2: Request Validation System
IMPLEMENT comprehensive request validation:
  - SETUP input validation and sanitization
  - CONFIGURE rate limiting for authentication requests
  - IMPLEMENT CSRF protection and validation
  - SETUP request origin verification
  - CONFIGURE security headers and policies

Task 3: Tenant Context Management
IMPLEMENT tenant-aware middleware:
  - SETUP tenant context injection and validation
  - CONFIGURE tenant isolation and boundaries
  - IMPLEMENT tenant-specific routing and access
  - SETUP tenant-aware permission checking
  - CONFIGURE tenant analytics and monitoring

Task 4: Role-Based Access Control
IMPLEMENT permission-based middleware:
  - SETUP role and permission validation
  - CONFIGURE admin route protection
  - IMPLEMENT fine-grained access control
  - SETUP permission caching and optimization
  - CONFIGURE role-based routing and features

Task 5: API Authentication
IMPLEMENT API-specific authentication:
  - SETUP Bearer token validation
  - CONFIGURE API key authentication
  - IMPLEMENT API route protection
  - SETUP API-specific error handling
  - CONFIGURE API rate limiting and throttling

Task 6: Session Management Integration
IMPLEMENT session-aware middleware:
  - SETUP session freshness validation
  - CONFIGURE session expiration handling
  - IMPLEMENT session recovery mechanisms
  - SETUP multi-device session management
  - CONFIGURE session security monitoring

Task 7: Security Event Monitoring
IMPLEMENT comprehensive security monitoring:
  - SETUP security event logging and alerts
  - CONFIGURE suspicious activity detection
  - IMPLEMENT breach prevention measures
  - SETUP compliance monitoring and reporting
  - CONFIGURE automated security responses

Task 8: Performance Optimization
IMPLEMENT middleware performance features:
  - SETUP caching for authentication checks
  - CONFIGURE database query optimization
  - IMPLEMENT response time monitoring
  - SETUP memory usage optimization
  - CONFIGURE load balancing considerations

Task 9: Error Handling and Recovery
IMPLEMENT robust error handling:
  - SETUP graceful error recovery
  - CONFIGURE user-friendly error messages
  - IMPLEMENT fallback authentication methods
  - SETUP error logging and monitoring
  - CONFIGURE automated error response

Task 10: Testing and Validation
IMPLEMENT comprehensive testing:
  - SETUP unit tests for middleware functions
  - CONFIGURE integration tests for authentication flows
  - IMPLEMENT security testing for middleware
  - SETUP performance testing for middleware
  - CONFIGURE compliance testing for security
```

### Integration Points

```yaml
# Authentication Integration
- better-auth core authentication system
- Session management and validation
- Multi-factor authentication flows
- OAuth provider integration
- Password security and recovery

# Next.js Integration
- Next.js middleware system
- App Router authentication
- Server Component protection
- API Route authentication
- Client Component state management

# Security Integration
- CSRF protection and validation
- Rate limiting and throttling
- Security event monitoring
- Audit logging and compliance
- Threat detection and response

# Performance Integration
- Cookie caching optimization
- Database query optimization
- Response time monitoring
- Memory usage optimization
- Load balancing considerations

# Multi-Tenant Integration
- Tenant context injection
- Tenant-specific routing
- Tenant isolation enforcement
- Tenant-aware permissions
- Tenant analytics and monitoring
```

---

## Validation Gates

### Level 1: Basic Middleware Functionality
```bash
# Verify middleware configuration
npm run type-check
npm run test:unit -- --testNamePattern="middleware"

# Test middleware execution
curl -X GET http://localhost:3000/dashboard \
  -H "Cookie: session=invalid" \
  -w "%{http_code}"
```

### Level 2: Authentication Flows
```bash
# Test authentication redirection
npm run test:e2e -- --testNamePattern="auth-redirect"

# Test protected route access
npm run test:e2e -- --testNamePattern="protected-routes"

# Test session validation
npm run test:e2e -- --testNamePattern="session-validation"
```

### Level 3: Security Features
```bash
# Test CSRF protection
npm run test:e2e -- --testNamePattern="csrf"

# Test rate limiting
npm run test:e2e -- --testNamePattern="rate-limit"

# Test security headers
npm run test:e2e -- --testNamePattern="security-headers"
```

### Level 4: Multi-Tenant Support
```bash
# Test tenant isolation
npm run test:e2e -- --testNamePattern="tenant-isolation"

# Test tenant context injection
npm run test:e2e -- --testNamePattern="tenant-context"

# Test tenant-specific routing
npm run test:e2e -- --testNamePattern="tenant-routing"
```

### Level 5: Performance and Monitoring
```bash
# Test middleware performance
npm run test:perf -- --testNamePattern="middleware"

# Test audit logging
npm run test:e2e -- --testNamePattern="audit-logging"

# Test monitoring integration
npm run test:e2e -- --testNamePattern="monitoring"
```

---

## Quality Standards

The PRP must include:
- [x] Secure request validation and session verification
- [x] Tenant context injection and isolation
- [x] Role-based access control and permissions
- [x] Authentication flow protection and routing
- [x] Comprehensive audit logging and monitoring
- [x] Error handling and developer-friendly debugging
- [x] Multi-tenant middleware configuration
- [x] Performance optimization and caching
- [x] Enterprise security compliance
- [x] Comprehensive middleware testing
- [x] API authentication and authorization
- [x] Security event monitoring and alerting

---

## Expected Outcomes

Upon successful implementation:

1. **Security**: 99.9% unauthorized access prevention success rate
2. **Performance**: < 50ms middleware execution time
3. **Reliability**: 99.9% middleware availability and stability
4. **Scalability**: Support for 10,000+ concurrent requests
5. **Compliance**: Full enterprise security standard compliance
6. **Monitoring**: Comprehensive audit trails and security monitoring
7. **Multi-Tenant**: Complete tenant isolation with proper boundaries

---

**Framework**: NEXUS SaaS Starter Multi-Tenant Architecture  
**Technology Stack**: Next.js 15.4+ / better-auth / Security Middleware  
**Optimization**: Production-ready, enterprise-grade authentication middleware
