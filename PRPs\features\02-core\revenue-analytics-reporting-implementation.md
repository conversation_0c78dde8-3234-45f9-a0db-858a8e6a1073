# Revenue Analytics & Reporting Implementation

## Overview
Implement comprehensive revenue analytics and reporting system with interactive dashboards providing real-time insights into financial performance, subscription metrics, and business intelligence.

## Implementation Details

### Core Components

#### 1. Revenue Analytics Dashboard
```typescript
// src/components/analytics/RevenueDashboard.tsx
'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Download, TrendingUp, TrendingDown } from 'lucide-react';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';
import { cn } from '@/lib/utils';
import { ChartContainer, ChartTooltip, ChartLegend } from '@/components/ui/chart';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart,
  ComposedChart,
  ReferenceLine,
} from 'recharts';

interface RevenueMetrics {
  totalRevenue: number;
  monthlyRecurringRevenue: number;
  annualRecurringRevenue: number;
  averageRevenuePerUser: number;
  churnRate: number;
  growthRate: number;
  lifetimeValue: number;
  conversionRate: number;
}

interface RevenueData {
  date: string;
  revenue: number;
  subscriptions: number;
  churn: number;
  newCustomers: number;
  upgrades: number;
  downgrades: number;
  oneTimePayments: number;
  recurringRevenue: number;
}

interface DateRange {
  from: Date;
  to: Date;
}

const CHART_COLORS = {
  primary: '#3b82f6',
  secondary: '#8b5cf6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#06b6d4',
  gray: '#6b7280',
};

export default function RevenueDashboard() {
  const [metrics, setMetrics] = useState<RevenueMetrics | null>(null);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: startOfMonth(new Date()),
    to: endOfMonth(new Date()),
  });
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchRevenueAnalytics();
  }, [dateRange]);

  const fetchRevenueAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/analytics/revenue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          from: dateRange.from.toISOString(),
          to: dateRange.to.toISOString(),
        }),
      });
      
      if (!response.ok) throw new Error('Failed to fetch analytics');
      
      const data = await response.json();
      setMetrics(data.metrics);
      setRevenueData(data.revenueData);
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportData = async (format: 'csv' | 'pdf') => {
    try {
      const response = await fetch('/api/analytics/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          format,
          dateRange,
          type: 'revenue',
        }),
      });
      
      if (!response.ok) throw new Error('Export failed');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `revenue-analytics-${format}-${format(new Date(), 'yyyy-MM-dd')}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Export error:', error);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 2,
    }).format(value / 100);
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.name.includes('Rate') || entry.name.includes('Conversion') 
                ? formatPercentage(entry.value) 
                : formatCurrency(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Revenue Analytics</h1>
          <p className="text-gray-600">Comprehensive revenue insights and reporting</p>
        </div>
        <div className="flex items-center space-x-4">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[280px] justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, 'LLL dd, y')} -{' '}
                      {format(dateRange.to, 'LLL dd, y')}
                    </>
                  ) : (
                    format(dateRange.from, 'LLL dd, y')
                  )
                ) : (
                  <span>Pick a date range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange.from}
                selected={dateRange}
                onSelect={(range) => {
                  if (range?.from && range?.to) {
                    setDateRange({ from: range.from, to: range.to });
                  }
                }}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
          <Button onClick={() => exportData('csv')} variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
          <Button onClick={() => exportData('pdf')} variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics?.totalRevenue || 0)}</div>
            <p className="text-xs text-muted-foreground">
              <span className={cn(
                "inline-flex items-center",
                metrics?.growthRate && metrics.growthRate > 0 ? "text-green-600" : "text-red-600"
              )}>
                {metrics?.growthRate && metrics.growthRate > 0 ? (
                  <TrendingUp className="mr-1 h-3 w-3" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3" />
                )}
                {formatPercentage(metrics?.growthRate || 0)} from last period
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Recurring Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics?.monthlyRecurringRevenue || 0)}</div>
            <p className="text-xs text-muted-foreground">
              ARR: {formatCurrency(metrics?.annualRecurringRevenue || 0)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Revenue Per User</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics?.averageRevenuePerUser || 0)}</div>
            <p className="text-xs text-muted-foreground">
              LTV: {formatCurrency(metrics?.lifetimeValue || 0)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(metrics?.churnRate || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Conversion: {formatPercentage(metrics?.conversionRate || 0)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue Trends</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="cohort">Cohort Analysis</TabsTrigger>
          <TabsTrigger value="forecasting">Forecasting</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Overview</CardTitle>
                <CardDescription>Monthly revenue breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar dataKey="recurringRevenue" fill={CHART_COLORS.primary} name="Recurring Revenue" />
                    <Bar dataKey="oneTimePayments" fill={CHART_COLORS.secondary} name="One-time Payments" />
                    <Line type="monotone" dataKey="revenue" stroke={CHART_COLORS.success} name="Total Revenue" />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Customer Metrics</CardTitle>
                <CardDescription>New customers vs. churn</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Area type="monotone" dataKey="newCustomers" stackId="1" stroke={CHART_COLORS.success} fill={CHART_COLORS.success} name="New Customers" />
                    <Area type="monotone" dataKey="churn" stackId="2" stroke={CHART_COLORS.danger} fill={CHART_COLORS.danger} name="Churned Customers" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Trends</CardTitle>
              <CardDescription>Detailed revenue analysis over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line type="monotone" dataKey="revenue" stroke={CHART_COLORS.primary} strokeWidth={2} name="Total Revenue" />
                  <Line type="monotone" dataKey="recurringRevenue" stroke={CHART_COLORS.success} strokeWidth={2} name="Recurring Revenue" />
                  <Line type="monotone" dataKey="oneTimePayments" stroke={CHART_COLORS.secondary} strokeWidth={2} name="One-time Payments" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Subscription Changes</CardTitle>
                <CardDescription>Upgrades vs. downgrades</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar dataKey="upgrades" fill={CHART_COLORS.success} name="Upgrades" />
                    <Bar dataKey="downgrades" fill={CHART_COLORS.warning} name="Downgrades" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Subscription Distribution</CardTitle>
                <CardDescription>Current subscription breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Basic', value: 30, fill: CHART_COLORS.info },
                        { name: 'Professional', value: 50, fill: CHART_COLORS.primary },
                        { name: 'Enterprise', value: 20, fill: CHART_COLORS.secondary },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="cohort" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Cohort Analysis</CardTitle>
              <CardDescription>Customer retention by cohort</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-gray-500">Cohort analysis visualization would be implemented here</p>
                <p className="text-sm text-gray-400 mt-2">This would show customer retention rates by signup month</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="forecasting" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Forecasting</CardTitle>
              <CardDescription>Predicted revenue based on current trends</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Line type="monotone" dataKey="revenue" stroke={CHART_COLORS.primary} strokeWidth={2} name="Actual Revenue" />
                  <ReferenceLine y={metrics?.averageRevenuePerUser || 0} label="Average" stroke={CHART_COLORS.warning} strokeDasharray="5 5" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
```

#### 2. Revenue Analytics API
```typescript
// src/app/api/analytics/revenue/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

const analyticsSchema = z.object({
  from: z.string().datetime(),
  to: z.string().datetime(),
  tenantId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { from, to, tenantId } = analyticsSchema.parse(body);

    const supabase = createClient();

    // Get current user's tenant context
    const { data: userTenant } = await supabase
      .from('user_tenants')
      .select('tenant_id, role')
      .eq('user_id', session.user.id)
      .single();

    if (!userTenant) {
      return NextResponse.json({ error: 'No tenant access' }, { status: 403 });
    }

    const effectiveTenantId = tenantId || userTenant.tenant_id;

    // Verify user has analytics access
    if (!['admin', 'owner', 'finance'].includes(userTenant.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get revenue metrics
    const { data: revenueData, error: revenueError } = await supabase
      .rpc('get_revenue_analytics', {
        p_tenant_id: effectiveTenantId,
        p_from_date: from,
        p_to_date: to,
      });

    if (revenueError) {
      console.error('Revenue analytics error:', revenueError);
      return NextResponse.json({ error: 'Failed to fetch revenue data' }, { status: 500 });
    }

    // Get subscription metrics
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .rpc('get_subscription_analytics', {
        p_tenant_id: effectiveTenantId,
        p_from_date: from,
        p_to_date: to,
      });

    if (subscriptionError) {
      console.error('Subscription analytics error:', subscriptionError);
      return NextResponse.json({ error: 'Failed to fetch subscription data' }, { status: 500 });
    }

    // Calculate key metrics
    const metrics = {
      totalRevenue: revenueData?.total_revenue || 0,
      monthlyRecurringRevenue: revenueData?.mrr || 0,
      annualRecurringRevenue: revenueData?.arr || 0,
      averageRevenuePerUser: revenueData?.arpu || 0,
      churnRate: subscriptionData?.churn_rate || 0,
      growthRate: revenueData?.growth_rate || 0,
      lifetimeValue: revenueData?.ltv || 0,
      conversionRate: subscriptionData?.conversion_rate || 0,
    };

    // Format time series data
    const timeSeriesData = revenueData?.time_series || [];

    return NextResponse.json({
      metrics,
      revenueData: timeSeriesData,
      subscriptionData: subscriptionData?.subscription_breakdown || [],
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

#### 3. Revenue Analytics Database Functions
```sql
-- Database functions for revenue analytics
-- src/lib/supabase/migrations/20240101000000_revenue_analytics.sql

-- Function to get comprehensive revenue analytics
CREATE OR REPLACE FUNCTION get_revenue_analytics(
  p_tenant_id UUID,
  p_from_date TIMESTAMP WITH TIME ZONE,
  p_to_date TIMESTAMP WITH TIME ZONE
) RETURNS JSON AS $$
DECLARE
  result JSON;
  total_revenue DECIMAL;
  mrr DECIMAL;
  arr DECIMAL;
  arpu DECIMAL;
  growth_rate DECIMAL;
  ltv DECIMAL;
  time_series JSON;
BEGIN
  -- Calculate total revenue
  SELECT COALESCE(SUM(amount), 0) INTO total_revenue
  FROM payments p
  JOIN subscriptions s ON p.subscription_id = s.id
  WHERE s.tenant_id = p_tenant_id
    AND p.status = 'succeeded'
    AND p.created_at BETWEEN p_from_date AND p_to_date;

  -- Calculate MRR (Monthly Recurring Revenue)
  WITH monthly_revenue AS (
    SELECT 
      DATE_TRUNC('month', p.created_at) as month,
      SUM(CASE 
        WHEN sp.interval = 'month' THEN p.amount
        WHEN sp.interval = 'year' THEN p.amount / 12
        ELSE 0
      END) as recurring_revenue
    FROM payments p
    JOIN subscriptions s ON p.subscription_id = s.id
    JOIN subscription_plans sp ON s.plan_id = sp.id
    WHERE s.tenant_id = p_tenant_id
      AND p.status = 'succeeded'
      AND p.created_at BETWEEN p_from_date AND p_to_date
    GROUP BY DATE_TRUNC('month', p.created_at)
  )
  SELECT COALESCE(AVG(recurring_revenue), 0) INTO mrr FROM monthly_revenue;

  -- Calculate ARR (Annual Recurring Revenue)
  arr := mrr * 12;

  -- Calculate ARPU (Average Revenue Per User)
  WITH active_users AS (
    SELECT COUNT(DISTINCT s.user_id) as user_count
    FROM subscriptions s
    WHERE s.tenant_id = p_tenant_id
      AND s.status = 'active'
      AND s.created_at <= p_to_date
  )
  SELECT CASE 
    WHEN user_count > 0 THEN total_revenue / user_count 
    ELSE 0 
  END INTO arpu
  FROM active_users;

  -- Calculate growth rate (compared to previous period)
  WITH current_period AS (
    SELECT COALESCE(SUM(amount), 0) as current_revenue
    FROM payments p
    JOIN subscriptions s ON p.subscription_id = s.id
    WHERE s.tenant_id = p_tenant_id
      AND p.status = 'succeeded'
      AND p.created_at BETWEEN p_from_date AND p_to_date
  ),
  previous_period AS (
    SELECT COALESCE(SUM(amount), 0) as previous_revenue
    FROM payments p
    JOIN subscriptions s ON p.subscription_id = s.id
    WHERE s.tenant_id = p_tenant_id
      AND p.status = 'succeeded'
      AND p.created_at BETWEEN (p_from_date - (p_to_date - p_from_date)) AND p_from_date
  )
  SELECT CASE 
    WHEN pp.previous_revenue > 0 THEN ((cp.current_revenue - pp.previous_revenue) / pp.previous_revenue) * 100
    ELSE 0 
  END INTO growth_rate
  FROM current_period cp, previous_period pp;

  -- Calculate LTV (Customer Lifetime Value)
  WITH churn_rate AS (
    SELECT 
      CASE 
        WHEN COUNT(*) > 0 THEN 
          (COUNT(*) FILTER (WHERE status = 'cancelled') * 100.0 / COUNT(*))
        ELSE 0 
      END as churn_pct
    FROM subscriptions s
    WHERE s.tenant_id = p_tenant_id
      AND s.created_at BETWEEN p_from_date AND p_to_date
  )
  SELECT CASE 
    WHEN churn_pct > 0 THEN arpu / (churn_pct / 100)
    ELSE arpu * 12 -- Default to 12 months if no churn
  END INTO ltv
  FROM churn_rate;

  -- Generate time series data
  WITH RECURSIVE date_series AS (
    SELECT p_from_date::DATE as date
    UNION ALL
    SELECT date + INTERVAL '1 day'
    FROM date_series
    WHERE date < p_to_date::DATE
  ),
  daily_metrics AS (
    SELECT 
      ds.date,
      COALESCE(SUM(p.amount), 0) as revenue,
      COUNT(DISTINCT s.id) as subscriptions,
      COUNT(DISTINCT CASE WHEN s.status = 'cancelled' THEN s.id END) as churn,
      COUNT(DISTINCT CASE WHEN s.created_at::DATE = ds.date THEN s.id END) as new_customers,
      COUNT(DISTINCT CASE WHEN s.status = 'active' AND s.updated_at::DATE = ds.date THEN s.id END) as upgrades,
      COUNT(DISTINCT CASE WHEN s.status = 'downgraded' AND s.updated_at::DATE = ds.date THEN s.id END) as downgrades,
      COALESCE(SUM(CASE WHEN sp.interval IS NULL THEN p.amount ELSE 0 END), 0) as one_time_payments,
      COALESCE(SUM(CASE WHEN sp.interval IS NOT NULL THEN p.amount ELSE 0 END), 0) as recurring_revenue
    FROM date_series ds
    LEFT JOIN payments p ON p.created_at::DATE = ds.date
    LEFT JOIN subscriptions s ON p.subscription_id = s.id AND s.tenant_id = p_tenant_id
    LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
    GROUP BY ds.date
    ORDER BY ds.date
  )
  SELECT json_agg(
    json_build_object(
      'date', TO_CHAR(date, 'YYYY-MM-DD'),
      'revenue', revenue,
      'subscriptions', subscriptions,
      'churn', churn,
      'newCustomers', new_customers,
      'upgrades', upgrades,
      'downgrades', downgrades,
      'oneTimePayments', one_time_payments,
      'recurringRevenue', recurring_revenue
    )
  ) INTO time_series
  FROM daily_metrics;

  -- Build final result
  result := json_build_object(
    'total_revenue', total_revenue,
    'mrr', mrr,
    'arr', arr,
    'arpu', arpu,
    'growth_rate', growth_rate,
    'ltv', ltv,
    'time_series', time_series
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get subscription analytics
CREATE OR REPLACE FUNCTION get_subscription_analytics(
  p_tenant_id UUID,
  p_from_date TIMESTAMP WITH TIME ZONE,
  p_to_date TIMESTAMP WITH TIME ZONE
) RETURNS JSON AS $$
DECLARE
  result JSON;
  churn_rate DECIMAL;
  conversion_rate DECIMAL;
  subscription_breakdown JSON;
BEGIN
  -- Calculate churn rate
  WITH subscription_stats AS (
    SELECT 
      COUNT(*) as total_subscriptions,
      COUNT(*) FILTER (WHERE status = 'cancelled') as cancelled_subscriptions
    FROM subscriptions s
    WHERE s.tenant_id = p_tenant_id
      AND s.created_at BETWEEN p_from_date AND p_to_date
  )
  SELECT CASE 
    WHEN total_subscriptions > 0 THEN (cancelled_subscriptions * 100.0 / total_subscriptions)
    ELSE 0 
  END INTO churn_rate
  FROM subscription_stats;

  -- Calculate conversion rate (trials to paid)
  WITH conversion_stats AS (
    SELECT 
      COUNT(*) FILTER (WHERE status = 'trialing') as trials,
      COUNT(*) FILTER (WHERE status = 'active' AND previous_status = 'trialing') as conversions
    FROM subscriptions s
    WHERE s.tenant_id = p_tenant_id
      AND s.created_at BETWEEN p_from_date AND p_to_date
  )
  SELECT CASE 
    WHEN trials > 0 THEN (conversions * 100.0 / trials)
    ELSE 0 
  END INTO conversion_rate
  FROM conversion_stats;

  -- Get subscription breakdown by plan
  WITH plan_breakdown AS (
    SELECT 
      sp.name as plan_name,
      sp.interval,
      COUNT(s.id) as count,
      SUM(sp.amount) as total_revenue
    FROM subscriptions s
    JOIN subscription_plans sp ON s.plan_id = sp.id
    WHERE s.tenant_id = p_tenant_id
      AND s.status = 'active'
      AND s.created_at BETWEEN p_from_date AND p_to_date
    GROUP BY sp.name, sp.interval, sp.amount
  )
  SELECT json_agg(
    json_build_object(
      'plan', plan_name,
      'interval', interval,
      'count', count,
      'revenue', total_revenue
    )
  ) INTO subscription_breakdown
  FROM plan_breakdown;

  -- Build final result
  result := json_build_object(
    'churn_rate', churn_rate,
    'conversion_rate', conversion_rate,
    'subscription_breakdown', subscription_breakdown
  );

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_revenue_analytics(UUID, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) TO authenticated;
GRANT EXECUTE ON FUNCTION get_subscription_analytics(UUID, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) TO authenticated;
```

#### 4. Export Functionality
```typescript
// src/app/api/analytics/export/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';
import { Parser } from 'json2csv';
import PDFDocument from 'pdfkit';

const exportSchema = z.object({
  format: z.enum(['csv', 'pdf']),
  dateRange: z.object({
    from: z.string().datetime(),
    to: z.string().datetime(),
  }),
  type: z.enum(['revenue', 'subscriptions', 'customers']),
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { format, dateRange, type } = exportSchema.parse(body);

    const supabase = createClient();

    // Get user's tenant context
    const { data: userTenant } = await supabase
      .from('user_tenants')
      .select('tenant_id, role')
      .eq('user_id', session.user.id)
      .single();

    if (!userTenant || !['admin', 'owner', 'finance'].includes(userTenant.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Fetch data based on type
    let data;
    if (type === 'revenue') {
      const { data: revenueData } = await supabase
        .rpc('get_revenue_analytics', {
          p_tenant_id: userTenant.tenant_id,
          p_from_date: dateRange.from,
          p_to_date: dateRange.to,
        });
      data = revenueData?.time_series || [];
    }

    if (format === 'csv') {
      const parser = new Parser();
      const csv = parser.parse(data);
      
      return new NextResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="revenue-analytics-${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    }

    if (format === 'pdf') {
      const doc = new PDFDocument();
      const chunks: Buffer[] = [];

      doc.on('data', (chunk) => chunks.push(chunk));
      
      return new Promise((resolve) => {
        doc.on('end', () => {
          const result = Buffer.concat(chunks);
          resolve(new NextResponse(result, {
            headers: {
              'Content-Type': 'application/pdf',
              'Content-Disposition': `attachment; filename="revenue-analytics-${new Date().toISOString().split('T')[0]}.pdf"`,
            },
          }));
        });

        // Generate PDF content
        doc.fontSize(20).text('Revenue Analytics Report', 100, 100);
        doc.fontSize(12).text(`Date Range: ${dateRange.from} to ${dateRange.to}`, 100, 140);
        
        // Add data table
        let yPosition = 180;
        data.forEach((row: any, index: number) => {
          if (index === 0) {
            doc.text('Date', 100, yPosition);
            doc.text('Revenue', 200, yPosition);
            doc.text('Subscriptions', 300, yPosition);
            yPosition += 20;
          }
          doc.text(row.date, 100, yPosition);
          doc.text(`$${row.revenue}`, 200, yPosition);
          doc.text(row.subscriptions.toString(), 300, yPosition);
          yPosition += 15;
        });

        doc.end();
      });
    }

    return NextResponse.json({ error: 'Invalid format' }, { status: 400 });

  } catch (error) {
    console.error('Export error:', error);
    return NextResponse.json({ error: 'Export failed' }, { status: 500 });
  }
}
```

#### 5. Chart Components
```typescript
// src/components/ui/chart.tsx
'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

interface ChartContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  config?: Record<string, any>;
}

const ChartContainer = React.forwardRef<HTMLDivElement, ChartContainerProps>(
  ({ children, className, config, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('w-full h-full', className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);
ChartContainer.displayName = 'ChartContainer';

interface ChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
  className?: string;
}

const ChartTooltip = React.forwardRef<HTMLDivElement, ChartTooltipProps>(
  ({ active, payload, label, className, ...props }, ref) => {
    if (!active || !payload || payload.length === 0) {
      return null;
    }

    return (
      <div
        ref={ref}
        className={cn(
          'rounded-lg border bg-background p-2 shadow-sm',
          className
        )}
        {...props}
      >
        <div className="grid gap-2">
          {label && (
            <div className="font-medium text-foreground">{label}</div>
          )}
          {payload.map((entry: any, index: number) => (
            <div
              key={index}
              className="flex items-center gap-2 text-sm"
            >
              <div
                className="h-2 w-2 shrink-0 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-muted-foreground">
                {entry.name}:
              </span>
              <span className="font-medium text-foreground">
                {entry.value}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  }
);
ChartTooltip.displayName = 'ChartTooltip';

const ChartLegend = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('flex items-center justify-center gap-4', className)}
        {...props}
      />
    );
  }
);
ChartLegend.displayName = 'ChartLegend';

export { ChartContainer, ChartTooltip, ChartLegend };
```

### Integration Steps

1. **Install Dependencies**:
```bash
npm install recharts json2csv pdfkit @types/pdfkit date-fns
```

2. **Database Setup**:
```sql
-- Add analytics tracking to existing tables
ALTER TABLE payments ADD COLUMN IF NOT EXISTS analytics_processed BOOLEAN DEFAULT FALSE;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS previous_status TEXT;

-- Create analytics materialized views for performance
CREATE MATERIALIZED VIEW revenue_analytics_daily AS
SELECT 
  DATE_TRUNC('day', p.created_at) as date,
  s.tenant_id,
  SUM(p.amount) as total_revenue,
  COUNT(DISTINCT s.id) as active_subscriptions,
  COUNT(DISTINCT CASE WHEN s.status = 'cancelled' THEN s.id END) as churned_subscriptions
FROM payments p
JOIN subscriptions s ON p.subscription_id = s.id
WHERE p.status = 'succeeded'
GROUP BY DATE_TRUNC('day', p.created_at), s.tenant_id;

-- Create index for performance
CREATE INDEX idx_revenue_analytics_daily_tenant_date ON revenue_analytics_daily(tenant_id, date);
```

3. **Component Integration**:
```typescript
// src/app/dashboard/analytics/page.tsx
import { Metadata } from 'next';
import RevenueDashboard from '@/components/analytics/RevenueDashboard';

export const metadata: Metadata = {
  title: 'Revenue Analytics',
  description: 'Comprehensive revenue insights and reporting',
};

export default function AnalyticsPage() {
  return (
    <div className="container mx-auto py-6">
      <RevenueDashboard />
    </div>
  );
}
```

### Testing Strategy

1. **Unit Tests**:
```typescript
// src/components/analytics/__tests__/RevenueDashboard.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import RevenueDashboard from '../RevenueDashboard';

// Mock fetch
global.fetch = jest.fn();

describe('RevenueDashboard', () => {
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('renders revenue dashboard with loading state', () => {
    render(<RevenueDashboard />);
    expect(screen.getByText('Revenue Analytics')).toBeInTheDocument();
  });

  it('displays revenue metrics after loading', async () => {
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({
        metrics: {
          totalRevenue: 50000,
          monthlyRecurringRevenue: 15000,
          churnRate: 5,
        },
        revenueData: [],
      }),
    });

    render(<RevenueDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('$50,000')).toBeInTheDocument();
      expect(screen.getByText('$15,000')).toBeInTheDocument();
    });
  });

  it('handles export functionality', async () => {
    const mockBlob = new Blob(['test'], { type: 'text/csv' });
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      blob: async () => mockBlob,
    });

    render(<RevenueDashboard />);
    
    // Test export functionality
    const exportButton = screen.getByText('Export CSV');
    exportButton.click();
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/analytics/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: expect.stringContaining('csv'),
      });
    });
  });
});
```

2. **Integration Tests**:
```typescript
// src/app/api/analytics/__tests__/revenue.test.ts
import { describe, it, expect, beforeEach } from '@jest/globals';
import { createMocks } from 'node-mocks-http';
import { POST } from '../revenue/route';

describe('/api/analytics/revenue', () => {
  it('returns revenue analytics for authorized user', async () => {
    const { req } = createMocks({
      method: 'POST',
      body: {
        from: '2024-01-01T00:00:00Z',
        to: '2024-01-31T23:59:59Z',
      },
    });

    const response = await POST(req);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data).toHaveProperty('metrics');
    expect(data.metrics).toHaveProperty('totalRevenue');
  });

  it('returns 401 for unauthorized user', async () => {
    const { req } = createMocks({
      method: 'POST',
    });

    const response = await POST(req);
    expect(response.status).toBe(401);
  });
});
```

### Performance Optimizations

1. **Caching Strategy**:
```typescript
// src/lib/cache/analytics.ts
import { Redis } from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

export async function getCachedAnalytics(
  tenantId: string,
  dateRange: { from: string; to: string }
): Promise<any | null> {
  const key = `analytics:${tenantId}:${dateRange.from}:${dateRange.to}`;
  const cached = await redis.get(key);
  return cached ? JSON.parse(cached) : null;
}

export async function setCachedAnalytics(
  tenantId: string,
  dateRange: { from: string; to: string },
  data: any,
  ttl: number = 3600
): Promise<void> {
  const key = `analytics:${tenantId}:${dateRange.from}:${dateRange.to}`;
  await redis.setex(key, ttl, JSON.stringify(data));
}
```

2. **Database Optimization**:
```sql
-- Pre-computed analytics tables
CREATE TABLE revenue_analytics_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id),
  date_range DATERANGE NOT NULL,
  metrics JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Background job to update analytics cache
CREATE OR REPLACE FUNCTION update_analytics_cache() RETURNS VOID AS $$
BEGIN
  -- Update daily analytics for all tenants
  INSERT INTO revenue_analytics_cache (tenant_id, date_range, metrics)
  SELECT 
    t.id,
    daterange(CURRENT_DATE - INTERVAL '30 days', CURRENT_DATE),
    get_revenue_analytics(t.id, CURRENT_DATE - INTERVAL '30 days', CURRENT_DATE)
  FROM tenants t
  ON CONFLICT (tenant_id, date_range) DO UPDATE
  SET metrics = EXCLUDED.metrics, updated_at = NOW();
END;
$$ LANGUAGE plpgsql;
```

## Security Considerations

1. **Access Control**: Role-based access to analytics data
2. **Data Privacy**: Tenant isolation for all analytics queries
3. **Export Security**: Secure file generation and download
4. **Rate Limiting**: Prevent abuse of analytics endpoints
5. **Audit Logging**: Track all analytics access and exports

## Success Metrics

1. **Data Accuracy**: Revenue calculations match payment records
2. **Performance**: Dashboard loads within 2 seconds
3. **Usability**: Clear, actionable insights for business decisions
4. **Reliability**: 99.9% uptime for analytics endpoints
5. **Scalability**: Handles growth in data volume efficiently

This implementation provides a comprehensive revenue analytics and reporting system with interactive visualizations, real-time data, and robust export capabilities, enabling businesses to make data-driven decisions about their subscription revenue.
