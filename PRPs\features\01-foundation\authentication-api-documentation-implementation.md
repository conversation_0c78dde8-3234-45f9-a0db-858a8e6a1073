# NEXUS SaaS Starter - Authentication API Documentation Implementation

**PRP Name**: Authentication API Documentation  
**Version**: 1.0  
**Date**: July 18, 2025  
**Type**: Foundation Implementation PRP  
**Phase**: 01-foundation  
**Framework**: Next.js 15.4+ / better-auth / API Documentation  

---

## Purpose

Implement comprehensive authentication API documentation using better-auth with interactive documentation, endpoint references, type definitions, and developer-friendly guides for multi-tenant SaaS applications.

## Core Principles

1. **Developer First**: Clear, comprehensive, and interactive documentation
2. **API Consistency**: Standardized endpoint patterns and response formats
3. **Type Safety**: Complete TypeScript definitions and type inference
4. **Interactive Examples**: Working code samples and API testing
5. **Multi-Tenant Aware**: Tenant-specific API patterns and examples
6. **Production Ready**: Real-world examples and best practices

---

## Goal

Build a production-ready authentication API documentation system that provides comprehensive endpoint references, interactive examples, TypeScript definitions, and developer guides with testing capabilities and multi-tenant support.

## Why

- **Developer Experience**: Accelerates integration and reduces support overhead
- **API Consistency**: Ensures standardized usage patterns across teams
- **Type Safety**: Prevents runtime errors with complete type definitions
- **Self-Service**: Enables developers to integrate independently
- **Multi-Tenancy**: Provides tenant-aware API patterns and examples
- **Maintainability**: Reduces documentation drift and maintenance overhead

## What

A comprehensive authentication API documentation system with:
- Interactive API documentation with testing capabilities
- Complete endpoint references with request/response examples
- TypeScript definitions and type inference support
- Developer guides and integration tutorials
- Multi-tenant API patterns and examples
- Automated documentation generation and validation

### Success Criteria

- [ ] Interactive API documentation with testing capabilities
- [ ] Complete endpoint references with request/response examples
- [ ] TypeScript definitions and type inference support
- [ ] Developer guides and integration tutorials
- [ ] Multi-tenant API patterns and examples
- [ ] Automated documentation generation and validation
- [ ] OpenAPI specification generation
- [ ] Client SDK documentation and examples
- [ ] Error handling and troubleshooting guides
- [ ] Performance optimization recommendations

---

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- url: https://www.better-auth.com/docs/introduction/concepts/api
  why: Better-auth API concepts and server-side usage
  critical: Understanding API patterns and endpoint structures

- url: https://www.better-auth.com/docs/introduction/concepts/client
  why: Better-auth client concepts and client-side usage
  critical: Understanding client SDK patterns and usage

- url: https://www.better-auth.com/docs/introduction/plugins/open-api
  why: Better-auth OpenAPI plugin for documentation generation
  critical: Automated API documentation generation

- url: https://www.better-auth.com/docs/introduction/concepts/plugins
  why: Better-auth plugin system for custom endpoints
  critical: Custom endpoint creation and documentation

- url: https://www.better-auth.com/docs/introduction/concepts/typescript
  why: TypeScript support and type inference
  critical: Type safety and developer experience

- url: https://swagger.io/specification/
  why: OpenAPI specification for API documentation
  critical: Standard API documentation format

- url: https://redocly.com/docs/
  why: ReDoc for interactive API documentation
  critical: Interactive documentation rendering

- url: https://github.com/scalar/scalar
  why: Scalar for modern API documentation
  critical: Modern API documentation experience

- url: https://typedoc.org/
  why: TypeDoc for TypeScript documentation
  critical: TypeScript API documentation generation

- url: https://nextjs.org/docs/pages/building-your-application/routing/api-routes
  why: Next.js API routes for documentation endpoints
  critical: API route patterns and documentation serving
```

### Current Technology Stack

```yaml
# API Documentation
- better-auth: Latest (comprehensive API framework)
- OpenAPI Plugin: Automated specification generation
- Scalar: Modern interactive documentation
- TypeDoc: TypeScript documentation generation
- Next.js API Routes: Documentation serving

# Documentation Tools
- Swagger/OpenAPI: API specification standard
- ReDoc: Interactive API documentation
- Scalar: Modern documentation experience
- TypeDoc: TypeScript documentation
- MDX: Documentation with interactive examples

# Type Safety
- TypeScript: Complete type definitions
- Type Inference: Automatic type generation
- Schema Validation: Request/response validation
- Error Types: Comprehensive error handling
- Plugin Types: Custom endpoint typing

# Integration Support
- Client SDKs: Multi-platform client libraries
- Code Examples: Working integration samples
- Testing Tools: API testing and validation
- Developer Guides: Step-by-step tutorials
- Multi-Tenant: Tenant-aware documentation
```

### Known Gotchas & Library Quirks

```typescript
// CRITICAL: Documentation generation gotchas
// OpenAPI generation: Must be enabled in better-auth configuration
// Schema validation: Type definitions must match runtime behavior
// Plugin documentation: Custom endpoints need proper typing
// Client generation: SDK documentation must stay in sync
// Version management: Documentation versioning with API changes
// Type inference: Complex types may not infer correctly
// Multi-tenant: Tenant-specific patterns need clear examples
// Error handling: Error types must be comprehensive
// Performance: Documentation generation can be resource intensive
// Cache invalidation: Documentation cache must be properly managed

// CRITICAL: Interactive documentation gotchas
// CORS issues: Documentation UI may have CORS problems
// Authentication: Testing requires proper auth setup
// Rate limiting: Documentation testing may hit rate limits
// Environment: Different environments need different configs
// SSL/TLS: HTTPS requirements for authentication testing
// Cookies: Session handling in documentation interface
// WebSocket: Real-time features may not work in docs
// File uploads: Binary data handling in documentation
// Pagination: Large result sets need proper handling
// Tenant isolation: Multi-tenant testing requires isolation

// CRITICAL: TypeScript documentation gotchas
// Generic types: Complex generics may not render well
// Conditional types: Type conditions may be confusing
// Utility types: Built-in utility types need explanation
// Module resolution: Import paths must be correct
// Type exports: Public API types must be properly exported
// Breaking changes: Type changes need version management
// Inheritance: Complex inheritance chains need clarity
// Overloads: Function overloads need clear documentation
// Decorators: Decorator usage needs examples
// Namespace: Module organization affects documentation
```

---

## Implementation Blueprint

### Core API Documentation System

```typescript
// lib/api-documentation.ts
import { betterAuth } from "better-auth";
import { openAPI } from "better-auth/plugins";
import { auth } from "@/lib/auth";

// Enhanced OpenAPI configuration
export const apiDocumentation = betterAuth({
  ...auth,
  plugins: [
    ...auth.plugins,
    openAPI({
      // Custom documentation path
      path: "/api/auth/docs",
      
      // Disable default Scalar UI (we'll use custom)
      disableDefaultReference: true,
      
      // Custom OpenAPI specification
      openAPISpec: {
        info: {
          title: "NEXUS SaaS Starter Authentication API",
          version: "1.0.0",
          description: `
# NEXUS SaaS Starter Authentication API

A comprehensive authentication API built with better-auth for multi-tenant SaaS applications.

## Features

- 🔐 **Complete Authentication**: Email/password, OAuth, MFA, magic links
- 🏢 **Multi-Tenant**: Tenant-aware authentication and session management
- 🔑 **Session Management**: Secure session handling with automatic expiration
- 👥 **Role-Based Access**: Granular permissions and role management
- 📊 **Analytics**: Comprehensive audit logging and monitoring
- 🚀 **Performance**: Optimized for high-scale applications

## Getting Started

1. **Authentication Flow**: Start with email/password or OAuth sign-in
2. **Session Management**: Handle session validation and refresh
3. **Multi-Tenant**: Implement tenant context and isolation
4. **Role-Based Access**: Configure permissions and roles
5. **Security**: Implement security best practices

## Support

- 📖 **Documentation**: Complete API reference and guides
- 💬 **Community**: Join our developer community
- 🐛 **Issues**: Report bugs and request features
- 📧 **Support**: Enterprise support available

## Multi-Tenant Architecture

This API is designed for multi-tenant SaaS applications:

- **Tenant Isolation**: Complete data isolation between tenants
- **Tenant Context**: Automatic tenant context injection
- **Tenant Policies**: Tenant-specific authentication policies
- **Tenant Analytics**: Tenant-aware monitoring and analytics
          `,
          contact: {
            name: "NEXUS SaaS Support",
            email: "<EMAIL>",
            url: "https://nexus-saas.com/support"
          },
          license: {
            name: "MIT",
            url: "https://opensource.org/licenses/MIT"
          }
        },
        servers: [
          {
            url: "https://api.nexus-saas.com",
            description: "Production API"
          },
          {
            url: "https://staging-api.nexus-saas.com",
            description: "Staging API"
          },
          {
            url: "http://localhost:3000",
            description: "Development API"
          }
        ],
        security: [
          {
            bearerAuth: []
          },
          {
            cookieAuth: []
          }
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: "http",
              scheme: "bearer",
              bearerFormat: "JWT",
              description: "Bearer token authentication"
            },
            cookieAuth: {
              type: "apiKey",
              in: "cookie",
              name: "session",
              description: "Session cookie authentication"
            }
          },
          schemas: {
            User: {
              type: "object",
              properties: {
                id: {
                  type: "string",
                  description: "Unique user identifier"
                },
                email: {
                  type: "string",
                  format: "email",
                  description: "User email address"
                },
                name: {
                  type: "string",
                  description: "User display name"
                },
                image: {
                  type: "string",
                  format: "uri",
                  description: "User avatar image URL"
                },
                emailVerified: {
                  type: "boolean",
                  description: "Email verification status"
                },
                createdAt: {
                  type: "string",
                  format: "date-time",
                  description: "User creation timestamp"
                },
                updatedAt: {
                  type: "string",
                  format: "date-time",
                  description: "User last update timestamp"
                },
                tenantId: {
                  type: "string",
                  description: "Associated tenant identifier"
                },
                roles: {
                  type: "array",
                  items: {
                    $ref: "#/components/schemas/Role"
                  },
                  description: "User roles and permissions"
                }
              },
              required: ["id", "email", "name", "createdAt", "updatedAt", "tenantId"]
            },
            Session: {
              type: "object",
              properties: {
                id: {
                  type: "string",
                  description: "Unique session identifier"
                },
                userId: {
                  type: "string",
                  description: "Associated user identifier"
                },
                tenantId: {
                  type: "string",
                  description: "Associated tenant identifier"
                },
                expiresAt: {
                  type: "string",
                  format: "date-time",
                  description: "Session expiration timestamp"
                },
                createdAt: {
                  type: "string",
                  format: "date-time",
                  description: "Session creation timestamp"
                },
                updatedAt: {
                  type: "string",
                  format: "date-time",
                  description: "Session last update timestamp"
                },
                ipAddress: {
                  type: "string",
                  description: "Session IP address"
                },
                userAgent: {
                  type: "string",
                  description: "Session user agent"
                },
                deviceId: {
                  type: "string",
                  description: "Device identifier"
                },
                deviceType: {
                  type: "string",
                  description: "Device type (mobile, desktop, tablet)"
                }
              },
              required: ["id", "userId", "tenantId", "expiresAt", "createdAt"]
            },
            Role: {
              type: "object",
              properties: {
                id: {
                  type: "string",
                  description: "Unique role identifier"
                },
                name: {
                  type: "string",
                  description: "Role name"
                },
                description: {
                  type: "string",
                  description: "Role description"
                },
                permissions: {
                  type: "array",
                  items: {
                    type: "string"
                  },
                  description: "Role permissions"
                },
                tenantId: {
                  type: "string",
                  description: "Associated tenant identifier"
                }
              },
              required: ["id", "name", "permissions", "tenantId"]
            },
            Tenant: {
              type: "object",
              properties: {
                id: {
                  type: "string",
                  description: "Unique tenant identifier"
                },
                name: {
                  type: "string",
                  description: "Tenant name"
                },
                domain: {
                  type: "string",
                  description: "Tenant domain"
                },
                plan: {
                  type: "string",
                  enum: ["free", "starter", "professional", "enterprise"],
                  description: "Tenant subscription plan"
                },
                settings: {
                  type: "object",
                  description: "Tenant-specific settings"
                },
                createdAt: {
                  type: "string",
                  format: "date-time",
                  description: "Tenant creation timestamp"
                },
                updatedAt: {
                  type: "string",
                  format: "date-time",
                  description: "Tenant last update timestamp"
                }
              },
              required: ["id", "name", "domain", "plan", "createdAt"]
            },
            ErrorResponse: {
              type: "object",
              properties: {
                error: {
                  type: "string",
                  description: "Error message"
                },
                code: {
                  type: "string",
                  description: "Error code"
                },
                details: {
                  type: "object",
                  description: "Additional error details"
                },
                timestamp: {
                  type: "string",
                  format: "date-time",
                  description: "Error timestamp"
                }
              },
              required: ["error", "code", "timestamp"]
            }
          }
        },
        tags: [
          {
            name: "Authentication",
            description: "User authentication endpoints"
          },
          {
            name: "Session",
            description: "Session management endpoints"
          },
          {
            name: "User",
            description: "User management endpoints"
          },
          {
            name: "OAuth",
            description: "OAuth provider endpoints"
          },
          {
            name: "MFA",
            description: "Multi-factor authentication endpoints"
          },
          {
            name: "Tenant",
            description: "Multi-tenant management endpoints"
          },
          {
            name: "Admin",
            description: "Administrative endpoints"
          }
        ]
      }
    })
  ]
});

// Documentation utilities
export const documentationUtils = {
  // Generate endpoint documentation
  generateEndpointDocs: (endpoint: any) => {
    return {
      summary: endpoint.summary || "API endpoint",
      description: endpoint.description || "API endpoint description",
      operationId: endpoint.operationId || endpoint.path.replace(/[^a-zA-Z0-9]/g, '_'),
      tags: endpoint.tags || [],
      parameters: endpoint.parameters || [],
      requestBody: endpoint.requestBody,
      responses: endpoint.responses || {
        200: {
          description: "Success",
          content: {
            "application/json": {
              schema: {
                type: "object"
              }
            }
          }
        },
        400: {
          description: "Bad Request",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/ErrorResponse"
              }
            }
          }
        },
        401: {
          description: "Unauthorized",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/ErrorResponse"
              }
            }
          }
        },
        500: {
          description: "Internal Server Error",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/ErrorResponse"
              }
            }
          }
        }
      },
      security: endpoint.security || []
    };
  },

  // Generate client SDK documentation
  generateClientSDKDocs: () => {
    return {
      typescript: {
        installation: "npm install @nexus-saas/auth-client",
        usage: `
import { createAuthClient } from "@nexus-saas/auth-client";

const authClient = createAuthClient({
  baseURL: "https://api.nexus-saas.com",
  tenantId: "your-tenant-id"
});

// Sign in with email/password
const { data, error } = await authClient.signIn.email({
  email: "<EMAIL>",
  password: "secure-password"
});

// Get current session
const session = await authClient.getSession();
        `,
        examples: {
          signIn: `
await authClient.signIn.email({
  email: "<EMAIL>",
  password: "secure-password",
  tenantId: "tenant-123"
});
          `,
          signUp: `
await authClient.signUp.email({
  email: "<EMAIL>",
  password: "secure-password",
  name: "John Doe",
  tenantId: "tenant-123"
});
          `,
          oauth: `
await authClient.signIn.social({
  provider: "google",
  callbackURL: "/dashboard"
});
          `,
          mfa: `
// Enable TOTP
await authClient.twoFactor.enable({
  password: "current-password"
});

// Verify TOTP
await authClient.twoFactor.verifyTotp({
  code: "123456"
});
          `,
          session: `
// Get session
const session = await authClient.getSession();

// List sessions
const sessions = await authClient.listSessions();

// Revoke session
await authClient.revokeSession({ 
  token: "session-token" 
});
          `
        }
      },
      javascript: {
        installation: "npm install @nexus-saas/auth-client",
        usage: `
const { createAuthClient } = require("@nexus-saas/auth-client");

const authClient = createAuthClient({
  baseURL: "https://api.nexus-saas.com",
  tenantId: "your-tenant-id"
});
        `
      },
      python: {
        installation: "pip install nexus-saas-auth",
        usage: `
from nexus_saas_auth import AuthClient

auth_client = AuthClient(
    base_url="https://api.nexus-saas.com",
    tenant_id="your-tenant-id"
)

# Sign in
result = auth_client.sign_in_email(
    email="<EMAIL>",
    password="secure-password"
)
        `
      }
    };
  },

  // Generate integration guides
  generateIntegrationGuides: () => {
    return {
      quickStart: `
# Quick Start Guide

## 1. Installation

\`\`\`bash
npm install @nexus-saas/auth-client
\`\`\`

## 2. Configuration

\`\`\`typescript
import { createAuthClient } from "@nexus-saas/auth-client";

const authClient = createAuthClient({
  baseURL: "https://api.nexus-saas.com",
  tenantId: "your-tenant-id"
});
\`\`\`

## 3. Authentication

\`\`\`typescript
// Sign in
const { data, error } = await authClient.signIn.email({
  email: "<EMAIL>",
  password: "secure-password"
});

if (error) {
  console.error("Sign in failed:", error);
} else {
  console.log("Signed in successfully:", data);
}
\`\`\`

## 4. Session Management

\`\`\`typescript
// Get current session
const session = await authClient.getSession();

// Check if user is authenticated
if (session) {
  console.log("User is authenticated:", session.user);
} else {
  console.log("User is not authenticated");
}
\`\`\`
      `,
      multiTenant: `
# Multi-Tenant Integration

## Tenant Context

All API requests must include tenant context:

\`\`\`typescript
const authClient = createAuthClient({
  baseURL: "https://api.nexus-saas.com",
  tenantId: "your-tenant-id"
});
\`\`\`

## Tenant-Specific Authentication

\`\`\`typescript
// Sign in with tenant context
await authClient.signIn.email({
  email: "<EMAIL>",
  password: "secure-password",
  tenantId: "tenant-123"
});
\`\`\`

## Tenant Isolation

- All user data is isolated by tenant
- Sessions are tenant-specific
- Permissions are tenant-scoped
- Analytics are tenant-aware
      `,
      security: `
# Security Best Practices

## 1. HTTPS Only

Always use HTTPS in production:

\`\`\`typescript
const authClient = createAuthClient({
  baseURL: "https://api.nexus-saas.com" // HTTPS only
});
\`\`\`

## 2. Secure Session Storage

Store sessions securely:

\`\`\`typescript
// Use httpOnly cookies for web apps
// Use secure storage for mobile apps
\`\`\`

## 3. Multi-Factor Authentication

Enable MFA for enhanced security:

\`\`\`typescript
await authClient.twoFactor.enable({
  password: "current-password"
});
\`\`\`

## 4. Rate Limiting

Implement rate limiting:

\`\`\`typescript
// Built-in rate limiting
// Custom rate limiting per tenant
\`\`\`
      `
    };
  },

  // Generate error handling guide
  generateErrorHandlingGuide: () => {
    return {
      commonErrors: {
        "INVALID_CREDENTIALS": {
          code: 401,
          description: "Invalid email or password",
          solution: "Verify credentials and try again"
        },
        "USER_NOT_FOUND": {
          code: 404,
          description: "User does not exist",
          solution: "Create account or verify email"
        },
        "EMAIL_NOT_VERIFIED": {
          code: 403,
          description: "Email address not verified",
          solution: "Verify email before signing in"
        },
        "MFA_REQUIRED": {
          code: 403,
          description: "Multi-factor authentication required",
          solution: "Complete MFA verification"
        },
        "TENANT_NOT_FOUND": {
          code: 404,
          description: "Tenant does not exist",
          solution: "Verify tenant ID and configuration"
        },
        "RATE_LIMIT_EXCEEDED": {
          code: 429,
          description: "Too many requests",
          solution: "Wait before retrying"
        }
      },
      errorHandling: `
\`\`\`typescript
try {
  const { data, error } = await authClient.signIn.email({
    email: "<EMAIL>",
    password: "secure-password"
  });

  if (error) {
    switch (error.code) {
      case "INVALID_CREDENTIALS":
        // Handle invalid credentials
        break;
      case "EMAIL_NOT_VERIFIED":
        // Handle unverified email
        break;
      case "MFA_REQUIRED":
        // Handle MFA requirement
        break;
      default:
        // Handle other errors
        break;
    }
  }
} catch (error) {
  // Handle network or other errors
  console.error("Authentication error:", error);
}
\`\`\`
      `
    };
  }
};
```

### Interactive Documentation Interface

```typescript
// app/api/auth/docs/route.ts
import { NextRequest, NextResponse } from "next/server";
import { apiDocumentation } from "@/lib/api-documentation";

export async function GET(request: NextRequest) {
  try {
    // Get OpenAPI specification
    const openAPISpec = await apiDocumentation.getOpenAPISpec();
    
    // Generate interactive documentation HTML
    const html = `
<!DOCTYPE html>
<html>
<head>
  <title>NEXUS SaaS Authentication API</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <style>
    body { margin: 0; padding: 0; }
    .api-reference { height: 100vh; }
  </style>
</head>
<body>
  <div id="api-reference" class="api-reference"></div>
  <script type="module">
    import { ApiReference } from 'https://cdn.jsdelivr.net/npm/@scalar/api-reference@latest/dist/browser/standalone.js'
    
    const apiReference = new ApiReference(document.getElementById('api-reference'), {
      spec: {
        url: '${request.nextUrl.origin}/api/auth/docs/openapi.json'
      },
      configuration: {
        theme: 'kepler',
        darkMode: true,
        layout: 'modern',
        showSidebar: true,
        authentication: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer'
            },
            cookieAuth: {
              type: 'apiKey',
              in: 'cookie',
              name: 'session'
            }
          }
        },
        servers: [
          {
            url: '${request.nextUrl.origin}',
            description: 'Current Environment'
          }
        ]
      }
    })
  </script>
</body>
</html>
    `;

    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'public, max-age=3600'
      }
    });
  } catch (error) {
    console.error('Documentation generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate documentation' },
      { status: 500 }
    );
  }
}
```

### OpenAPI Specification Generator

```typescript
// app/api/auth/docs/openapi.json/route.ts
import { NextRequest, NextResponse } from "next/server";
import { apiDocumentation, documentationUtils } from "@/lib/api-documentation";

export async function GET(request: NextRequest) {
  try {
    // Generate OpenAPI specification
    const openAPISpec = {
      openapi: "3.0.0",
      info: {
        title: "NEXUS SaaS Authentication API",
        version: "1.0.0",
        description: "Comprehensive authentication API for multi-tenant SaaS applications"
      },
      servers: [
        {
          url: request.nextUrl.origin,
          description: "Current Environment"
        }
      ],
      paths: {
        "/api/auth/sign-in/email": {
          post: {
            ...documentationUtils.generateEndpointDocs({
              summary: "Sign in with email and password",
              description: "Authenticate user with email and password credentials",
              tags: ["Authentication"],
              requestBody: {
                required: true,
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        email: {
                          type: "string",
                          format: "email",
                          description: "User email address"
                        },
                        password: {
                          type: "string",
                          minLength: 8,
                          description: "User password"
                        },
                        tenantId: {
                          type: "string",
                          description: "Tenant identifier"
                        },
                        rememberMe: {
                          type: "boolean",
                          default: true,
                          description: "Remember user session"
                        }
                      },
                      required: ["email", "password", "tenantId"]
                    }
                  }
                }
              },
              responses: {
                200: {
                  description: "Sign in successful",
                  content: {
                    "application/json": {
                      schema: {
                        type: "object",
                        properties: {
                          user: {
                            $ref: "#/components/schemas/User"
                          },
                          session: {
                            $ref: "#/components/schemas/Session"
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          }
        },
        "/api/auth/sign-up/email": {
          post: {
            ...documentationUtils.generateEndpointDocs({
              summary: "Sign up with email and password",
              description: "Create new user account with email and password",
              tags: ["Authentication"],
              requestBody: {
                required: true,
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        email: {
                          type: "string",
                          format: "email",
                          description: "User email address"
                        },
                        password: {
                          type: "string",
                          minLength: 8,
                          description: "User password"
                        },
                        name: {
                          type: "string",
                          description: "User display name"
                        },
                        tenantId: {
                          type: "string",
                          description: "Tenant identifier"
                        }
                      },
                      required: ["email", "password", "name", "tenantId"]
                    }
                  }
                }
              }
            })
          }
        },
        "/api/auth/session": {
          get: {
            ...documentationUtils.generateEndpointDocs({
              summary: "Get current session",
              description: "Retrieve current user session information",
              tags: ["Session"],
              security: [{ cookieAuth: [] }, { bearerAuth: [] }],
              responses: {
                200: {
                  description: "Session information",
                  content: {
                    "application/json": {
                      schema: {
                        type: "object",
                        properties: {
                          user: {
                            $ref: "#/components/schemas/User"
                          },
                          session: {
                            $ref: "#/components/schemas/Session"
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          }
        },
        "/api/auth/sessions": {
          get: {
            ...documentationUtils.generateEndpointDocs({
              summary: "List user sessions",
              description: "Get all active sessions for current user",
              tags: ["Session"],
              security: [{ cookieAuth: [] }, { bearerAuth: [] }],
              responses: {
                200: {
                  description: "List of user sessions",
                  content: {
                    "application/json": {
                      schema: {
                        type: "array",
                        items: {
                          $ref: "#/components/schemas/Session"
                        }
                      }
                    }
                  }
                }
              }
            })
          }
        },
        "/api/auth/sign-out": {
          post: {
            ...documentationUtils.generateEndpointDocs({
              summary: "Sign out",
              description: "End current user session",
              tags: ["Authentication"],
              security: [{ cookieAuth: [] }, { bearerAuth: [] }],
              responses: {
                200: {
                  description: "Sign out successful",
                  content: {
                    "application/json": {
                      schema: {
                        type: "object",
                        properties: {
                          success: {
                            type: "boolean",
                            example: true
                          }
                        }
                      }
                    }
                  }
                }
              }
            })
          }
        }
      },
      components: {
        securitySchemes: {
          bearerAuth: {
            type: "http",
            scheme: "bearer",
            bearerFormat: "JWT"
          },
          cookieAuth: {
            type: "apiKey",
            in: "cookie",
            name: "session"
          }
        },
        schemas: {
          User: {
            type: "object",
            properties: {
              id: { type: "string" },
              email: { type: "string", format: "email" },
              name: { type: "string" },
              image: { type: "string", format: "uri" },
              emailVerified: { type: "boolean" },
              tenantId: { type: "string" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" }
            }
          },
          Session: {
            type: "object",
            properties: {
              id: { type: "string" },
              userId: { type: "string" },
              tenantId: { type: "string" },
              expiresAt: { type: "string", format: "date-time" },
              createdAt: { type: "string", format: "date-time" },
              ipAddress: { type: "string" },
              userAgent: { type: "string" },
              deviceId: { type: "string" },
              deviceType: { type: "string" }
            }
          },
          ErrorResponse: {
            type: "object",
            properties: {
              error: { type: "string" },
              code: { type: "string" },
              details: { type: "object" },
              timestamp: { type: "string", format: "date-time" }
            }
          }
        }
      }
    };

    return NextResponse.json(openAPISpec, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600'
      }
    });
  } catch (error) {
    console.error('OpenAPI generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate OpenAPI specification' },
      { status: 500 }
    );
  }
}
```

### Task Breakdown

```yaml
Task 1: Core Documentation Infrastructure
SETUP documentation generation system:
  - CONFIGURE OpenAPI plugin with better-auth
  - SETUP interactive documentation interface
  - CONFIGURE documentation serving endpoints
  - SETUP documentation caching and optimization
  - INTEGRATE documentation with development workflow

Task 2: API Endpoint Documentation
IMPLEMENT comprehensive endpoint documentation:
  - SETUP endpoint reference generation
  - CONFIGURE request/response schema documentation
  - IMPLEMENT interactive API testing
  - SETUP authentication documentation
  - CONFIGURE error handling documentation

Task 3: TypeScript Integration
IMPLEMENT TypeScript documentation:
  - SETUP type definition generation
  - CONFIGURE type inference documentation
  - IMPLEMENT client SDK type documentation
  - SETUP TypeScript examples and guides
  - CONFIGURE type validation and testing

Task 4: Client SDK Documentation
IMPLEMENT client SDK documentation:
  - SETUP multi-platform SDK documentation
  - CONFIGURE usage examples and guides
  - IMPLEMENT integration tutorials
  - SETUP code samples and snippets
  - CONFIGURE SDK testing and validation

Task 5: Multi-Tenant Documentation
IMPLEMENT tenant-aware documentation:
  - SETUP tenant-specific API patterns
  - CONFIGURE tenant isolation examples
  - IMPLEMENT tenant context documentation
  - SETUP tenant-aware authentication flows
  - CONFIGURE tenant-specific error handling

Task 6: Interactive Features
IMPLEMENT interactive documentation features:
  - SETUP API testing interface
  - CONFIGURE authentication testing
  - IMPLEMENT request/response examples
  - SETUP environment configuration
  - CONFIGURE rate limiting and throttling

Task 7: Developer Guides
IMPLEMENT comprehensive developer guides:
  - SETUP quick start guides
  - CONFIGURE integration tutorials
  - IMPLEMENT best practices documentation
  - SETUP troubleshooting guides
  - CONFIGURE performance optimization guides

Task 8: Documentation Generation
IMPLEMENT automated documentation generation:
  - SETUP continuous documentation updates
  - CONFIGURE version management
  - IMPLEMENT documentation validation
  - SETUP documentation testing
  - CONFIGURE documentation deployment

Task 9: Error Handling Documentation
IMPLEMENT error handling documentation:
  - SETUP comprehensive error catalog
  - CONFIGURE error code documentation
  - IMPLEMENT error handling examples
  - SETUP troubleshooting guides
  - CONFIGURE error recovery patterns

Task 10: Performance and Monitoring
IMPLEMENT documentation performance features:
  - SETUP documentation caching
  - CONFIGURE search and navigation
  - IMPLEMENT documentation analytics
  - SETUP user feedback collection
  - CONFIGURE documentation monitoring
```

### Integration Points

```yaml
# Authentication Integration
- better-auth core authentication system
- OpenAPI plugin for specification generation
- Interactive documentation interface
- Client SDK integration
- Type definition generation

# Development Integration
- Next.js API routes for documentation serving
- TypeScript for type safety
- MDX for documentation content
- Continuous integration for documentation updates
- Version control for documentation versions

# User Experience Integration
- Interactive API testing interface
- Code examples and snippets
- Multi-platform SDK documentation
- Developer guides and tutorials
- Error handling and troubleshooting

# Performance Integration
- Documentation caching and optimization
- Search and navigation features
- Analytics and user feedback
- Mobile-responsive documentation
- Accessibility compliance

# Multi-Tenant Integration
- Tenant-aware API patterns
- Tenant-specific examples
- Tenant context documentation
- Tenant isolation patterns
- Tenant-aware error handling
```

---

## Validation Gates

### Level 1: Basic Documentation Generation
```bash
# Verify documentation generation
npm run type-check
npm run test:unit -- --testNamePattern="documentation"

# Test OpenAPI generation
curl -X GET http://localhost:3000/api/auth/docs/openapi.json
```

### Level 2: Interactive Documentation
```bash
# Test interactive documentation
npm run test:e2e -- --testNamePattern="documentation"

# Test API testing interface
npm run test:e2e -- --testNamePattern="api-testing"

# Test authentication in documentation
npm run test:e2e -- --testNamePattern="auth-testing"
```

### Level 3: TypeScript Integration
```bash
# Test type generation
npm run test:unit -- --testNamePattern="types"

# Test client SDK documentation
npm run test:e2e -- --testNamePattern="sdk-docs"

# Test type inference
npm run test:unit -- --testNamePattern="inference"
```

### Level 4: Multi-Tenant Documentation
```bash
# Test tenant-aware documentation
npm run test:e2e -- --testNamePattern="tenant-docs"

# Test tenant-specific examples
npm run test:e2e -- --testNamePattern="tenant-examples"

# Test tenant context patterns
npm run test:e2e -- --testNamePattern="tenant-context"
```

### Level 5: Performance and Accessibility
```bash
# Test documentation performance
npm run test:perf -- --testNamePattern="documentation"

# Test documentation accessibility
npm run test:a11y -- --testNamePattern="documentation"

# Test documentation search
npm run test:e2e -- --testNamePattern="search"
```

---

## Quality Standards

The PRP must include:
- [x] Interactive API documentation with testing capabilities
- [x] Complete endpoint references with request/response examples
- [x] TypeScript definitions and type inference support
- [x] Developer guides and integration tutorials
- [x] Multi-tenant API patterns and examples
- [x] Automated documentation generation and validation
- [x] OpenAPI specification generation
- [x] Client SDK documentation and examples
- [x] Error handling and troubleshooting guides
- [x] Performance optimization recommendations
- [x] Accessibility compliance
- [x] Mobile-responsive documentation

---

## Expected Outcomes

Upon successful implementation:

1. **Developer Experience**: 90% reduction in integration time
2. **API Adoption**: 95% self-service integration success rate
3. **Documentation Quality**: 99% accuracy and completeness
4. **Performance**: < 2s documentation load time
5. **Accessibility**: WCAG 2.1 AA compliance
6. **Multi-Tenant**: Complete tenant-aware documentation
7. **Maintenance**: 80% reduction in documentation maintenance

---

**Framework**: NEXUS SaaS Starter Multi-Tenant Architecture  
**Technology Stack**: Next.js 15.4+ / better-auth / API Documentation  
**Optimization**: Production-ready, enterprise-grade API documentation
